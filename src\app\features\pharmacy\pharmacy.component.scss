*{
    font-family: Roboto,sans-serif;
}



.container{
    display: block;
    justify-content: center;
    background-color: #e5eff7;
    padding: 17px;
    border-radius: 10px;
    width: 80%;
}
.secondcontainer{
    display: block;
    justify-content: center;
    background-color: #e5eff7;
    padding: 17px;
    border-radius: 10px;
    width: 80%;
    margin-top: 20px;
    margin-left: auto;
    margin-right: auto;
}

.main{
    color: var(--primaryColor);
    width: 80%;
    margin-left: auto;
    margin-right: auto;
    text-align: left;
    margin-top: 30px;
    margin-bottom: 20px;
}

.tablets{
    background-color: #fff;
    margin-bottom: 10px;
    padding: 25px;
    border-radius: 10px;
}

.head{
    font-weight: 500;
    font-size: 1.6rem;
}

.miniHead{
    font-weight: 400;
    font-size: 1rem;
}

.raisedBtn{
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.disBtn{
    padding: 5px 17px;
    font-weight: 500;
    font-size: 1.1rem;
    border-radius: 8px;
}

.mat-expansion-panel{
    box-shadow: none;
}

.mat-expansion-panel-header{
    padding: 0 24px 0 0;
}

.mat-expansion-panel:hover{
    background-color: #fff;
}

.secondBox{
    margin-bottom: 20px;
}

.select{
    width: 75%;
    padding: 21px 10px 18px 10px;
    /* min-height: 27%; */
    border-radius: 10px;
}

.option{
    padding: 21px 10px 18px 10px;
}