.container{
    // width: 80%;
    // margin-left: auto;
    // margin-right: auto;
    text-align: left;
    display: flex;
    justify-content: center;
    margin-top: 30px;
    margin-bottom: 20px;
}
.main{
    // width: 80%;
    display: flex;
    justify-content: center;
}

.back{
    color: #629a35;
    text-decoration: underline;
    font-size: 19px;
    font-weight: bold;
}

.content{
    font-size: 35px;
    font-weight: 600;
    color: var(--primaryColor);
}

.content-button{
    width: 140px;
    height: 50px;
    border-radius: 10px;
    font-size: 1.125rem;
    font-weight: 500;
    font-family: var(--fontStyle);
    background-color: var(--primaryColor);
    color: #fff;
}

.modal-foot{
    display: flex;
    flex-wrap: wrap;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-start;
    padding: 0 0.75rem 0.75rem 0.75rem;
    border-bottom-right-radius: calc(0.3rem - 1px);
    border-bottom-left-radius: calc(0.3rem - 1px);
}

@media (max-width:1200px){
    .col-5{
        width: 100%;
    }
    .col-7{
        width: 100%;
    }
}

::ng-deep .modal-header{
    border:none
}

@media print {
    body * {
      visibility: hidden;
    }
    #hide-for-print{
        visibility: hidden;
    }
    #elementToCapture, #elementToCapture * {
      visibility: visible;
    }
    #elementToCapture {
      position: absolute;
      left: 0;
      top: 0;
    }
    #elementToCapture .content{
        font-size: 30px;
        padding-bottom: 30px;
    }
  }

  .modal-dialog{
    display: flex;
    justify-content: center;
}

.modal-content{
 width: 70%;
}

.modal-contents{
 width: 100%;
}

.modal-footer{
    justify-content: center;
    padding: 10px;
    border: none;
}

.msg_button{
    padding: 10px 20px;
    //font-size: 22px;
    font-size: 1.375rem;
    border-radius: 7px;
}

#dynamicMail{
    resize: both;
    // overflow: auto;
    word-wrap: break-word;
}