
*{
    margin: 0;
    padding: 0;
}



.content-left{
    font-family: var(--fontStyle);
    margin-top: 10px;
    margin: 50px 10px 20px;
}

.content-left1{
    font-family: var(--fontStyle);
}

.main-text{
    color: var(--primaryColor);
    font-family: var(--fontStyle);
    font-size: 2.375rem;
    font-weight: 600;
    line-height: 50px;
    letter-spacing: 0px;
}

.sub-text{
    font-family: var(--fontStyle);
    margin-top: 30px;
    font-size: 1.125rem;
    font-weight: 400;
    line-height: 1.875rem;
    color: #282828;
}

mat-panel-title h6{
    font-size: 1.5rem;
    font-weight: 500;
    font-family: var(--fontStyle);
    color: #333333;
}

mat-panel-title h6:hover{
    color: var(--primaryColor);
}

mat-icon {
    color: var(--brandYellow);
    font-weight: 800;
}

.mat-expansion-panel{
    box-shadow:none;
}

.example-form {
    min-width: 150px;
    max-width: 500px;
    width: 100%;
  }
  
  .example-full-width {
    width: 100%;
  }

.para-text{
    border: 1px solid #e9e5e5;
    padding: 15px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 1.185rem;
    line-height: 1.875;
    font-family: var(--fontStyle);
}

.qsa-form{
    padding: 2px 40px 10px 40px;
    box-shadow: -6px -6px 15px -1px #ebe9e9, 6px 6px 15px -1px #ebe9e9;
    width: 74%;
    // height: 109%;
    border-radius: 11px;
    margin: 70px 0px 5px 89px;
    position: relative;
    top: -6rem;
    left: 3rem;
}

// .img-fluid{
//     // position: relative;
//     // left: 9rem;
//     transform: rotate(-90deg);
//     /* Firefox */
//     -moz-transform:rotate(-90deg);
//     /* Safari and Chrome */
//     -webkit-transform:rotate(-90deg);
//     /* Opera */
//     -o-transform:rotate(-90deg);
//     /* IE9 */
//     -ms-transform:rotate(-90deg);
//     /* IE6,IE7 */

// }

#medicine{
    color: var(--primaryColor);
    font-family: var(--fontStyle);
    padding: 17px 0px 23px 0px;
    font-size: 1.5rem;
    font-weight: 500;
}

#medicines{
    padding: 10px 10px 10px 10px;
}

.medicines{
    padding: 0 0 25px 0;
}

.form-check{
    padding: 0px 0px 5px 29px;
}

.form-button{
    display: flex;
    justify-content: center;
}

.button{
    padding: 13px;
    font-size: 1.125rem;
    border-radius: 9px;
    margin-top: 25px;
    border: none;
    color: var(--primaryColor);
    font-family: var(--fontStyle);
    font-weight: 500;
    background-color: var(--brandYellow);
}

.button:hover{
    color: #fff;
    background-color: var(--primaryColor);
}

a{
    color: var(--primaryColor);   
    cursor: pointer; 
    text-decoration: none;
}

a:hover{
    color: var(--secondaryColor);
    font-weight: bold;
    cursor: pointer; 
}

:host::ng-deep a{
    color: var(--primaryColor);   
    cursor: pointer; 
    text-decoration: none;
}

:host::ng-deep a:hover{
    color: var(--secondaryColor);
    font-weight: bold;
    cursor: pointer; 
}

.captchaclass{
    text-align: center;
}
.captchadiv{
    display: inline-block;
}


@media (max-width:768px){

    .content-left{
        margin: 10px 10px;
    }
    .col-6{
        width: 100%;
    }

    mat-panel-title{
        font-size: 16px;
    }

    .qsa-form{
        padding: 0 40px 10px 40px;
        width: 100%;
        margin: 0px 0 30px 0;
        position: relative;
        top: 0;
        left: 0;
    }

    // .img-fluid{
    //     position: relative;
    //     left: 36px;
    //     width: 100%;
    // }

    .for768px{
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }

    .main-text{
        font-size: 40px;
    }

    // mat-expansion-panel{
    //     height: 55%;
    // }

    .mat-expansion-panel-header{
        margin-bottom: 30px;
        height: auto !important;
    }

    #captchaElem{
        padding: 0px 0px 0px 0px ;
    }

}

@media (min-width:1196px){
    .for768px{
        position: relative;
        left: 5rem;
        margin-top: 2%;
    }
}

@media (min-width:768px) and (max-width:1198px) {
    .content-left{
        width: 100%;
        
    }

    .for768px{
        width: 100%;
        // position: relative;
        /* left: -5rem; */
        // top: -5rem;
        display: flex;
        justify-content: center;
    }

    .content-left1{
        width: 100%;
        margin-bottom:30px;
    }

    .qsa-form{
        margin: 0;
        position: relative;
        top: 1rem;
        left: 0;
        width: 100%;
    }
}

@media (max-width:1200px){
    .container{
        max-width: calc(100vw - 6vw) !important;
        width: 100% !important;
    }
}

@media (min-width:1200px) {
    .container{
      max-width: 1170px;
    }
  }

@media (max-width:992px){
    .container{
        max-width: calc(100% - 6vw)
    }
}

@media (max-width:768px){
    .container{
        max-width: calc(100% - 6vw)
    }
}

@media (max-width:576px){
    .container{
        max-width: calc(100% - 6vw)
    }
}

@media (max-width:480px) {
    #captcha_custom{
        transform: scale(0.77);
    }
}

@media (max-width:280px) {
    #captcha_custom{
        transform: scale(0.60);
    }
}

#chatbot{
    top: calc(100vh - 26%) ;
    left: calc(100vw - 15%) ;
}


.form-button button:disabled,
.form-button button[disabled]{
    opacity: 50 !important;
}

.invalid-feedback{
    font-size: 12px;
    color: red;
}

@supports (-webkit-overflow-scrolling: touch) {
    /* CSS specific to iOS devices */
    input, textarea {
        transform: translateZ(0px) !important;
    }
}