<div *ngIf="offsetFlag" class="contain" id="borderline">
  <div class="containers row">
    <!-- <div class="offset-1 col-10" style="padding: 0;" [innerHtml]="_returnHtmlFromRichText(shortStickyIsi)">
    </div> -->

    <div class="text-end pe-3">
      <a (click)="scrollToElementById('contentbox')">See More
        <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
          <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
        </svg>
      </a>
    </div>
    <div class="bottom-isi" style="padding: 15px;padding-bottom: 0px;">
      <h4 *ngIf="indication_text">{{indication_header}}</h4>
      <div [innerHtml]="_returnHtmlFromRichText(indication_text)" *ngIf="indication_text"></div>
      <h4 *ngIf="isi_text">{{isi_header}}</h4>
      <div [innerHtml]="_returnHtmlFromRichText(isi_text)" *ngIf="isi_text"></div>
    </div>
  </div>
</div>

 