<div class="container" id="top">
  <div>
    <mat-accordion>
      <mat-expansion-panel (opened)="panelOpenState = true"
                           (closed)="panelOpenState = false" 
                           expanded="true" 
                           hideToggle >
        <mat-expansion-panel-header>
          <svg xmlns="http://www.w3.org/2000/svg" *ngIf="!panelOpenState" width="25" height="25" fill="currentColor" class="bi bi-arrow-down-circle-fill" viewBox="0 0 16 16">
            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.5 4.5a.5.5 0 0 0-1 0v5.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V4.5z"/>
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" *ngIf="panelOpenState" width="25" height="25" fill="currentColor" class="bi bi-arrow-up-circle-fill" viewBox="0 0 16 16">
            <path d="M16 8A8 8 0 1 0 0 8a8 8 0 0 0 16 0zm-7.5 3.5a.5.5 0 0 1-1 0V5.707L5.354 7.854a.5.5 0 1 1-.708-.708l3-3a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 5.707V11.5z"/>
          </svg>
          <mat-panel-title style="color: var(--fontColor);">
            <h4>How to Get Started</h4>
            <i class="bi bi-arrow-down-circle-fill"></i>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <table>
          <tbody>
            <tr>
              <td>
                <img src="../../../assets/images/contents/Playicon.png" width="28px" height="28px" alt="">
              </td>
              <td>
                <p class="para">
                  {{firstList}}
                  <a href={{firstListUrl}}>{{firstListUrlValue}}</a>{{firstListValue}}
                </p>
              </td>
            </tr>
            <tr>
              <td class="copyicon">
                <img src="../../../assets/images/contents/CopyIcon.png" width="28px" height="28px" alt="">
              </td>
              <td>
                <p class="para">
                  <a href={{secondListUrl}} (click)="getTabUrl()">{{secondList}}</a> {{secondListValue}}
                  <a href={{secondListValue2Url}} data-bs-toggle="modal" data-bs-target="#exampleModal1" data-bs-backdrop="true" >{{secondListValue2}}</a>
                  <br>{{secondListValue2Sl}} <a href={{secondListValue2TlUrl}}>{{secondListValue2Tl}}</a> 
                  {{secondListValue2Fl}}
                </p>
              </td>
            </tr>
            <tr>
              <td>
                <img src="../../../assets/images/contents/ViewIcon.png" width="28px" height="28px" alt="">
              </td>
              <td>
                <p class="para">
                 {{thirdList}}
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
      <div class="main-content">
        <div class="row top-content">
          <div class="col-md-6 HizentraLogo">
            <img src={{hizenLogo}} width="300px" height="150px" alt="HizentraLogo">
          </div>
          <div class="col-md-6 pdf-info">
            <a href="https://labeling.cslbehring.com/PI/US/Hizentra/EN/Hizentra-Prescribing-Information.pdf" target="_blank" rel="noopener">Prescribing Information</a>
            &nbsp;
            <h5 style="position: relative;top: 3px;">|</h5>
            &nbsp;
            <a href (click)="scrollToElementById('contentbox')" >Important Safety Information</a>
          </div>
        </div>
        <!-- <div>
          <button mat-raised-button (click)="openBottomSheet()">Open file</button>
        </div> -->
        
        <div>
          <button type="button" class="brand-msg">{{dynamicMsg}}</button>
        </div>
        <!-- <div>
          <table class="table tool-row"  style="margin-top: 30px;">
            <tr>
              <section class="example-section">
                <p>select tools</p>
              </section>
            </tr>
            <tr class="second-tr" style="margin: 0px 160px 0px 160px ;">
              <img src="../../../assets/images/contents/ViewIcon.png" class="viewicon" width="28px" height="28px" alt="">
              <label for="viewicon">Preview tool</label>
            </tr>
            <tr>
              <label for=""><mat-icon id="arrow">keyboard_arrow_down</mat-icon>Description</label>
            </tr>
          </table>
        </div> -->
        <div class="row" id="demo">
          <div class="col-2">
            <!-- <p><mat-icon>check_box_outline_blank</mat-icon>Select Tool(s)</p> -->
            <p> <img src="../../../assets/images/checkbox outline.png" width="20px" height="20px" alt=""> Select Tool(s)</p>
          </div>
          <div class="col-2">
            <p> <img src="../../../assets/images/contents/ViewIcon.png" width="25px" alt=""> Preview Tool</p>
          </div>
          <div class="col-2">
            <!-- <p><mat-icon style="font-size:20px;">keyboard_arrow_down</mat-icon>Description</p> -->
            <p> <img src="../../../assets/images/downarrow.webp"style="rotate:90deg; width: 20px;"  alt=""> Description</p>
          </div>
        </div>
        <div class="contentbox" style="text-overflow: ellipsis;">
          <mat-accordion *ngFor="let groupResource of resourceData; let i = index">
            {{groupResource.name}}
            <section class="example-section row">
              <mat-checkbox color="primary" #managing (click)="mailTitle(resource.fields.healthResourceTitle)"
              (click)="mailDescription(resource.fields.teaser.content[0].content[0].value)"  (click)="mailPdf(resource.fields.healthResourceUrl)" class="example-margin example-section" *ngFor="let resource of groupResource.values">
              <mat-expansion-panel (opened)="panelOpenState3 = true" 
                                     (closed)="panelOpenState3 = false"
                                     hideToggle                                    
                                     class="col">                 
                  <mat-expansion-panel-header>
                    <div class="alignRight">
                      <a href="{{resource.fields.healthResourceUrl}}" target="_blank" rel="noopener"><img class="viewIconImg"  src="../../../assets/images/contents/ViewIcon.png" alt=""></a>                 
                      <mat-icon id="arrow" *ngIf="!panelOpenState3">keyboard_arrow_down</mat-icon>
                      <mat-icon id="arrow" *ngIf="panelOpenState3">keyboard_arrow_up</mat-icon>
                    </div>
                    <mat-panel-title>
                      {{resource.fields.healthResourceTitle}}                   
                    </mat-panel-title>
                  </mat-expansion-panel-header >
                  <p class="para-text">{{resource.fields.teaser.content[0].content[0].value}}.</p>
                </mat-expansion-panel>
            </mat-checkbox>            
              </section>
          </mat-accordion>
        </div>



          <!--Loop End-->
          <!-- <h4>PROVIDER</h4> -->
          <!-- <mat-accordion *ngFor="let resource of resourceContents">
            <section class="example-section row">
              <mat-checkbox color="primary" #managing  
                                                      (click)="mailTitle(resource.fields.healthResourceTitle)"
                                                      (click)="mailDescription(resource.fields.teaser.content[0].content[0].value)" 
                                                      (click)="mailPdf(resource.fields.healthResourceUrl)"                                                   
              class="example-margin example-section">
                <mat-expansion-panel (opened)="panelOpenState = true" 
                                     (closed)="panelOpenState = false"
                                     hideToggle                                    
                                     class="col">                 
                  <mat-expansion-panel-header>
                    <div class="alignRight">
                      <a href="{{resource.fields.healthResourceUrl}}" target="_blank" rel="noopener"><img class="viewIconImg"  src="../../../assets/images/contents/ViewIcon.png" alt=""></a>                 
                      <mat-icon id="arrow" *ngIf="!resource.fields.healthResourceTitle">keyboard_arrow_down</mat-icon>
                      <mat-icon id="arrow" *ngIf="resource.fields.healthResourceTitle">keyboard_arrow_up</mat-icon>
                    </div>
                    <mat-panel-title>
                      {{resource.fields.healthResourceTitle}}                   
                    </mat-panel-title>
                  </mat-expansion-panel-header >
                  <p class="para-text">{{resource.fields.teaser.content[0].content[0].value}}.</p>
                </mat-expansion-panel>
              </mat-checkbox>
            </section>
          </mat-accordion> -->

          <!-- <mat-accordion *ngFor="let resource of resourceContents; let i = index">
            <section class="example-section row" *ngIf="i < 2">
              <mat-checkbox color="primary" #managing  
                                                      (click)="mailTitle(resource.fields.healthResourceTitle)"
                                                      (click)="mailDescription(resource.fields.teaser.content[0].content[0].value)" 
                                                      (click)="mailPdf(resource.fields.healthResourceUrl)"                                                   
              class="example-margin example-section">
                <mat-expansion-panel (opened)="panelOpenState3 = true" 
                                     (closed)="panelOpenState3 = false"
                                     hideToggle                                    
                                     class="col">                 
                  <mat-expansion-panel-header>
                    <div class="alignRight">
                      <a href="{{resource.fields.healthResourceUrl}}" target="_blank" rel="noopener"><img class="viewIconImg"  src="../../../assets/images/contents/ViewIcon.png" alt=""></a>                 
                      <mat-icon id="arrow" *ngIf="!panelOpenState3">keyboard_arrow_down</mat-icon>
                      <mat-icon id="arrow" *ngIf="panelOpenState3">keyboard_arrow_up</mat-icon>
                    </div>
                    <mat-panel-title>
                      {{resource.fields.healthResourceTitle}}                   
                    </mat-panel-title>
                  </mat-expansion-panel-header >
                  <p class="para-text">{{resource.fields.teaser.content[0].content[0].value}}.</p>
                </mat-expansion-panel>
              </mat-checkbox>
            </section>
          </mat-accordion>

          <h4 class="fillable">FILLABLE FORMS</h4>

          <mat-accordion *ngFor="let resource of resourceContents | slice:2; let i = index;">
            <section class="example-section row" *ngIf="i < 2">
              <mat-checkbox color="primary" #managing  
                                                      (click)="mailTitle(resource.fields.healthResourceTitle)"
                                                      (click)="mailDescription(resource.fields.teaser.content[0].content[0].value)" 
                                                      (click)="mailPdf(resource.fields.healthResourceUrl)"                                                   
              class="example-margin example-section">
                <mat-expansion-panel (opened)="panelOpenState1 = true" 
                                     (closed)="panelOpenState1 = false"
                                     hideToggle                                    
                                     class="col">                 
                  <mat-expansion-panel-header>
                    <div class="alignRight">
                      <a href="{{resource.fields.healthResourceUrl}}" target="_blank" rel="noopener"><img class="viewIconImg"  src="../../../assets/images/contents/ViewIcon.png" alt=""></a>                 
                      <mat-icon id="arrow" *ngIf="!panelOpenState1">keyboard_arrow_down</mat-icon>
                      <mat-icon id="arrow" *ngIf="panelOpenState1">keyboard_arrow_up</mat-icon>
                    </div>
                    <mat-panel-title>
                      {{resource.fields.healthResourceTitle}}                   
                    </mat-panel-title>
                  </mat-expansion-panel-header >
                  <p class="para-text">{{resource.fields.teaser.content[0].content[0].value}}.</p>
                </mat-expansion-panel>
              </mat-checkbox>
            </section>
          </mat-accordion> -->


          <!-- <pre>{{this.result | json}}</pre> -->
          <!-- <section class="example-section">
            <mat-checkbox color="primary" class="example-margin example-section" >Optimizing the Transition to Hizentra for CIDP Patients</mat-checkbox>
          </section>
          <section class="example-section">
            <mat-checkbox color="primary" class="example-margin" >Managing Common Infusion Issues</mat-checkbox>
          </section>
          <h4 style="color:#691c32 ;position:relative;left:1.5rem; margin: 20px 0px 20px 0;">FILLABLE FORMS</h4>
         <div class="d-flex">
          <section class="example-section">
            <mat-checkbox color="primary" class="example-margin" >
              Hizentra Connect℠ Resource Center—­Benefits <br> Investigation Request Prescription Referral Form</mat-checkbox>
          </section>
         </div>
          <section class="example-section">
            <mat-checkbox color="primary" class="example-margin" >Free Trial Program Request Form</mat-checkbox>
          </section> -->
          
          <!-- <mat-accordion>
              <section class="example-section">
                <div class="row">
                  <mat-checkbox  color="primary" #optimizing (change)="check(optimizing.value)" value="https://assets.ctfassets.net/h2axairjfqha/3DPnzJpofvwh7Ll50Pb6Ub/afad9fc302a35ea200778b8e7e1d0edb/Optimizing_the_Transition_to_Hizentra_for_CIDP_Patients.pdf" class="example-margin example-section col-1" ></mat-checkbox>
                  <mat-expansion-panel (opened)="panelOpenState1 = true"
                                       (closed)="panelOpenState1 = false"
                                       hideToggle
                                       class="col">                 
                    <mat-expansion-panel-header>
                      <mat-icon id="arrow" *ngIf="!panelOpenState1">keyboard_arrow_down</mat-icon>
                      <mat-icon id="arrow" *ngIf="panelOpenState1">keyboard_arrow_up</mat-icon>
                      <mat-panel-title>
                        {{dropdown1}}
                        <div id="viewIcon">
                        <a href="//assets.ctfassets.net/h2axairjfqha/3DPnzJpofvwh7Ll50Pb6Ub/afad9fc302a35ea200778b8e7e1d0edb/Optimizing_the_Transition_to_Hizentra_for_CIDP_Patients.pdf" target="_blank" rel="noopener"><img src="../../../assets/images/contents/ViewIcon.png" width="35px" alt=""></a>                 
                        </div>
                      </mat-panel-title>
                    </mat-expansion-panel-header >
                    <p class="para-text">{{dropdown1Des}}</p>
                  </mat-expansion-panel>
                </div>
              </section>
             </mat-accordion>
             <mat-accordion>
              <section class="example-section row">
                <mat-checkbox color="primary" #managing (change)="check(managing.value)" value="https://assets.ctfassets.net/h2axairjfqha/41uG0xeahLR6RmP80tyZAA/b8f6a2193772032e30e7c4dee6539433/Managing_Common_Infusion_Issues.pdf" class="example-margin example-section col-1" ></mat-checkbox>
                  <mat-expansion-panel (opened)="panelOpenState2 = true"
                                       (closed)="panelOpenState2 = false"
                                       hideToggle
                                       class="col">                 
                    <mat-expansion-panel-header>
                      <mat-icon id="arrow" *ngIf="!panelOpenState2">keyboard_arrow_down</mat-icon>
                      <mat-icon id="arrow" *ngIf="panelOpenState2">keyboard_arrow_up</mat-icon>
                      <mat-panel-title>
                        {{dropdown2}}
                        <div id="viewIcon2">
                          <a href="//assets.ctfassets.net/h2axairjfqha/41uG0xeahLR6RmP80tyZAA/b8f6a2193772032e30e7c4dee6539433/Managing_Common_Infusion_Issues.pdf" target="_blank" rel="noopener"><img  src="../../../assets/images/contents/ViewIcon.png" width="35px" alt=""></a>                 
                          </div>
                      </mat-panel-title>
                    </mat-expansion-panel-header >
                    <p class="para-text">{{dropdown2Des}}.</p>
                  </mat-expansion-panel>
              </section>
            </mat-accordion>
            
              <h4 style="color:var(--secondaryColor) ;position:relative;left:1.5rem; margin: 20px 0px 20px 0;">FILLABLE FORMS</h4>
            <mat-accordion>   
              <section class="example-section row">
                <mat-checkbox color="primary" #referral (change)="check(referral.value)" value="https://assets.ctfassets.net/h2axairjfqha/1JDxPKqyzXBAys6k5TseuH/a5620cb16ef437f5494da8965e6369f3/Is_Self-infused_Ig_Right_for_Your_CIDP.pdf" class="example-margin example-section col-1" ></mat-checkbox>
                  <mat-expansion-panel (opened)="panelOpenState3 = true"
                                       (closed)="panelOpenState3 = false"
                                       hideToggle
                                       class="col">                 
                    <mat-expansion-panel-header>
                      <mat-icon id="arrow" *ngIf="!panelOpenState3">keyboard_arrow_down</mat-icon>
                      <mat-icon id="arrow" *ngIf="panelOpenState3">keyboard_arrow_up</mat-icon>
                      <mat-panel-title>
                        Hizentra Connect℠ Resource Center—­Benefits <br>
                        Investigation Request Prescription Referral Form
                        <div id="viewIcon3">
                          <a href="https://labeling.cslbehring.com/PI/US/Hizentra/EN/Hizentra-Prescribing-Information.pdf" target="_blank" rel="noopener"><img src="../../../assets/images/contents/ViewIcon.png" width="35px" alt=""></a>                 
                          </div>
                      </mat-panel-title>
                    </mat-expansion-panel-header >
                    <p class="para-text"> Provides a dosing calculator to initiate a patient’s Hizentra therapy and to adjust the duration of infusion based on patient need</p>
                  </mat-expansion-panel>
              </section>
            </mat-accordion>
          <mat-accordion>
              <section class="example-section row">
                <mat-checkbox color="primary" #freeTrail (change)="check(freeTrail.value)" value="https://assets.ctfassets.net/h2axairjfqha/3A0uVvYZrnWT7KruYvaC1J/f86d9027894941f4b1f57f628e0a384c/Free_Trial_Program_Request_Form.pdf" class="example-margin example-section col-1"></mat-checkbox>
                  <mat-expansion-panel (opened)="panelOpenState4 = true"
                                       (closed)="panelOpenState4 = false"
                                       hideToggle
                                       class="col">                 
                    <mat-expansion-panel-header>
                      <mat-icon id="arrow" *ngIf="!panelOpenState4">keyboard_arrow_down</mat-icon>
                      <mat-icon id="arrow" *ngIf="panelOpenState4">keyboard_arrow_up</mat-icon>
                      <mat-panel-title>
                        {{dropdown4}}
                        <div id="viewIcon4">
                          <a href="//assets.ctfassets.net/h2axairjfqha/3A0uVvYZrnWT7KruYvaC1J/f86d9027894941f4b1f57f628e0a384c/Free_Trial_Program_Request_Form.pdf" target="_blank" rel="noopener"><img src="../../../assets/images/contents/ViewIcon.png" width="35px" alt=""></a>                 
                          </div>
                      </mat-panel-title>
                    </mat-expansion-panel-header >
                    <p class="para-text">{{dropdown4Des}}</p>
                  </mat-expansion-panel>
              </section>

            <section class="example-section" >
                <mat-checkbox color="primary" #freeTrail (change)="check(freeTrail.value)" value="http://assets.ctfassets.net/h2axairjfqha/3A0uVvYZrnWT7KruYvaC1J/f86d9027894941f4b1f57f628e0a384c/Free_Trial_Program_Request_Form.pdf" class="example-margin example-section"></mat-checkbox>
                <mat-expansion-panel (opened)="panelOpenState4 = true"
                                     (closed)="panelOpenState4 = false"
                                     hideToggle>                 
                  <mat-expansion-panel-header>
                    <mat-icon id="arrow" *ngIf="!panelOpenState4">keyboard_arrow_down</mat-icon>
                    <mat-icon id="arrow" *ngIf="panelOpenState4">keyboard_arrow_up</mat-icon>
                    <mat-panel-title>
                      {{dropdown4}}
                      <div id="viewIcon4">
                        <a href="//assets.ctfassets.net/h2axairjfqha/3A0uVvYZrnWT7KruYvaC1J/f86d9027894941f4b1f57f628e0a384c/Free_Trial_Program_Request_Form.pdf" target="_blank" rel="noopener"><img src="../../../assets/images/contents/ViewIcon.png" width="35px" alt=""></a>                 
                      </div>
                    </mat-panel-title>
                  </mat-expansion-panel-header>
                  <p class="para-text">{{dropdown4Des}}</p>
                </mat-expansion-panel>             
            </section> -->
          <!-- </mat-accordion>      -->
        </div>

        <div class="contentbox2" id="paraContent">
       <p class="paraContentText">
        {{paraContent}}
       </p>
        </div>
<!-- 
        <div class="contentbox" style="margin-top:20px;">
          <h4>PATIENT</h4>
          <mat-accordion *ngFor="let resource of resourceContents | slice:4; let i = index">
            <section class="example-section row" *ngIf="i < 3">
              <mat-checkbox color="primary" #managing  
                                                      (click)="mailTitle(resource.fields.healthResourceTitle)"
                                                      (click)="mailDescription(resource.fields.teaser.content[0].content[0].value)" 
                                                      (click)="mailPdf(resource.fields.healthResourceUrl)"                                                   
              class="example-margin example-section">
                <mat-expansion-panel (opened)="panelOpenState2 = true" 
                                     (closed)="panelOpenState2 = false"
                                     hideToggle                                    
                                     class="col">                 
                  <mat-expansion-panel-header>
                    <div class="alignRight">
                      <a href="{{resource.fields.healthResourceUrl}}" target="_blank" rel="noopener"><img class="viewIconImg"  src="../../../assets/images/contents/ViewIcon.png" alt=""></a>                 
                      <mat-icon id="arrow" *ngIf="!panelOpenState2">keyboard_arrow_down</mat-icon>
                      <mat-icon id="arrow" *ngIf="panelOpenState2">keyboard_arrow_up</mat-icon>
                    </div>
                    <mat-panel-title>
                      {{resource.fields.healthResourceTitle}}                   
                    </mat-panel-title>
                  </mat-expansion-panel-header >
                  <p class="para-text">{{resource.fields.teaser.content[0].content[0].value}}.</p>
                </mat-expansion-panel>
              </mat-checkbox>
            </section>
          </mat-accordion>
          <!-- <mat-accordion [displayMode]="displayMode" [multi]="multi">
            <section class="example-section">
              <div class="row">
                <mat-checkbox  color="primary" #myLife (change)="check(myLife.value)" (click)="mailTitle(dropdown5)" (click)="mailDescription(dropdown5Des)" value="https://assets.ctfassets.net/h2axairjfqha/1k8FfQG9kR9EK487Yk2zPV/51c3614f9f4ccc2a55ac2c7893b768f6/My_Life_My_Way_With_Hizentra.pdf" class="example-margin example-section col-1"></mat-checkbox>
                <mat-expansion-panel (opened)="panelOpenState5 = true"
                                     (closed)="panelOpenState5 = false"
                                     hideToggle
                                     #panel5
                                     
                                     (afterExpand)="selectedTitle = dropdown5"
                                     (afterExpand)="selectedDescription = dropdown5Des"
                                     class="col">                 
                  <mat-expansion-panel-header>
                    <mat-icon id="arrow" *ngIf="!panelOpenState5">keyboard_arrow_down</mat-icon>
                    <mat-icon id="arrow" *ngIf="panelOpenState5">keyboard_arrow_up</mat-icon>
                    <mat-panel-title>
                      {{dropdown5}}
                      <div id="viewIcon5" #a>
                         <a href="//assets.ctfassets.net/h2axairjfqha/1k8FfQG9kR9EK487Yk2zPV/51c3614f9f4ccc2a55ac2c7893b768f6/My_Life_My_Way_With_Hizentra.pdf" target="_blank" rel="noopener"><img src="../../../assets/images/contents/ViewIcon.png" width="35px" alt=""></a>                 
                      </div>
                    </mat-panel-title>
                  </mat-expansion-panel-header >
                  <p class="para-text">{{dropdown5Des}}</p>
                </mat-expansion-panel>
              </div>
            </section>
            </mat-accordion>
            <mat-accordion [displayMode]="displayMode" [multi]="multi">
              <section class="example-section">
                <div class="row">
                  <mat-checkbox  #selfInfused (change)="check(selfInfused.value)" (click)="mailTitle(dropdown6)" (click)="mailDescription(dropdown6Des)" value="https://assets.ctfassets.net/h2axairjfqha/1JDxPKqyzXBAys6k5TseuH/a5620cb16ef437f5494da8965e6369f3/Is_Self-infused_Ig_Right_for_Your_CIDP.pdf" color="primary" class="example-margin example-section col-1" ></mat-checkbox>
                  <mat-expansion-panel (opened)="panelOpenState6 = true"
                                       (closed)="panelOpenState6 = false"
                                       hideToggle
                                       #panel6
                                       
                                       (afterExpand)="selectedTitle = dropdown6"
                                       (afterExpand)="selectedDescription = dropdown6Des"
                                       class="col">                 
                    <mat-expansion-panel-header>
                      <mat-icon id="arrow" *ngIf="!panelOpenState6">keyboard_arrow_down</mat-icon>
                      <mat-icon id="arrow" *ngIf="panelOpenState6">keyboard_arrow_up</mat-icon>
                      <mat-panel-title>
                        {{dropdown6}}
                        <div id="viewIcon6">
                        <a href="//assets.ctfassets.net/h2axairjfqha/1JDxPKqyzXBAys6k5TseuH/a5620cb16ef437f5494da8965e6369f3/Is_Self-infused_Ig_Right_for_Your_CIDP.pdf" target="_blank" rel="noopener"><img src="../../../assets/images/contents/ViewIcon.png" width="35px" alt=""></a>                 
                        </div>
                      </mat-panel-title>
                    </mat-expansion-panel-header >
                    <p class="para-text">{{dropdown6Des}}</p>
                  </mat-expansion-panel>
                </div>
              </section>
            </mat-accordion> -->
            <!-- <mat-accordion [displayMode]="displayMode" [multi]="multi">
                <section class="example-section">
                  <div class="row">
                    <mat-checkbox #infusion  (change)="mailPdf(infusion.value)" (click)="mailTitle(dropdown7)" (click)="mailDescription(dropdown7Des)"  value="https://assets.ctfassets.net/h2axairjfqha/2FxIoYCTyxVGSBTIaDG7ef/5cd16a7ef675f0f7e5e7940eccbb82e3/Hizentra_Infusion_Guide.pdf" color="primary" class="example-margin example-section col-1" ></mat-checkbox>
                    <mat-expansion-panel (opened)="panelOpenState7 = true"
                                         (closed)="panelOpenState7 = false"
                                         hideToggle
                                         #panel7
                                         
                                         (afterExpand)="selectedTitle = dropdown7"
                                         (afterExpand)="selectedDescription = dropdown7Des"
                                         class="col">                 
                      <mat-expansion-panel-header>
                        <mat-icon id="arrow" *ngIf="!panelOpenState7">keyboard_arrow_down</mat-icon>
                        <mat-icon id="arrow" *ngIf="panelOpenState7">keyboard_arrow_up</mat-icon>
                        <mat-panel-title>
                          {{dropdown7}}
                          <div id="viewIcon7">
                          <a href="//assets.ctfassets.net/h2axairjfqha/2FxIoYCTyxVGSBTIaDG7ef/5cd16a7ef675f0f7e5e7940eccbb82e3/Hizentra_Infusion_Guide.pdf" target="_blank" rel="noopener"><img src="../../../assets/images/contents/ViewIcon.png" width="35px" alt=""></a>                 
                          </div>
                        </mat-panel-title>
                      </mat-expansion-panel-header >
                      <p class="para-text">{{dropdown7Des}}</p>
                    </mat-expansion-panel>
                  </div> 
                </section>
            </mat-accordion>  -->
        </div>           -->
        

      <div style="display: flex;justify-content: center;align-items: center;">
        <div class="row button-group">
          <div class="col-md mt-2">
            <button type="button" (click)="mailBody()"  mat-raised-button class="content-button" data-bs-toggle="modal" data-bs-target="#exampleModal">
              Email
            </button>
          </div>        
          <div class="col-md mt-2">
            <button class="content-button"  (click)="viewPrint()" mat-raised-button color="primary">View/Print</button>
          </div>
          <div class="col-md mt-2">
            <button class="content-button" (click)="Oncopy()" mat-raised-button color="primary">Copy Link</button>
          </div>
    </div>
      </div>

     
      
      <!-- Modal -->
     
        <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <div class="modal-header d-flex row">
                <div class="col-10" style=" display:flex;justify-content:center ;">
                  <h5 class="modal-title" id="exampleModalLabel">Fill out the information below to send an email <br>
                    containing this tool.</h5>
                </div>
                <div class="col-2">
                  <button type="button" #closebutton class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
              </div>
            <!-- <form #usersForm="ngForm" (ngSubmit)="getUserFormData(usersForm.value)"> -->
            <form [formGroup]="myemailForm" (ngSubmit)="onSubmit()">
              <div class="modal-body">
                <div class="col box">
                  <label for="validationCustom01" class="form-label">From</label>
                  <input type="email"  formControlName="fromEmail" class="form-control" readonly id="validationCustom01" required>
                </div>
                 <div class="col box">
                  <label for="validationCustom01" class="form-label">To</label>
                  <input type="email"  formControlName="toEmail" class="form-control" id="validationCustom01"  required>
                </div>
                 <div class="col box">
                  <label for="validationCustom01" class="form-label">Subject (not-editable)</label>
                  <input type="text" readonly formControlName="subject"  class="form-control" id="validationCustom01" required>
                </div>
                
              
               <div id="dynamicMail"  class="box" required>

                  <div class="mb-3 box">
                  
                    <label for="validationTextarea"  class="form-label">Body (not-editable)</label>
                    
                    <textarea class="form-control" ngModel={{mail_body}} formControlName="body" readonly matInput rows="6" cols="50" id="validationTextarea  name" placeholder="Required  textarea" required>
                     
                    </textarea>
                         
                  </div>

                  <!-- <div [innerHTML]="mail_body">

                  </div> -->
                                   
               </div>
              </div>
              
              
              <div class="modal-footer">
                <div class="mail_button">
                  <!-- <button class="msg_button"  (click)="getMaildata(data)" (click)=" getMaildata1(data)" mat-raised-button color="primary">Send Message</button> -->
                  <button class="msg_button" type="submit" (click)="closeModal()" mat-raised-button color="primary">Send Message</button>
                </div>
              </div>
            </form>
            </div> 
          </div>
        </div>

        <!-- Modal for email Form -->

        <div class="modal fade" id="exampleModal1" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel"> Provide your Email Address</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <form>
                  <div class="mb-3">
                    <label for="recipient-name" class="col-form-label">Email:</label>
                    <input type="text" class="form-control" id="recipient-name">
                  </div>
                </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" >Close</button>
                <button type="button" class="btn btn-primary"data-bs-dismiss="modal">Submit</button>
              </div>
            </div>
          </div>
        </div>

      <app-subresource></app-subresource>
        <div class="contentbox3" id="contentbox3">
          <div id="contentbox" [innerHtml]="_returnHtmlFromRichText(safetyInformation)">
          </div>
          <!-- <div>
            <h5 style="color:var(--secondaryColor); font-size: 22px; font-weight: 600;">{{heading}}</h5>
            <div style="font-size: 18px; font-weight: 400;">
                  <p>{{paragraph}}</p>
                  <ul>
                    <li>{{unorderedList}}</li>
                    <li>{{listItem}}
                      <ul>
                        <li id="contentbox">{{listItem1}}</li>
                      </ul>
                    </li>
                  </ul>
                  <p><strong><br>{{subhead}}</strong></p>					
            </div>
          </div>
            <div>
          <h2 style="color:var(--secondaryColor); font-size: 22px; font-weight: 600;">{{heading2}}</h2>		
            </div> -->
            <!-- <div class="elementor-element elementor-element-58c3f00 elementor-widget elementor-widget-text-editor" data-id="58c3f00" data-element_type="widget" data-widget_type="text-editor.default">
              <div class="">
                    <p>
                      <strong>{{information}}
                      </strong>
                    </p>
                    <p>
                      <strong>{{information1}}
                      </strong>
                    </p>
                    <p>{{information2}}
                       {{information3}}
                    </p>
                    <p>{{information4}}
                    </p>
                    <p>{{information5}}
                    </p>
                    <p>{{information6}}.<br>{{information7}}
                    </p>
                    <p>{{information8}} Prescribing Information for Hizentra including boxed warning.
                    </p>
                    <p>{{information9}}{{information10}}
                    </p>					
               </div>
            </div> -->
            <div class="float-right">        
              <span>
                <span><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chevron-double-up" viewBox="0 0 16 16">
                  <path fill-rule="evenodd" d="M7.646 2.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 3.707 2.354 9.354a.5.5 0 1 1-.708-.708l6-6z"/>
                  <path fill-rule="evenodd" d="M7.646 6.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 7.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z"/>
                </svg></span>
                <button (click)="scrollToElementById('top')" class="elementor-button-text scrolltotop">BACK TO TOP</button>
              </span>
            </div>
              <div>
                <div class="botton-content">
                  <div>
                   
                 </div>
                </div>
                <div>
                    <div class="">
                        <img src={{cslBearing}} title="CSLBehring" alt="CSLBehring">
                    </div>
                </div>
                <div>
                    <div class="license">
                          <p>{{copyRights}}
                            <br>{{copyRights1}}</p>					
                  	</div>
                 </div>
                 <div>
                    <div class="">
                        <p>{{copyRights2}}  {{copyRightsuri}}</p>						
                    </div>
                 </div>
                 <div>
                  <!-- <div *ngFor="let demos of demo">
                    {{demos.fields.primaryColor?.value}}
                  </div>
                 </div>  -->
					</div>
        </div>
        <div>
          <!-- <pre>
            <div *ngFor="let demos of demo">{{demos.fields.healthResourceTitle}}</div>
          <div *ngFor="let demos of demo">{{demos.fields.teaser?.content[0].content[0].value}}</div>
          </pre> -->
        </div>
      </div>
      <app-chat-bot></app-chat-bot>
</div>





