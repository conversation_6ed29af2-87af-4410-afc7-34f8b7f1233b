<div class=" row container">
      <div class="col-6 content-left">
        <h1 class="main-text">{{mainText}}</h1>
        <p class="sub-text" [innerHtml]="_returnHtmlFromRichText(mainContent)"></p>
      </div>
      <div class="col-5 for768px">
            <img class="img-fluid" width="100%" src={{getImg}}>
      </div> 
    </div>
<div class="row container">
  <div class="col-6 content-left1">
    <mat-accordion>
     <mat-expansion-panel (opened)="panelOpenState = true"
                          (closed)="panelOpenState = false"
                          hideToggle>                 
       <mat-expansion-panel-header>
         <mat-icon *ngIf="!panelOpenState">add</mat-icon>
         <mat-icon *ngIf="panelOpenState">remove</mat-icon>
         <mat-panel-title>
           <h6>{{subheadtext}}</h6>
         </mat-panel-title>
       </mat-expansion-panel-header >
       <p class="para-text"> {{subheadpara}}</p>
     </mat-expansion-panel>
   <mat-expansion-panel (opened)="panelOpenState2 = true"
                        (closed)="panelOpenState2 = false"
                        hideToggle>
       <mat-expansion-panel-header>
         <mat-icon *ngIf="!panelOpenState2">add</mat-icon>
         <mat-icon *ngIf="panelOpenState2">remove</mat-icon>
         <mat-panel-title>
           <h6>{{subheadtext1}}</h6>
         </mat-panel-title>
       </mat-expansion-panel-header>
         <p class="para-text"> {{subheadpara1}}</p>
       </mat-expansion-panel>
     <mat-expansion-panel (opened)="panelOpenState1 = true"
                           (closed)="panelOpenState1 = false"
                           hideToggle>
       <mat-expansion-panel-header>
         <mat-icon *ngIf="!panelOpenState1">add</mat-icon>
         <mat-icon *ngIf="panelOpenState1">remove</mat-icon>
         <mat-panel-title>
          <h6>{{subheadtext2}}</h6>
         </mat-panel-title>
       </mat-expansion-panel-header>
         <p class="para-text">{{subheadpara2}}</p>
     </mat-expansion-panel>
   </mat-accordion>
 </div>
 <div class="col-6 content-left1">
  <div class="form-check-box qsa-form">
    <form class="box" [formGroup]="form" >
      <h6 id="medicine" >{{formTitle}}</h6>
      <div>
        <mat-form-field class="example-full-width" appearance="fill">
          <mat-label>Medicine</mat-label>
          <input type="text"
                 placeholder="Medicines"
                 matInput
                 id="goThrough"
                 formControlName="goThrough"
                 [matAutocomplete]="auto"
                 [ngClass]="{ 'is-invalid': submitted && f.goThrough.errors }"
                 (input)="inputHandle($event)"
                 required
                 >
                 <!--<mat-error *ngIf="submitted && form.get('goThrough').hasError('required')" style="color: red !important;">
                  Required Field!
                </mat-error>--> 
                <div *ngIf="submitted && f.goThrough.errors" class="invalid-feedback" style="margin-top: 10px;">
                  *Medicine is required
                </div>
          <mat-autocomplete #auto="matAutocomplete" (optionSelected)="onSelectionChanged($event)" id="matfilteroption">
            <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
              {{option}}
            </mat-option>
          </mat-autocomplete>
        </mat-form-field>
      </div>
      <div id="div2" style="margin-top: 10px;">
        <mat-form-field class="example-full-width" appearance="fill">
          <mat-label>Zip code</mat-label>
          <input type="text"
                 placeholder="Zip code"
                 aria-label="Number"
                 matInput
                 id="zipCode"
                 formControlName="zipCode"
                 [ngClass]="{ 'is-invalid': submitted && f.zipCode.errors }"
                 oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');" 
                 >
                <!----<mat-error *ngIf="submitted && form.get('zipCode').hasError('required')" style="color: red !important;">
                  Required Field!
                </mat-error>-->
                <div *ngIf="submitted && f.zipCode.errors" class="invalid-feedback" style="margin-top: 10px;">
                  *Zipcode is required
                </div>
          <!--<mat-autocomplete>
            <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
              {{option}}
            </mat-option>
          </mat-autocomplete>-->
        </mat-form-field>
      </div>

      <div class="form-group form-check"  style="margin-top: 20px;">
        <input
          type="checkbox"
          formControlName="acceptTerms"
          id="acceptTerms"
          class="form-check-input"
          [ngClass]="{ 'is-invalid': submitted && f.acceptTerms.errors }"/>
        <label style="position: relative; top: -3px;" [innerHtml]="_returnHtmlFromRichText(privacyContent)">
        </label>
        <div *ngIf="submitted && f.acceptTerms.errors" class="invalid-feedback" style="margin-top:-15px">
          *Confirmation is required
        </div>
      </div>


     <!--<div id="captchaElem" class="captchaclass">
      <ngx-recaptcha2 #captchaElem
        [siteKey]="siteKey"
        id="captcha"
        formControlName="recaptcha"
        [ngClass]="{ 'is-invalid': submitted && f.recaptcha.errors }" id="captcha_custom" class="captchadiv">
      </ngx-recaptcha2>
      
    <div *ngIf="submitted && f.recaptcha.errors" class="invalid-feedback captchadiv">                   
      *Please verify captcha
   </div>
    </div>-->
    

      <div class="form-button" id="button1" style="text-align:center">
        <button class="button" (click)="onSubmit(id)" type="button"><p>Go to Your QSA</p></button>
      </div>
      <div class="form-button" id="button2" style="text-align:center">
        <button class="button" (click)="onSubmit2(id)" [disabled]="!optionchoosed" type="button"><p>Go to Your QSA</p></button>
      </div>
    </form>   
  </div>
</div>
<app-chat-bot id="chatbot"></app-chat-bot> 
</div>