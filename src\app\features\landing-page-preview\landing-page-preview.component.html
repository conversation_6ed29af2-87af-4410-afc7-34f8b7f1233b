<div class="container-fluid" id="top" *ngIf="allcontents">
  <div>
    <div class="container-fluid" id="headgradient">
      <div class="container">
        <div class="row top-content">
          <div class="col-md-6 HizentraLogo">
            <img src={{logo}} width="350px" alt="ResourceLogo">
          </div>
          <div class="col-md-6 pdf-info">
            <div class="row">
              <div class="col-md-12 pdf-info">
                <div class="" *ngIf="p_file">
                  <a class="paraA header-link" href={{p_file}} target="_blank" rel="noopener">Prescribing
                    Information</a><span style="display:inline-block;margin-left:10px;margin-right:10px" *ngIf="p_file">
                    <h5 class="header-link">|</h5>
                  </span>
                </div>
                <div class="text-white">
                  <a class="paraA header-link" (click)="scrollToElementById('contentbox')" target="_blank"
                    rel="noopener">Important Safety Information</a><span
                    style="display:inline-block;margin-left:10px;margin-right:10px">
                    <h5 style="visibility:hidden">|</h5>
                  </span>
                </div>
              </div>
              <!-- <div class="col-md-6 text-center pt-4">
                <button (click)="scrollToElementById('box')" class="top-content-button">Request QSA</button>
              </div> -->
              <div class="">
                <div class="row justify-content-center">
                  <div class="col-md-3 col-lg-auto" *ngFor="let btn of nonLinkButtons; let i = index"
                    style="padding-top:1.9rem !important;">
                    <button style="min-width:150px;border:0;background-color: transparent;"
                      (click)="onNonLinkButtonClick(i); scrollToElementById(btn.fields.buttonLink)">
                      <a style="text-decoration: none;
                               border-radius: 6px;
                               padding: 13px 50px;
                               font-size: 16px;" [style.color]="btn?.fields?.customButtonTextColor?.value"
                        [style.backgroundColor]="btn?.fields?.customButtonBackgroundColor?.value"
                        [ngStyle]="{ 'background-color': i === nonLinkHoveredIndex ? btn?.fields?.customButtonBackgroundRolloverColor?.value : btn?.fields?.customButtonBackgroundColor?.value }"
                        (mouseenter)="onNonLinkButtonMouseEnter(i)" (mouseleave)="onNonLinkButtonMouseLeave()">
                        {{btn.fields.actionbuttonText}}
                      </a>
                    </button>
                  </div>
                  <div class="col-md-9 col-lg-auto" *ngFor="let btn of linkButtons; let j = index"
                    style="padding-top:1.9rem !important;">
                    <button style="min-width:150px;border:0;background-color: transparent;"
                      (click)="onLinkButtonClick(j)" (mouseenter)="onLinkButtonMouseEnter(j)"
                      (mouseleave)="onLinkButtonMouseLeave()">
                      <a href="{{btn.fields.buttonLink}}{{userId}}" style="text-decoration: none;
                               border-radius: 6px;
                               padding: 13px 50px;
                               font-size: 16px;" [style.color]="btn?.fields?.customButtonTextColor?.value"
                        [style.backgroundColor]="btn?.fields?.customButtonBackgroundColor?.value"
                        [ngStyle]="{ 'background-color': j === linkHoveredIndex ? btn?.fields?.customButtonBackgroundRolloverColor?.value : btn?.fields?.customButtonBackgroundColor?.value }">
                        {{btn.fields.actionbuttonText}}
                      </a>
                    </button>
                  </div>
                </div>
              </div>



            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="contentbox">
      <div class="container">
        <div>
          <h1 class="medicines"
            style="color:var(--resourceh1Color);font-family: var(--fontStyle);font-size: 2.375rem;font-weight: 600;">
            {{mainHead}}</h1>
        </div>

        <!-- <div>
            <h3 class="medicines" style="font-size:1.125rem;font-family: var(--fontStyle);font-weight: 400;line-height: 30px;">{{mainContent}}</h3>
          </div> -->
        <div class="mainContent" [innerHtml]="_returnHtmlFromRichText(mainContent)">
        </div>



        <div class="copyBlockFootnotes" [innerHtml]="_returnHtmlFromRichText(copyBlockFootnotes)"
          *ngIf="copyBlockFootnotes"></div>

        <div>
          <h2 class="medicines"
            style="color:var(--resourceh1Color);font-family: var(--fontStyle);font-size: 2.375rem;font-weight: 600;">
            {{videoTitle}}</h2>
        </div>
        <div class="video-container" *ngIf="vimeo_video">
          <div #playerContainer class="video-wrapper"></div>
        </div>
        <!-- text contentBelowVideo -->
        <div *ngIf="headingText && !contentBelowVideoImage"
          style="margin-top: 30px;  ">
          <div>
            <div class="contentheadingText" [innerHtml]="_returnHtmlFromRichText(headingText)"></div>
            <div class="contentBelowText" *ngIf="contentBelowText"
              [innerHtml]="_returnHtmlFromRichText(contentBelowText)"></div>
            <div class="contentBelowrefText" *ngIf="contentBelowrefText"
              [innerHtml]="_returnHtmlFromRichText(contentBelowrefText)"></div>
          </div>
        </div>
        <!-- image+text contentBelowVideo -->
        <div *ngIf="headingText && contentBelowVideoImage"
          style="margin-top: 30px; display: flex;  ">
          <div>
            <div class="contentheadingText" [innerHtml]="_returnHtmlFromRichText(headingText)"></div>
            <div class="contentBelowText" *ngIf="contentBelowText"
              [innerHtml]="_returnHtmlFromRichText(contentBelowText)"></div>
            <div class="contentBelowrefText" *ngIf="contentBelowrefText"
              [innerHtml]="_returnHtmlFromRichText(contentBelowrefText)"></div>
          </div>
          <img class="qsa-sample" src={{contentBelowVideoImage}} alt="contentBelowVideoImage">
        </div>

        <!-- image contentBelowVideo -->
        <div *ngIf="contentBelowVideoImage && !headingText" style="margin-top: 30px; display: flex; justify-content: var(--imageOnlyAlignment) ;">
          <img *ngIf="contentBelowVideoImage" class="qsa-sample" src={{contentBelowVideoImage}}
            alt="contentBelowVideoImage">
        </div>

        <div class="row" style="margin-top: 30px;">
          <div class="col-xl" style="display: flex;justify-content: center;" *ngIf="sampleImg">
            <img class="qsa-sample" src={{sampleImg}} alt="QSA-Sample">
          </div>
          <div class="col-md medicines">
            <h6
              style="color:var(--primaryColor);margin-bottom:25px;font-weight: 500;font-size:1.5rem;font-family: var(--fontStyle);">
              {{mainSub}}</h6>
            <!--<div class="ulHead" style="margin-bottom:25px;">{{unordered}}</div>-->
            <!--<ul>
                <li>{{listed1}}</li>
                <li>{{listed2}}</li>
                <li>{{listed3}}</li>
              </ul>-->
            <div>

              <div class="mainContent" [innerHtml]="_returnHtmlFromRichText(copyblocklist)"></div>
              <form class="box" id="request-qsa-form" [formGroup]="landingForm" (ngSubmit)="userData()"
                *ngIf="dynamicMsg">
                <form [formGroup]="captchaForm">
                  <div class="form-group row" *ngIf="step == 1">
                    <h6
                      style="color:var(--primaryColor); font-weight: 500; font-size: 1.5rem;font-family: var(--fontStyle);">
                      {{dynamicMsg}}</h6>
                    <div class="form-check form-group medicines">
                      <input class="form-check-input" formControlName="acceptTerms" type="checkBox" id="acceptTerms"
                        name="policy" [ngClass]="{ 'is-invalid': submitted && f.acceptTerms.errors }" />
                      <label [innerHtml]="_returnHtmlFromRichText(privacyContent)">
                      </label>
                      <div *ngIf="submitted && f.acceptTerms.errors" class="invalid-feedback"
                        style="position: relative;top:-1.2rem ;">
                        *Confirmation is required
                      </div>
                    </div>


                    <!--<div class="form-check form-group medicines" id="captchaElem" class="captchaclass">
                    <ngx-recaptcha2 #captchaElem
                      [siteKey]="siteKey"
                      id="captcha"
                      formControlName="captcha"
                      [ngClass]="{ 'is-invalid': submitted && f.captcha.errors }" id="captcha_custom" class="captchadiv">
                    </ngx-recaptcha2>
                    <div *ngIf="submitted && f.captcha.errors" class="invalid-feedback captchadiv">                   
                      *Please verify captcha
                   </div>
                  </div>-->

                  </div>
                </form>
                <div class="form-group" *ngIf="step == 2">
                  <h6
                    style="color:var(--primaryColor); font-weight: 500; font-size: 1.5rem;font-family: var(--fontStyle);">
                    {{dynamicMsg}}</h6>
                  <div class="row">
                    <div class="col-md request-input">
                      <input class="form-control form-control-lg" type="text" id="firstName" formControlName="firstName"
                        name="firstName" placeholder="First Name" aria-label=".form-control-lg example"
                        [ngClass]="{ 'is-invalid': isSubmitted && for.firstName.errors}" />
                      <div *ngIf="isSubmitted && for.firstName.errors" class="invalid-feedback">
                        *First Name is required
                      </div>
                    </div>

                    <div class="col-md request-input">
                      <input class="form-control form-control-lg" id="lastName" type="text" formControlName="lastName"
                        name="lastName" placeholder="Last Name" aria-label=".form-control-lg example"
                        [ngClass]="{ 'is-invalid': isSubmitted && for.lastName.errors}" />
                      <div *ngIf="isSubmitted && for.lastName.errors" class="invalid-feedback">
                        *Last Name is required
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md request-input">
                      <input class="form-control form-control-lg" type="email" id="email" formControlName="toEmail"
                        name="toEmail" placeholder="Email" aria-label=".form-control-lg example"
                        [ngClass]="{ 'is-invalid': isSubmitted && for.toEmail.errors}" />
                      <div *ngIf="isSubmitted && for.toEmail.errors" class="invalid-feedback">
                        *Email is required
                      </div>
                    </div>
                    <div class="col-md request-input">
                      <input class="form-control form-control-lg" type="text" formControlName="npi" name="npi"
                        placeholder="NPI #" aria-label=".form-control-lg example">
                    </div>
                    <div class="mb-3" hidden>
                      <label for="recipient-qsaUrl" class="col-form-label">qsaUrl</label>
                      <input type="text" ngModel={{qsaUrl}} class="form-control" formControlName="qsaUrl" name="qsaUrl"
                        id="recipient-qsaUrl">
                    </div>
                    <div class="mb-3" hidden>
                      <label for="recipient-qsaUrl" class="col-form-label">drug</label>
                      <input type="text" ngModel={{drug}} class="form-control" formControlName="drug" name="drug"
                        id="recipient-drug">
                    </div>
                  </div>
                </div>
                <div style="display: flex;justify-content: center;">
                  <button type="button" (click)="next()" *ngIf="step != 2"
                    class="from-buttom medicines mx-2">Next</button>
                </div>
                <div style="display: flex;justify-content: center;">
                  <button type="submit" *ngIf="step == 2" class="btn medicines request-button">Go to Your QSA</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <app-landing-preview-page-subresource></app-landing-preview-page-subresource>
    <div class="contentbox3" id="contentbox3">
      <div class="container bottom-isi">
        <!--<div id="contentbox" [innerHtml]="_returnHtmlFromRichText(safetyInformation)"></div>-->
        <h4 style="padding-top: 20px;text-transform:uppercase;font-weight:700" id="contentbox">Indications</h4>
        <div class="isi-text" id="contentbox" [innerHtml]="_returnHtmlFromRichText(indication)">
        </div>
        <h4 style="padding-top: 10px;text-transform:uppercase;font-weight:700">Important safety Information</h4>
        <div class="isi-text" id="contentbox" [innerHtml]="_returnHtmlFromRichText(isi)"></div>
        <div class="reference" [innerHtml]="_returnHtmlFromRichText(references)" *ngIf="references"></div>
        <div class="disclaimer isi-text" [innerHtml]="_returnHtmlFromRichText(disclaimer)" *ngIf="disclaimer"></div>
        <div class="row">
          <div class="text-end">
            <span>
              <span><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                  class="bi bi-chevron-double-up" viewBox="0 0 16 16">
                  <path fill-rule="evenodd"
                    d="M7.646 2.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 3.707 2.354 9.354a.5.5 0 1 1-.708-.708l6-6z" />
                  <path fill-rule="evenodd"
                    d="M7.646 6.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 7.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z" />
                </svg></span>
              <button (click)="scrollToElementById('top')" class="elementor-button-text scrolltotop">BACK TO
                TOP</button>
            </span>
          </div>
        </div>
        <div class="botton-content" id="footgradient" style="padding:2%" *ngIf="landFootImg || copyRights">
          <div>
            <div class="" style="padding-top: 35px;">
              <img src={{landFootImg}} title="LandingImage" alt="LandingImage">
            </div>
          </div>
          <div>
            <div class="license text-white">
              <div [innerHtml]="_returnHtmlFromRichText(copyRights)" id="foot_links">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <app-chat-bot></app-chat-bot>
</div>
<div id="alertmssg" class="text-center" *ngIf="alertmssg">
  <h4 style="color:var(--primaryColor);padding-top:10%">No Matching Drug Found</h4>
</div>