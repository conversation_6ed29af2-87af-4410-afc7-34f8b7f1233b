import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ContactUsComponent } from './contact-us/contact-us.component';
import { LandingPagePreviewComponent } from './landing-page-preview/landing-page-preview.component';
import { LandingPageComponent } from './landing-page/landing-page.component';
import { NewlandingPagePreviewComponent } from './newlanding-page-preview/newlanding-page-preview.component';
import { PdfViewerComponent } from './pdf-viewer/pdf-viewer.component';
import { DiscountComponent } from './pharmacy/discount/discount.component';
import { PharmacyComponent } from './pharmacy/pharmacy.component';
import { PreviewContentComponent } from './preview-content/preview-content.component';
import { PrivacyPolicyComponent } from './privacy-policy/privacy-policy.component';
import { QsaGinaComponent } from './qsa-gina/qsa-gina.component';
import { QsaVideoComponent } from './qsa-video/qsa-video.component';
import { ResourcePagePreviewComponent } from './resource-page-preview/resource-page-preview.component';
import { ResourceComponent } from './resource/resource.component';
import { ResourcesPagePreviewComponent } from './resources-page-preview/resources-page-preview.component';
import { ResourcesComponent } from './resources/resources.component';
import { TermsOfUseComponent } from './terms-of-use/terms-of-use.component';
import { AboutUsComponent } from './users/about-us/about-us.component';
import { InterventionMapComponent } from './users/intervention-map/intervention-map.component';
import { UsersComponent } from './users/users.component';

const routes: Routes = [
  // { path: '', pathMatch: 'full', redirectTo: 'users' },

  {
    path:'',
    component:UsersComponent,
  },

  {
    path:'about-us',
    component:AboutUsComponent,
    
  },
  {
    path:'contact-us',
    component:ContactUsComponent,
  },

  {
    path:'intervention',
    component:InterventionMapComponent,
  },

  {
    path:'resource/:drugname/:id',
    component:ResourceComponent,
  },

  {
    path:'resources/:drugname/:id',
    component:ResourcesComponent,
  },

  {
    path:'home/:id',
    component:LandingPageComponent,
  },
  {
    path:'home/:id/:radomtext',
    component:LandingPageComponent,
  },
  {
    path:'resource-page-preview/:drugname',
    component: ResourcePagePreviewComponent,
  },
  {
    path:'resources-page-preview/:drugname',
    component: ResourcesPagePreviewComponent,
  },
  {
    path:'landing-page-preview/:drugname',
    component: LandingPagePreviewComponent,
  },
  {
    path:'newlanding-page-preview/:drugname',
    component: NewlandingPagePreviewComponent,
  },
  {
    path:'privacy-notice',
    component: PrivacyPolicyComponent,
  },

  {
    path:'terms-of-use',
    component: TermsOfUseComponent,
  },

  {
    path: 'pdfViewer',
    component:PdfViewerComponent,
  },
  {
    path: 'qsa-video',
    component:QsaVideoComponent,
  },
  {
    path: 'gina',
    component:QsaGinaComponent,
  },
  {
    path: 'pharmacy',
    component:PharmacyComponent,
  },
  {
    path: 'discount/:id',
    component: DiscountComponent
  },
  {
    path: 'preview-content/:id',
    component: PreviewContentComponent
  },
  {
    path: '**',
    component:UsersComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class FeatureRoutingModule { }
