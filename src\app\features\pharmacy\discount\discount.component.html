
<div class="container">
  <div class="main row">
  <div class="d-flex col-12" id="hide-for-print">
    <span role="img" aria-label="arrow-left" class="anticon anticon-arrow-left">
      <svg viewBox="64 64 896 896" focusable="false" data-icon="arrow-left" width="1em" height="1em" fill="#629a35" aria-hidden="true">
        <path d="M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path>
      </svg>
    </span> &nbsp; &nbsp; 
    <span (click)="return()" style="cursor: pointer;" class="back col-12">Back to all prices</span>
  </div>
  <div id="elementToCapture" #pdfContent>
  <div class="content">Present this discount card to your pharmacist</div>

  <div class="row" style="border:#f5f5f5 dashed; border-radius: 10px; padding: 20px;">
    <div class="col-7">
      <div class="row">
        <div>
          <h6 style="font-weight: 400;">Your estimated price</h6>
          <h1 style="font-weight: bolder;">${{selectProducts.price | number : '1.2-2'}}</h1>
          <p style="font-weight: 400;">at {{selectProducts.pharmacyName}}</p>
        </div>
        <div>
          <h1 style="font-weight: bolder;">{{selectProducts.ln}}</h1>
          <!--<p style="font-weight: 400;">550 MG  | {{selectProducts.qty}} Tablet</p>-->
        </div>
        <div>
        <p><b>Reminder:</b> This discount card can be used for any prescriptions for you and your family. Discount program. Not insurance.</p>
        </div>
      </div>
    </div>
    <div class="col-5" style="background-color: #f5f5f5; border-radius: 10px; padding: 20px;">
      <div style="margin-bottom: 20px;">
        <h5>Pharmacy Use</h5>
      </div>
      <div class="row">
          <div class="col-6">
            <p><b>BIN #:</b></p>
          </div>
          <div style="text-align: end;" class="col-6">
            <p>015558</p>
          </div>
      </div>
      <div class="row">
          <div class="col-6">
            <p><b>PCN:</b></p>
          </div>
          <div style="text-align: end;" class="col-6">
            <p>HT</p>
          </div>
      </div>
      <div class="row">
          <div class="col-6">
            <p><b>GROUP #:</b></p>
          </div>
          <div style="text-align: end;" class="col-6">
            <p>{{selectProducts.groupNum}}</p>
          </div>
      </div>
      <div class="row">
          <div class="col-6">
            <p><b>CARD HOLDER ID:</b></p>
          </div>
          <div style="text-align: end;" class="col-6">
            <p>{{selectProducts.groupName}}</p>
          </div>
      </div>
      <p><b>Attention Pharmacist:</b> This card is active and entitles the card holder to all prescription drug benefits associated with the BIN and Group Numbers (per state and federal laws).</p>
      <span><b>Pharmacist & Customer Support:</b></span><br>
      <span><b>{{selectProducts.phone}}</b></span>
    </div>
  </div>
  </div>
  <div style="display: flex;justify-content: center;align-items: center;margin-top: 30px;" id="hide-for-print">
    <div class="row button-group">
      <!--<div class="col-md mt-2" style="display: flex;justify-content: center;">
        <button type="button" mat-raised-button color="primary" data-bs-toggle="modal" data-bs-target="#exampleModal" class="content-button">
          <svg style="margin-right: 10px;" viewBox="64 64 896 896" focusable="false" data-icon="mobile" width="1em" height="1em" fill="currentColor" aria-hidden="true">
            <path d="M744 62H280c-35.3 0-64 28.7-64 64v768c0 35.3 28.7 64 64 64h464c35.3 0 64-28.7 64-64V126c0-35.3-28.7-64-64-64zm-8 
                    824H288V134h448v752zM472 784a40 40 0 1080 0 40 40 0 10-80 0z">
            </path>
          </svg>Text Card</button>
      </div>  -->      
      <div class="col-md mt-2" style="display: flex;justify-content: center;">
        <button class="content-button" mat-raised-button color="primary" (click)="takeScreenshot()">
          <svg style="margin-right: 10px;" viewBox="64 64 896 896" focusable="false" data-icon="mail" width="1em" height="1em" fill="currentColor" aria-hidden="true">
            <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40
             110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4
             232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z">
            </path>
          </svg>Email Card</button>
          <button type="button" id='openbutton' mat-raised-button class="content-button" data-bs-toggle="modal"
          data-bs-target="#exampleModal" hidden>
          <p>Email</p>
        </button>
      </div>
      <div class="col-md mt-2" style="display: flex;justify-content: center;">
        <button class="content-button" mat-raised-button color="primary" (click)="print()">
          <svg style="margin-right: 10px;" viewBox="64 64 896 896" focusable="false" data-icon="printer" width="1em" height="1em" fill="currentColor" aria-hidden="true">
            <path d="M820 436h-40c-4.4 0-8 3.6-8 8v40c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-40c0-4.4-3.6-8-8-8zm32-104H732V120c0-4.4-3.6-8-8-8H300c-4.4 0-8
             3.6-8 8v212H172c-44.2 0-80 35.8-80 80v328c0 17.7 14.3 32 32 32h168v132c0 4.4 3.6 8 8 8h424c4.4 0 8-3.6 8-8V772h168c17.7 0 32-14.3 32-32V412c0-44.2-35.8-80-80-80zM360
             180h304v152H360V180zm304 664H360V568h304v276zm200-140H732V500H292v204H160V412c0-6.6 5.4-12 12-12h680c6.6 0 12 5.4 12 12v292z">
            </path>
          </svg>Print Card</button> 
      </div>
    </div>
  </div>
  <div class="row" style="margin-top: 30px;" id="hide-for-print">
    <p><b>FAQs</b></p>
    <span><b>Q:</b> What do I do if my pharmacy is not familiar with this savings card?</span><br>
    <p><b>A:</b> If your pharmacist is unable to process your savings card for any reason, please have them call 1-************ (M-F 9AM-6PM EST).</p>
    <span><b>Q:</b> Can I use the savings card along with my health insurance?</span><br>
    <p><b>A:</b> This discounted price may be lower than your health insurance co-pay. However, it cannot be used in conjunction with your health insurance and it can not be used to lower your co-pay. Ask your pharmacist for help in receiving the best possible price.</p>
    <span><b>Q:</b> What do I do if the pharmacist runs my savings card and the price on my savings card is not correct?</span><br>
    <p><b>A:</b> While we do our best to be as accurate as possible, prescription prices are constantly changing. Please make sure to report any and all <NAME_EMAIL>. The final price is determined by your local pharmacy. Please ask your pharmacist to help you find the lowest possible price.</p>
  </div>
  </div>
</div>


<!-----------------BS Modal starts --------------------> 
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" id="hide-for-print">
  <div class="modal-dialog">
    <div class="modal-content" style="padding: 15px;">
      <div class="modal-header row">
        <div class="col-6">
          <h1 style="color: var(--primaryColor);">Text Card</h1>
        </div>
        <div class="col-6 d-flex justify-content-flex-end">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
      <div>
          <h5>Enter your phone number to text this card.</h5>
        </div>
      </div>
      <div class="modal-body">
        <form [formGroup]="myForm">
         <div class="row">
          <div class="mb-3 col-6">
            <label for="phonenumber" class="col-form-label">MOBILE PHONE NUMBER</label>
             <input type="text" 
                    formControlName="phonenumber" 
                    class="form-control" 
                    id="recipient phonenumber" 
                    placeholder="************">
          </div>
         </div>
         <div class="row">
          <div class="mb-3 col-6">
            <label for="recipient-name1" class="col-form-label">FIRST NAME</label>
            <input type="text" formControlName="firstname" class="form-control" id="recipient-name!" placeholder="Enter First Name">
          </div>
          <div class="mb-3 col-6">
            <label for="recipient-name2" class="col-form-label">LAST NAME</label>
            <input type="text" formControlName="lastname" class="form-control" id="recipient-name2" placeholder="Enter Last Name">
          </div>
         </div>
          <div class="mb-3">
            <label for="message-text" class="col-form-label">EMAIL ADDRESS</label>
            <input class="form-control" formControlName="email" id="message-text" placeholder="<EMAIL>">
          </div>
          <div class="modal-foot">
            <button (click)="onSubmit()" type="submit" style="background-color: var(--primaryColor);border: none;margin-bottom: 20px;" class="btn btn-primary d-flex justify-content-flex-start">Send message</button>
            <p>By entering your phone number and selecting Send Text you agree that Msg & data rates may apply. Terms and privacy information are available here.</p>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- --------- BS Modal Ends ----------------- -->

<!-- Modal -->

<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header d-flex row">
        <div class="col-10" style=" display:flex;justify-content:center ;">
          <h5 class="modal-title" id="exampleModalLabel">Fill out the information below to send an email <br>
            containing this tool.</h5>
        </div>
        <div class="col-2">
          <button type="button" #closebutton class="btn-close" data-bs-dismiss="modal"
            aria-label="Close"></button>
        </div>
      </div>
      <!-- <form #usersForm="ngForm" (ngSubmit)="getUserFormData(usersForm.value)"> -->
      <form [formGroup]="myemailForm" (ngSubmit)="onemailSubmit()">
        <div class="modal-body">
          <div class="col box">
            <label for="validationCustom01" class="form-label">From</label>
            <input type="email" formControlName="fromEmail" class="form-control" readonly id="fromEmail" required>
          </div>
          <div class="col box">
            <label for="validationCustom01" class="form-label">To</label>
            <input type="email" formControlName="toEmail" class="form-control" id="toEmail" required>
          </div>
          <div class="col box">
            <label for="validationCustom01" class="form-label">Subject (not-editable)</label>
            <input type="text" readonly formControlName="subject" class="form-control" id="subject" required>
          </div>

          <div id="dynamicMail" class="box" required>
            <div class="mb-3 box">
              <label for="validationTextarea" class="form-label">Body (not-editable)</label>
              <div class="disabled-input" id="imagebody">
                <img src="" id="showimg" alt="DrugCard" style="width:100%;height:auto">
              </div>
            </div>
          </div>
        </div>


        <div class="modal-footer">
          <div class="mail_button">
            <button class="msg_button" type="submit" mat-raised-button color="primary">Send Message</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>