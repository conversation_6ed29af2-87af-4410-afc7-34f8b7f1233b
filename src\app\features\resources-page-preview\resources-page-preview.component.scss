// @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;900&display=swap');

.container-fluid {
    //position: relative;
    // left: 1.5rem;
    // top: 3rem;
    width: 100vw;
    /* make it 100% of the viewport width (vw) */
    margin-left: calc((100% - 100vw) / 2);
    /* then remove the gap to the left of the container with this equation */
    padding: 0 !important
}

.disabled-input {
    background: #eaecef;
    padding: 10px;
}

::ng-deep.col-1 {
    width: 0.33333%;
}

::ng-deep.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:hover:not([aria-disabled=true]) {
    background-color: transparent;

}

.text-banner-image.Left {
    float: left;
    margin-right: 10px;
}

.text-banner-image.Right {
    float: right;
    margin-left: 10px;
}

.text-banner-image.Above {
    display: block;
    text-align: center;
    margin-bottom: 10px;
}

.text-banner-image.Below {
    display: block;
    text-align: center;
    margin-top: 10px;
}

.text-mobile-banner-image.Above {
    display: block;
    text-align: center;
    margin-bottom: 10px;
}

.text-mobile-banner-image.Below {
    display: block;
    text-align: center;
    margin-top: 10px;
}

/* Clear floats after the text blocks */
.text-banner-content::after {
    content: "";
    display: table;
    clear: both;
}

.text-mobile-banner-content::after {
    content: "";
    display: table;
    clear: both;
}

.mockupimg,
.mockupimgmobile,
.text-banner {
    justify-content: center;
}

.mockupimg img {
    width: var(--ImageBannerwidth);
    height: var(--ImageBannerheight)
}

.text-banner-image img {
    width: var(--imagewidth);
    height: var(--imageheight);
}

.text-mobile-banner-image img {
    width: var(--imageMobilewidth);
    height: var(--imageMobileheight);
}

@media (max-width:500px) {

    .mockup,
    .mockupimg,
    .text-banner {
        display: none;
    }
}

@media (min-width:500px) {

    .mockupmobile,
    .mockupimgmobile,
    .text-mobile-banner {
        display: none;
    }
}

.mat-expansion-panel-margin {
    // margin-bottom: 10px !important;
}

h4 {
    font-weight: 600;
}

#foot_links a {
    color: var(--resourceLinkColor);
}

#foot_links :host::ng-deep a {
    color: var(--resourceLinkRolloverColor);

}

a {
    color: var(--resourceLinkColor);
    cursor: pointer;
    font-weight: 500;
}

a:hover {
    color: var(--resourceLinkRolloverColor);
    font-weight: 500;
    cursor: pointer;
}

#foot_links {

    /* styles for the host element go here */
    :host {
        a {
            color: white
        }
    }

    /* styles for child elements go here */
    ::ng-deep {
        a {
            color: white
        }
    }
}

:host::ng-deep a {
    color: var(--resourceLinkColor);
    cursor: pointer;
    text-decoration: none;

}

:host::ng-deep a:hover {
    color: var(--resourceLinkRolloverColor);
    // font-weight: 500;
    text-decoration: none;
    cursor: pointer;
}

a,
u {
    text-decoration: none;
}

.para {
    position: relative;
    left: 1rem;
    top: 0.5rem;
    font-size: var(--bodyTextFontSize);
    font-weight: var(--bodyTextFontWeight);
    line-height: var(--bodyTextLineHeight);
    color: #282828;
    font-family: var(--fontStyle);
    width: 100%;
}

.paraA {
    color: var(--resourceLinkColor);
    cursor: pointer;
}

.paraA:hover {
    color: var(--resourceLinkRolloverColor);
    cursor: pointer;
}


.fillable {
    color: var(--resourcesecondarycolor);
    position: relative;
    left: 1.5rem;
    margin: 20px 0px 20px 0;
}

.copyicon {
    position: relative;
    top: -1rem;
}

.mat-expansion-panel {
    box-shadow: none;
    //width: calc(100vw - 35vw);
    background-color: var(--textBoxResourceListsbg);
    padding-right: 10px;
}

.mat-expansion-panel:hover {
    background-color: var(--textBoxResourceListsbg);
}

.main-content-resource {
    // background-color: #f1f1f1;
    margin-bottom: 80px;
    // border-top: 3px solid var(--resourcehorizontalRuleColor);
    //width: 100vw; /* make it 100% of the viewport width (vw) */
    //margin-left: calc((100% - 100vw) / 2);  /* then remove the gap to the left of the container with this equation */
}


// ::ng-deep.mat-content{
//     height: 67%;
// }

.top-content {
    width: 100%;
    position: relative;
    //left: 3rem;
}

.pdf-info {
    color: var(--resourceHeaderColor) !important;
    display: flex;
    justify-content: end;
    align-items: center;
    //font-size: 20px;
    font-size: 1.25rem;
    position: relative;
    //left: -2rem;
}

.HizentraLogo {
    position: relative;
    //left: 1.5rem;
    margin-top: 20px;
    margin-bottom: 20px;
}

.brand-msg {
    width: 100%;
    height: 140px;
    border: none;
    background-color: var(--resourcesecondarycolor);
    color: #fff;
    font-weight: 600;
    //font-size: 30px;
    font-size: 1.875rem;
    border-radius: 10px;
    position: relative;
    //left: 3.6rem;
    justify-content: center;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

#demo {
    //margin-left: 60px;
}


.icon-label {
    margin-left: 10px;
    vertical-align: text-top;
}

.info {
    color: var(--resourceprimarycolor);
    cursor: pointer;
}

.info:hover {
    color: var(--brandYellow);
}

.tool-row {
    justify-content: center;
    display: flex;
    align-items: center;
    margin-bottom: 20px;

}

.contentbox {
    background-color: var(--textBoxResourceListsbg);
    border-radius: var(--textBoxResourceLsborderRadius);
    width: 100%;
    position: relative;
    margin-bottom: 20px;
    height: 50%;
    padding: 20px 40px;
}

.mat-panel-title {
    color: var(--resourceprimarycolor);
    //font-size: 22px;
    font-size: 1.375rem;
    font-weight: 500;
    background-color: var(--textBoxResourceListsbg);
}

::ng-deep .mat-expansion-panel-header {
    height: 64px !important;
    background-color: var(--textBoxResourceListsbg);
    // margin-top: 6px;
    // height:auto !important;
}

::ng-deep .mat-expansion-panel-header:hover {
    background-color: var(--textBoxResourceListsbg);
    // margin-top: 6px;
    // height:auto !important;
}

:host::ng-deep .mat-checkbox-inner-container {
    //display: inline-block;
    //width: 24px;
    //height: 24px;
    //margin-top: 17px;
    margin: 23px 8px 0 auto;
    background-color: var(--textBoxResourceListsbg);
}



#arrow {
    //font-size: 50px;
    font-size: 3.125rem;
    width: 40px;
    color: #686868;
}

::ng-deep.mat-checkbox-layout {
    // width: 100%;
}

.alignRight {
    display: flex;
    margin-right: 40px;
    position: absolute;
    right: 0;
    justify-content: center;
}



.para-text {
    //font-size: 19px;
    font-size: 1.188rem;
    font-weight: 400;
    word-spacing: 2px;
    line-height: 30px;
    font-family: 'Roboto';
}

.button-group {
    position: relative;
    //    left: 8rem;
    margin: 25px 0 25px 0;
}

// .example-section{
//     margin-bottom: 10px;
// }

.contentbox2 {
    background-color: rgb(254, 254, 254);
    border-radius: 15px;
    width: 100%;
    position: relative;
    //left: 3.6rem;
    padding: 20px 40px;
    padding-bottom: 8px !important;
}

#paraContent {
    font-size: 1.125rem;
    font-weight: 400;
    color: #282828;
    font-family: var(--fontStyle);
    margin: 20px 0 20px 0;
    line-height: 1.875rem;
}

.paraContentText {
    padding: 5px 150px 0px 150px
}

.button-contentbox {
    border-radius: 15px;
    width: 100%;
    position: relative;
    //left: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.content-button {
    width: 140px;
    height: 50px;
    border-radius: 10px;
    font-size: 1.125rem;
    font-weight: 500;
    font-family: var(--fontStyle);
    background-color: var(--resourcebuttonBackgroundColor);
    color: var(--resourcebuttonfontcolor);
}

.content-button p {
    margin: 0;
}

.btn-close {
    justify-content: flex-end;
    display: flex;
    padding-right: 33px;
    padding-bottom: 33px;
}

.content-button:hover {
    background-color: var(--resourcebuttonBackgroundRollOverColor);
}

.box {
    margin-top: 20px;
}

.modal-dialog {
    display: flex;
    justify-content: center;
}

.modal-content {
    width: 70%;
}

.modal-contents {
    width: 100%;
}

.modal-footer {
    justify-content: center;
    padding: 10px;
    border: none;
}

.msg_button {
    padding: 10px 20px;
    //font-size: 22px;
    font-size: 1.375rem;
    border-radius: 7px;
}

#dynamicMail {
    resize: both;
    // overflow: auto;
    word-wrap: break-word;
}

.contentbox3 {
    background-color: rgb(254, 254, 254);
    // width: 90%;
    position: relative;
    // left: 3.6rem;
    top: 0.5rem;
    padding: 10px 0 0 0;
}

:host::ng-deep.mat-checkbox-layout {
    white-space: normal !important;
}


.scrolltotop {
    margin: 20px 0 20px 0;
    border: none;
    background: #fff;
}


.fixed-footer {
    width: 100%;
    color: white;
    background-color: var(--backgroundColor);
    padding: 0 2rem;
    transition: bottom 0.3s ease-in-out;
}

.botton-content {
    position: relative;
    top: 2rem;
    margin-bottom: 7rem;
}

.license {
    font-size: 0.9rem;
    position: relative;
    top: 1rem;
}

a:not([href]):not([class]) {
    color: var(--resourceLinkColor);

}


a:not([href]):not([class]):hover {
    color: var(--resourceLinkRolloverColor);
}

.header-padding {
    padding: 0 !important;
}


@media (min-width:1200px) {
    .container {
        //max-width: calc(100vw - 10vw);
        max-width: 1170px;
    }

    .header-margin {
        margin-left: -25px
    }

    #checkbox-mat-panel {
        max-width: 1000px !important;
    }

    .mat-expansion-panel {
        width: calc(100vw - 20vw);
        min-width: 800px;
    }

    .mat-checkbox-inner-container {
        margin: 17px 8px auto 0;
    }

}

@media (max-width: 1200px) {

    .container {
        position: relative;
        // top: -2rem;
        // left: 0;
        width: 100%;
        // padding-left: 0;
        // padding-right: 0;
        max-width: calc(100vw - 6vw) !important;
    }

    .mat-checkbox-inner-container {
        margin: 23px 8px auto 0;
    }

    .mat-expansion-panel {
        width: calc(100vw - 15vw);
    }

    #paraContent {
        font-size: 1rem;
        line-height: 1.75rem;
    }

    .main-content {
        position: relative;
    }

    .top-content {
        display: flex;
        justify-content: center;
        align-self: center;
        position: relative;
        //left: 1.2rem;
    }

    .HizentraLogo {
        position: relative;
        left: 0;
        display: flex;
        justify-content: center;
    }

    .fillable {
        //font-size: 15px;
        font-size: 0.938rem;
        left: 0;
    }

    :host::ng-deep.mat-expansion-panel-body {
        padding: 0;
    }

    .pdf-info {
        //font-size: 18px;
        color: var(--resourceHeaderColor) !important;
        font-size: 1.125rem;
        position: relative;
        left: 0rem;
        width: 100%;
        text-align: center;
    }

    .info {
        position: relative;
        left: 1rem;
    }

    .paraContentText {
        padding: 0
    }

    .contentbox2 {
        padding: 20px;
    }

    .brand-msg {
        position: relative;
        //left: 1rem;
        margin-top: 30px;
        border: none;
        //font-size: 15px;
        font-size: 0.938rem;
        font-weight: 400;
        height: 50px;
    }

    .tool-row {
        display: block;
        position: relative;
        //: 1rem;
    }

    ::ng-deep .mat-expansion-panel-header {
        //height: auto !important;
    }

    .alignRight {
        margin-right: 6px;
    }


    .second-tr {
        position: relative;
        left: 7px;
    }

    // .button-group{
    //     position: relative;
    //     left: 5.5rem;
    // }

    .contentbox {
        position: relative;
        //left: 1rem;
        padding: 20px;
    }

    .contentbox2 {
        position: relative;
        //left: 1rem;
    }

    .para-text {
        //font-size: 11px;
        // font-size: 0.688rem;
        // line-height: 1;
        font-size: 1rem;
        line-height: 1.75rem;
    }

    // .contentbox3{
    //     position: relative;
    //     left: 1rem;
    //     top: 1rem;
    // }

    .button-contentbox {
        display: block;
        position: relative;
        left: 0;
    }

    .modal-content {
        width: 100%;
    }


    .license {
        //font-size: 10px;
        font-size: .625rem;
    }

    .example-margin {
        //font-size: 14px;
        font-size: 0.875rem;
    }

    :host::ng-deep .mat-checkbox-inner-container {
        //display: inline-block;
        // width: 16px;
        //height: 16px;
        //margin-top: 17px;

    }

    #viewIcon {
        width: 12.5%;
        text-align: right;
        float: right;
    }

    #viewIcon img {
        width: 25px;
    }

    mat-panel-title {
        //font-size: 11px;
        //font-size: 0.688rem;
        font-size: 1rem;
        line-height: 1.75rem;
        //margin-top: 8px;
        //margin-right: 20% !important;

    }

    .mat-expansion-panel-header {
        padding: 0;
    }

    #arrow {
        //font-size: 31px;
        font-size: 1.938rem;
        //width: 25px;
        color: #686868;
        margin-top: 3px;
    }

    #demo {
        margin-left: 10px;
        //display: block;
    }

    #demo .col-2 {
        width: 50%;
    }

}

@media (max-width:280px) {
    mat-panel-title {
        //margin-top: 28px;
    }

    mat-panel-title h3 {
        font-size: 1.5rem !important;
        // line-height: 1rem;
        // margin-top: 8px;
    }

    .header-margin {
        margin-top: 20px;
    }

    .pdf-info {
        font-size: 0.6rem;
    }

    .HizentraLogo img {
        width: 100%;
        height: auto;
    }

    .brand-msg {
        font-size: 0.738rem !important;
        width: 100%;
    }

}

@media(max-width:480px) {
    .para {
        font-size: 1rem;
        width: 90% !important;
    }

    mat-panel-title h3 {
        font-size: 1.5rem !important;
        // line-height: 1rem;
        // margin-top: 8px;
    }

    .brand-msg {
        font-size: 0.838rem;
    }


    .mat-checkbox-inner-container {
        margin: 23px 8px auto 0;
    }
}

@media (max-width:576px) {
    .pdf-info {
        display: block;
    }

    .pdf-info h5 {
        color: var(--resourceHeaderColor) !important;
        content-visibility: hidden !important;
        visibility: hidden;
        padding-top: 10px;
        height: 10px;
    }

    .mat-checkbox-inner-container {
        margin: 23px 8px auto 0;
    }
}

@media (max-width:576px) {
    .container {
        max-width: calc(100vw - 10vw);
    }

    .para .brand-msg .contentbox .contentbox2 {
        width: 100% !important;
    }
}

@media (max-width:480px) {
    .mat-expansion-panel {
        width: calc(100vw - 20vw);
    }
}

@media (max-width:576px) {
    .mat-expansion-panel {
        width: calc(100vw - 120px) !important;
    }

    .mat-expansion-panel-header {
        height: auto !important;
        // margin-top: 6px;
        // height:auto !important;
    }
}

@media (max-width:768px) {
    .healthToolsres-content {
        margin: var(--healthToolsTitleMarginMobile) !important;
    }

    .top_heading_container-bg {
        padding: var(--topcontentpaddingMobile);
        margin: var(--topcontentmarginMobile);
    }

    .mat-expansion-panel {
        width: calc(100vw - 20vw);
    }

    .columnBlockcontainer {
        padding: var(--columnBlockPaddingMobile) !important;
    }

    .Emailbutton,
    .Printbutton,
    .CopyLinkbutton {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }

    .headerIsi {
        font-size: var(--fontSizeMobile) !important;
    }

    .mat-checkbox-inner-container {
        margin: 23px 8px auto 0;
    }

    .brandheaderleftcol {
        font-size: var(--brandheaderfontsizeMobile) !important;
    }

    .brandheaderleftcol2 {
        font-size: var(--brandheaderfontsizeMobile2) !important;
    }

    .Emailimage {
        width: var(--buttonWidthMobile) !important;
        height: var(--buttonHeightMobile) !important;
    }

    .Printimage {
        width: var(--buttonWidthMobile1) !important;
        height: var(--buttonHeightMobile1) !important;
    }

    .CopyLinkimage {
        width: var(--buttonWidthMobile2) !important;
        height: var(--buttonHeightMobile2) !important;
    }

    .headerIsitext {
        display: none;
    }

    .headerIsi {
        padding: var(--textBlockPaddingMobile) !important;
        margin: var(--headerIsimarginMobile) !important;
        line-height: var(--headerIsilineHeightMobile) !important;
        text-align: var(--headerIsialignmentMobile) !important;
        font-size: var(--headerIsifontSizeMobile) !important;
    }
}

@media (min-width:768px) {
    .headerIsitextmobile {
        display: none;
    }
}

@media (max-width:992px) {
    .mat-expansion-panel {
        width: calc(100vw - 20vw);
    }

    .mat-checkbox-inner-container {
        margin: 23px 8px auto 0;
    }
}

@media (max-width:1200px) {
    .mat-expansion-panel {
        width: calc(100vw - 20vw);
    }
}

.mat-expansion-indicator::after {
    height: 20px !important;
    width: 20px !important;
    font-size: larger !important;
}

.mat-expansion-indicator::after {
    transform: rotate(225deg) !important;
}

.searhlink {
    margin-left: auto
}

@media (max-width:480px) {
    .mat-panel-title {
        font-size: 12px !important;
        line-height: normal !important;
    }
}




.top-con-background {
    background: var(--gradientone);
    background: linear-gradient(60deg, var(--gradientone) 11%, var(--gradienttwo) 50%, var(--gradienthree) 100%);
}

#headgradient {
    background: var(--headergradientColor);
}

#footgradient {
    background: var(--resourceprimarycolor);
}

.paraA {
    color: var(--resourceLinkColor)
}

.indications {
    color: var(--headerIndicationFontColor)
}

.indications h4 {
    padding-top: 65px;
    margin-bottom: 10px;
    font-size: var(--headerIndicationHeaderFontSize);
}

.indications .indication-text {
    font-size: var(--headerIndicationCopyFontSize);
}

.indication-text p {
    margin-bottom: 0 !important;
}

.bottom-isi h4 {
    color: var(--isiHeadersColors);
    font-size: var(--isiHeadersFontSize);
    padding-top: 40px;
    padding-bottom: 20px;
    z-index: 1;
}

.header-link {
    color: var(--headerIndicationFontColor)
}

.isi-text {
    font-size: var(--isiTextFontSize);
    font-weight: var(--isiTextFontWeight);
    line-height: var(--isiTextLineHeight);
}

// .top-heading-width{
// }
.top_heading_container {
    width: var(--dyanamicMessagingWidth);
}

.headerpanel,
.top_heading_container-bg {
    width: 100% !important;
}

.dropdown-container {
    position: relative;
    display: inline-block;
}

.dropdown-header img {
    cursor: pointer;
}

.dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    background-color: transparent;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 160px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1;
    padding: 12px;
    right: 0;
    top: 100%;
}

.dropdown-content.show {
    display: block;
}

.dropdown-content span {
    display: block;
    padding: 8px;
    cursor: pointer;
}

.dropdown-content span:hover {
    background-color: #f5f5f5;
}

.dropdown-content1 {
    display: none;
    background-color: var(--dynamicMsgbg);
    z-index: 1;
    padding: 12px;
    right: 0;
    top: 100%;
}

.dropdown-content1.show {
    display: block;
}

.dropdown-content1 span {
    display: block;
    padding: 8px;
    cursor: pointer;
}

.dropdown-content1 span:hover {
    background-color: var(--dynamicMsgbg);
}


.dropdown-container2 {
    width: 100%;
    position: relative;
    display: inline-block;
}

.dropdown-content2 {
    display: none;
    width: 100%;
}

.dropdown-content2.show {
    display: block;
}

.dropdown-content2 span {
    display: block;
    padding: 8px;
    cursor: pointer;
}

.headinglist-content,
.headingMobilelist-content {
    margin-top: 1rem;
}

::ng-deep { 

    .brandheadertextContent p {
        margin-bottom: 0 !important;
        color: var(--linkFontColor);
    }

    .brandheadertextContent p a {
        color: var(--linkFontColor);
    }

    .brandheadertextContent p a:hover {
        color: var(--linkFontColor);
    }

    .brandheadertextContent2 p {
        margin-bottom: 0 !important;
        color: var(--textFontColor2);
    }

    .heading p {
        margin-bottom: 0 !important;
    }

    .headingMobile p {
        margin-bottom: 0 !important;
    }

    .headerIsi p a {
        color: var(--headerIsifontColor) !important;
    }

    .headerIsi p {
        // margin-bottom: 0 !important;
    }

    .generateEmailContent {
        margin-top: 5px;
        font-family: var(--rescopyFont);
        font-size: var(--rescopyFontSize);
        color: var(--rescopyFontColor);
    }

    .viewPrintSelectedResourcs {
        margin-top: 5px;
        font-family: var(--rescopyFont1);
        font-size: var(--rescopyFontSize1);
        color: var(--rescopyFontColor1);
    }

    .copySelectedResourceLinks {
        margin-top: 5px;
        font-family: var(--rescopyFont2);
        font-size: var(--rescopyFontSize2);
        color: var(--rescopyFontColor2);
    }

    .buttonsSectionBlockcecontent {
        text-align: var(--btnsec0textAlignment);
        font-size: var(--btnsec0fontSize);
        color: var(--btnsec0fontColor);
    }


    .mat-checkbox-label {
        font-size: var(--healthToolsTitleFontSize);
        font-weight: var(--healthToolsTitleFontWeight);
        color: var(--healthToolsTitleFontColor);
    }
}

.healthToolsres {
    font-size: var(--healthToolsTitleFontSize);
    font-weight: var(--healthToolsTitleFontWeight);
    color: var(--healthToolsTitleFontColor);
}

.dropdownBackground {
    background-color: var(--dropdownBackgroundColor);
    color: var(--dropdownFontColor);
    width: 185px;
    margin-top: 5px;
    text-align: left;
}

.dropdownBackground:hover {
    width: 185px;
    background-color: var(--dropdownLinkRolloverBackgroundColor);
    color: var(--dropdownFontColorRollover);
}

.headerIsi {
    width: var(--headerIsiwidth);
    padding: var(--textBlockPadding);
    margin: var(--headerIsimargin);
    line-height: var(--headerIsilineHeight);
    text-align: var(--headerIsialignment);
    font-size: var(--headerIsifontSize);
    color: var(--headerIsifontColor);
}

@media only screen and (min-width: 768px) {
    ::ng-deep {
        .mainIsiContentheading p {
            text-align: var(--mainIsiContenttextAlignment);
            font-size: var(--mainIsiContentfontSize);
            line-height: var(--mainIsiContentlineHeight);
            color: var(--mainIsiContentfontColor);
            padding: var(--mainIsiContenttextBlockPadding);
            margin: var(--mainIsiContenttextBlockMargin);
        }
    }
    .mainIsiContentheadingMobile{
        display: none;
    }
    .textBoxBelowHeaderIndication {
        text-align: var(--textBoxBelowHeaderalignment);
        font-size: var(--textBoxBelowHeaderfontSize);
        line-height: var(--textBoxBelowHeaderlineHeight);
        color: var(--textBoxBelowHeaderfontColor);
        margin: var(--textBlockMargin);
        padding: var(--textBlockHeaderPadding);
    }

    .textBoxAboveResourcecontent {
        text-align: var(--textBoxAboveRestextAlignment);
        font-size: var(--textBoxAboveResfontSize);
        color: var(--textBoxAboveResfontColor);
        margin: var(--textBoxAboveBlockMargin);
        padding: var(--textBoxAboveBlockPadding);
        line-height: var(--textBoxAboveReslineHeight);
    }

    .textBoxAboveResourcecontentMobile {
        display: none;
    }

    .textBoxBelowHeaderIndicationMobile {
        display: none;
    }

    .buttonsSectionBlockcecontent {
        text-align: var(--btnsec0textAlignment);
        font-size: var(--btnsec0fontSize);
        color: var(--btnsec0fontColor);
        margin: var(--btnsec0textBlockMargin);
        padding: var(--btnsec0textBlockPadding);
        line-height: var(--btnsec0lineHeight);
    }

    .buttonsSectionBlockcecontentMobile {
        display: none;
    }
}

@media only screen and (max-width: 768px) {
    .textBoxBelowHeaderIndicationMobile {
        font-size: var(--textBoxBelowHeaderfontSizemobile) !important;
        line-height: var(--textBoxBelowHeaderlineHeightmobile) !important;
        color: var(--textBoxBelowHeaderfontColor) !important;
        padding: var(--textBlockHeaderPaddingMobile) !important;
        margin: var(--textBlockMarginMobile) !important;
        text-align: var(--textBoxBelowHeaderalignmentMobile) !important;
    }

    .textBoxBelowHeaderIndication {
        display: none;
    }

    .resteaser {
        font-size: var(--healthToolsDescriptionFontSizeMobile) !important;
        margin-left: 27px !important;
    }

    .heading {
        display: none;
    }

    .headinglist {
        display: none !important;
    }

    .headingMobile {
        display: flex;
        align-items: center;
        text-align: var(--topheadingAlignmentMobile);
        font-size: var(--headingFontSizeMobile);
        color: var(--headingFontColor);
    }

    .headingMobilelist {
        text-align: var(--textAlignmentMobile);
        font-size: var(--textFontSizeMobile1);
        line-height: var(--textLineHeightMobile);
    }

    ::ng-deep.DynamicMessagecontent1 p {
        padding: var(--copyPaddingMobile1) !important;
        margin: var(--copyMarginMobile1) !important;
    }

    ::ng-deep.DynamicMessagecontent2 p {
        padding: var(--copyPaddingMobile2) !important;
        margin: var(--copyMarginMobile2) !important;
    }

    ::ng-deep.DynamicMessagecontent3 p {
        padding: var(--copyPaddingMobile3) !important;
        margin: var(--copyMarginMobile3) !important;
    }

    ::ng-deep.DynamicMessagecontent4 p {
        padding: var(--copyPaddingMobile4) !important;
        margin: var(--copyMarginMobile4) !important;
    }

    .DynamicMessagecontent {
        font-size: var(--copyFontSizeMobile) !important;
    }

    .DynamicMessagecontent1 {
        font-size: var(--copyFontSizeMobile1) !important;
        text-align: var(--copyTextAlignMobile1) !important;
        line-height: var(--copyLineHeightMobile1) !important;
    }

    .DynamicMessagecontent2 {
        font-size: var(--copyFontSizeMobile2) !important;
        text-align: var(--copyTextAlignMobile2) !important;
        line-height: var(--copyLineHeightMobile2) !important;
    }

    .DynamicMessagecontent3 {
        font-size: var(--copyFontSizeMobile3) !important;
        text-align: var(--copyTextAlignMobile3) !important;
        line-height: var(--copyLineHeightMobile3) !important;
    }

    .DynamicMessagecontent4 {
        font-size: var(--copyFontSizeMobile4) !important;
        text-align: var(--copyTextAlignMobile4) !important;
        line-height: var(--copyLineHeightMobile4) !important;
    }

    .textBoxAboveResourcecontentMobile {
        font-size: var(--textBoxAboveResfontSizeMobile);
        text-align: var(--textBoxAboveRestextAlignmentMobile);
        line-height: var(--textBoxAboveReslineHeightMobile);
        margin: var(--textBoxAboveBlockMarginMobile);
        padding: var(--textBoxAboveBlockPaddingMobile);
    }

    .textBoxAboveResourcecontent {
        display: none;
    }

    .searhlink .viewIconImgsec,
    .expandIconsec {
        width: 50px !important;
    }

    .buttonsSectionBlockcecontentMobile {
        font-size: var(--btnsec0fontSizeMobilet);
        text-align: var(--btnsec0textAlignmentMobile);
        line-height: var(--btnsec0lineHeightMobile);
        margin: var(--btnsec0textBlockMarginMobile);
        padding: var(--btnsec0textBlockPadding);
    }

    .buttonsSectionBlockcecontent {
        display: none;
    }

    ::ng-deep {
        .mat-checkbox-inner-container {
            width: 20px !important;
            height: 20px !important;
            margin: 0px 10px 0px auto !important;
        }

        .generateEmailContent {
            font-size: var(--rescopyFontSizeMobile);
        }

        .viewPrintSelectedResourcs {
            font-size: var(--rescopyFontSizeMobile1);
        }

        .copySelectedResourceLinks {
            font-size: var(--rescopyFontSizeMobile2);
        }

        .buttonsSectionBlockcecontent {
            font-size: var(--btnsec0fontSizeMobilet);
        }

        .mat-checkbox-label {
            font-size: var(--healthToolsTitleFontSizeMobile);
        }
    }

    .healthToolsres {
        font-size: var(--healthToolsTitleFontSizeMobile);
    }

    .expandIconPopUpContent {
        font-size: var(--expandIconfontSizeMobile);
    }

    .previewIconPopUpContent {
        font-size: var(--previewIconPopUpContentfontSizeMobile);
    }

    .checkboxSelectPopUpContent {
        font-size: var(--checkboxSelectPopUpContentfontSizeMobile);
    }

    .footerres {
        text-align: var(--footerrestextAlignmentMobile);
    }

    .isi-text {
        font-size: 12px;
    }

    .imgcsl {
        width: 100%;
    }

    .mainIsiHeader {
        font-size: var(--mainIsiHeaderFontSizeMobile) !important;
    }

    ::ng-deep {
        .mainIsiContentheadingMobile p {
            text-align: var(--mainIsiContenttextAlignment);
            font-size: var(--mainIsiContentfontSizeMobile) !important;
            line-height: var(--mainIsiContentlineHeightMobile) !important;
            color: var(--mainIsiContentfontColor);
            padding: var(--mainIsiContenttextBlockPaddingMobile) !important;
            margin: var(--mainIsiContenttextBlockMarginMobile) !important;
        }
    }

    .mainIsiContentheading {
        display: none;
    }

    .mainIsiContenttext {
        font-size: var(--mainIsiContentfontSizeMobile1) !important;
        line-height: var(--mainIsiContentlineHeightMobile1) !important;
    }

    .mainIsiContentheading1 {
        font-size: var(--mainIsiContentfontSizeMobile2) !important;
        line-height: var(--mainIsiContentlineHeightMobile2) !important;
    }

    .mainIsiContenttext1 {
        font-size: var(--mainIsiContentfontSizeMobile3) !important;
        line-height: var(--mainIsiContentlineHeightMobile3) !important;
    }

    .mainIsiContentheading2 {
        font-size: var(--mainIsiContentfontSizeMobile4) !important;
        line-height: var(--mainIsiContentlineHeightMobile4) !important;
    }

    .mainIsiContenttext2 {
        font-size: var(--mainIsiContentfontSizeMobile5) !important;
        line-height: var(--mainIsiContentlineHeightMobile5) !important;
    }

    .ResourceLogo {
        width: var(--branddiseaselogoWidthMobile) !important;
        height: var(--branddiseaselogoHeightMobile) !important;
    }

    .expandIcon,
    .viewIconImg {
        width: var(--branddiseaseresWidthMobile) !important;
        height: var(--branddiseaseresHeightMobile) !important;
    }

    .customButtonIcon {
        width: var(--customButtonIconWidthMobile) !important;
        height: var(--customButtonIconHeightMobile) !important;
    }

    .customButtonIcon1 {
        width: var(--customButtonIconWidthMobile1) !important;
        height: var(--customButtonIconHeightMobile1) !important;
    }

    .customButtonIcon2 {
        width: var(--customButtonIconWidthMobile2) !important;
        height: var(--customButtonIconHeightMobile2) !important;
    }

    .brandheaderimageurl2 {
        width: var(--brandheaderimageurl2WidthMobile) !important;
        height: var(--brandheaderimageurl2HeightMobile) !important;
    }

    .dynamicicon {
        width: var(--iconWidthMobile) !important;
        height: var(--iconHeightMobile) !important;
    }

    .DynamicMessageicon {
        width: var(--branddiseaseiconWidthMobile) !important;
        height: var(--branddiseaseiconHeightMobile) !important;
    }

    .brandheaderimageurl {
        width: var(--brandheaderimageurlWidthMobile) !important;
        height: var(--brandheaderimageurlHeightMobile) !important;
    }
}




.resteaser {
    max-width: 80%;
    font-size: var(--healthToolsDescriptionFontSize);
    color: var(--healthToolsDescriptionFontColor);
    margin-left: 34px;
    margin-top: 5px;
    font-weight: 400;
}

@media only screen and (min-width: 600px) {
    .headingMobile {
        display: none;
    }

    .headingMobilelist {
        display: none !important;
    }

    .heading {
        display: flex;
        align-items: center;
        text-align: var(--topheadingAlignment);
        font-size: var(--headingFontSize);
        color: var(--headingFontColor);
    }

    .headinglist {
        text-align: var(--textAlignment);
        font-size: var(--textFontSize1);
        line-height: var(--textLineHeight);
    }

    ::ng-deep {

        .mat-checkbox-inner-container {
            width: 25px !important;
            height: 25px !important;
            margin: 0px 10px 0px auto !important;
        }
    }
}

.dynamicMsgsec {
    background-color: var(--dynamicMsgbg);
    border-radius: var(--borderRadiusRoundedCorners);
    padding: var(--massecpadding);
}

.iconBackgroundColor {
    background-color: var(--iconBackgroundColor);
    border-radius: var(--iconBackgroundBorderRadius);
}


.DynamicMescol {
    display: flex;
    justify-content: space-between;
}


.viewIconImg {
    border-radius: 50%;
    background-color: var(--ResiconBackgroundColor);
}

.viewIconImg:hover {
    background-color: var(--ResiconRolloverBackgroundColor);
}

.expandIcon {
    border-radius: 50%;
    background-color: var(--ResiconBackgroundColor);
}

.expandIcon:hover {
    background-color: var(--ResiconRolloverBackgroundColor);
}

.searhlink {
    display: flex;
    justify-content: flex-start;
    text-align: center;
}

.searhlink .viewIconImgsec,
.expandIconsec {
    width: 80px;
}

.Emailimage {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--customButtonBackgroundColor);
    border-radius: var(--buttonBorderRadius);
    padding: var(--buttonPadding);
    width: var(--buttonWidth);
    height: var(--buttonHeight);
}

.Emailimage:hover {
    background-color: var(--customButtonBackgroundRolloverColor);
}

.Printimage {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--customButtonBackgroundColor1);
    border-radius: var(--buttonBorderRadius1);
    padding: var(--buttonPadding1);
    width: var(--buttonWidth1);
    height: var(--buttonHeight1);
}

.Printimage:hover {
    background-color: var(--customButtonBackgroundRolloverColor1);
}

.CopyLinkimage {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--customButtonBackgroundColor2);
    border-radius: var(--buttonBorderRadius2);
    padding: var(--buttonPadding2);
    width: var(--buttonWidth2);
    height: var(--buttonHeight2);
}

.CopyLinkimage:hover {
    background-color: var(--customButtonBackgroundRolloverColor2);
}

.top_heading_container-bg {
    background-color: var(--heading_backgroundColor);
    padding: var(--copypadding);
    margin: var(--copymargin);
}

.expandIconPopUpContent {
    position: absolute;
    /* Position it relative to the parent */
    background-color: var(--expandIconbackgroundColor);
    border-radius: var(--expandIconborderRadius);
    padding: var(--expandIcontextBlockPadding);
    color: var(--expandIconfontColor);
    width: var(--expandIconwidth);
    text-align: var(--expandIcontextAlignment);
    font-size: var(--expandIconfontSize);
    z-index: 1000;
    /* Ensure it appears above other elements */
    display: none;
    /* Initially hidden */
    margin-left: -20px;
    margin-top: -22px;
}

.expandIconsec:hover .expandIconPopUpContent {
    display: block;
    /* Show on hover */
}

.previewIconPopUpContent {
    position: absolute;
    /* Position it relative to the parent */
    background-color: var(--previewIconPopUpContentbackgroundColor);
    border-radius: var(--previewIconPopUpContentborderRadius);
    padding: var(--previewIconPopUpContenttextBlockPadding);
    color: var(--previewIconPopUpContentfontColor);
    width: var(--previewIconPopUpContentwidth);
    text-align: var(--previewIconPopUpContenttextAlignment);
    font-size: var(--previewIconPopUpContentfontSize);
    z-index: 1000;
    /* Ensure it appears above other elements */
    display: none;
    /* Initially hidden */
    margin-left: -20px;
    margin-top: -22px;
}

.viewIconImgsec:hover .previewIconPopUpContent {
    display: block;
    /* Show on hover */
}

.checkboxSelectPopUpContent {
    position: absolute;
    background-color: var(--checkboxSelectPopUpContentbackgroundColor);
    border-radius: var(--checkboxSelectPopUpContentborderRadius);
    padding: var(--checkboxSelectPopUpContenttextBlockPadding);
    color: var(--checkboxSelectPopUpContentfontColor);
    width: var(--checkboxSelectPopUpContentwidth);
    text-align: var(--checkboxSelectPopUpContenttextAlignment);
    font-size: var(--checkboxSelectPopUpContentfontSize);
    z-index: 1000;
    /* Ensure it appears above other elements */
    display: none;
    /* Initially hidden */
    margin-left: -25px;
    margin-top: -10px;
}

.healthResourceTitle {
    display: flex;
    align-items: var(--healthToolsIconsVerticalAlignment);
}

.mat_checkbox {
    margin-bottom: 3px;
}

.mat_checkbox:hover .checkboxSelectPopUpContent {
    display: block;
    /* Show on hover */
}

.footerres {
    width: fit-content !important;
    text-align: var(--footerrestextAlignment);
}

.footerres-container {
    display: flex;
    justify-content: var(--copyrightfooterContentAlignment);
}

.mainIsi {
    margin: var(--mainIsiMargin);
    padding: var(--mainIsiPadding);
}

.mainIsiHeader {
    font-size: var(--mainIsiHeaderFontSize);
    font-weight: var(--mainIsiHeaderFontWeight);
    color: var(--mainIsiHeaderFontColor);
}

.mainIsiContentheading, .mainIsiContentheadingMobile {
    width: var(--mainIsiContentwidth);
}

.mainIsiContenttext {
    text-align: var(--mainIsiContenttextAlignment1);
    font-size: var(--mainIsiContentfontSize1);
    line-height: var(--mainIsiContentlineHeight1);
    color: var(--mainIsiContentfontColor1);
    padding: var(--mainIsiContenttextBlockPadding1);
    margin: var(--mainIsiContenttextBlockMargin1);
    width: var(--mainIsiContentwidth1);
}

.mainIsiContentheading1 {
    text-align: var(--mainIsiContenttextAlignment2);
    font-size: var(--mainIsiContentfontSize2);
    line-height: var(--mainIsiContentlineHeight2);
    color: var(--mainIsiContentfontColor2);
    padding: var(--mainIsiContenttextBlockPadding2);
    margin: var(--mainIsiContenttextBlockMargin2);
    width: var(--mainIsiContentwidth2);
}

.mainIsiContenttext1 {
    text-align: var(--mainIsiContenttextAlignment3);
    font-size: var(--mainIsiContentfontSize3);
    line-height: var(--mainIsiContentlineHeight3);
    color: var(--mainIsiContentfontColor3);
    padding: var(--mainIsiContenttextBlockPadding3);
    margin: var(--mainIsiContenttextBlockMargin3);
    width: var(--mainIsiContentwidth3);
}

.mainIsiContentheading2 {
    text-align: var(--mainIsiContenttextAlignment3);
    font-size: var(--mainIsiContentfontSize3);
    line-height: var(--mainIsiContentlineHeight3);
    color: var(--mainIsiContentfontColor3);
    padding: var(--mainIsiContenttextBlockPadding3);
    margin: var(--mainIsiContenttextBlockMargin3);
    width: var(--mainIsiContentwidth3);
}

.mainIsiContenttext2 {
    text-align: var(--mainIsiContenttextAlignment5);
    font-size: var(--mainIsiContentfontSize5);
    line-height: var(--mainIsiContentlineHeight5);
    color: var(--mainIsiContentfontColor5);
    padding: var(--mainIsiContenttextBlockPadding5);
    margin: var(--mainIsiContenttextBlockMargin5);
    width: var(--mainIsiContentwidth5);
}

.dynamicicon {
    width: var(--iconWidth);
    height: var(--iconHeight);
}

.headinglist-icon {
    background-color: var(--heading_iconBackgroundColor);
    border-radius: var(--heading_iconRadius);
    margin: var(--iconMargin);
    padding: var(--iconPadding);
}

.DynamicMessageicon {
    width: var(--branddiseaseiconWidth);
    height: var(--branddiseaseiconHeight);
    padding: var(--branddiseaseiconPadding);
    margin: var(--branddiseaseiconMargin);
}

.ResourceLogo {
    width: var(--branddiseaselogoWidth);
    height: var(--branddiseaselogoHeight);
    padding: var(--branddiseasepadding);
    margin: var(--branddiseasemargin);
}

.expandIcon,
.viewIconImg {
    width: var(--branddiseaseresWidth);
    height: var(--branddiseaseresHeight);
    padding: var(--branddiseaseresiconPadding);
    margin: var(--branddiseaseresiconMargin);
}

.customButtonIcon {
    width: var(--customButtonIconWidth);
    height: var(--customButtonIconHeight);
}

.customButtonIcon1 {
    width: var(--customButtonIconWidth1);
    height: var(--customButtonIconHeight1);
}

.customButtonIcon2 {
    width: var(--customButtonIconWidth2);
    height: var(--customButtonIconHeight2);
}

.brandheaderimageurl2 {
    width: var(--brandheaderimageurl2Width);
    height: var(--brandheaderimageurl2Height);
    padding: var(--imagePadding2);
    margin: var(--imageMargin2);
}

.brandheaderimageurl {
    width: var(--brandheaderimageurlWidth);
    height: var(--brandheaderimageurlHeight);
    padding: var(--imagePadding);
    margin: var(--imageMargin);
}


.DynamicMessagecontent {
    font-family: var(--copyFont);
    color: var(--copyFontColor);
    font-size: var(--copyFontSize);
    text-align: var(--copyTextAlign);
    line-height: var(--copyLineHeight);
}

.DynamicMessagecontent1 {
    font-family: var(--copyFont1);
    color: var(--copyFontColor1);
    font-size: var(--copyFontSize1);
    text-align: var(--copyTextAlign1);
    line-height: var(--copyLineHeight1);
}

.DynamicMessagecontent2 {
    font-family: var(--copyFont2);
    color: var(--copyFontColor2);
    font-size: var(--copyFontSize2);
    text-align: var(--copyTextAlign2);
    line-height: var(--copyLineHeight2);
}

.DynamicMessagecontent3 {
    font-family: var(--copyFont3);
    color: var(--copyFontColor3);
    font-size: var(--copyFontSize3);
    text-align: var(--copyTextAlign3);
    line-height: var(--copyLineHeight3);
}

.DynamicMessagecontent4 {
    font-family: var(--copyFont4);
    color: var(--copyFontColor4);
    font-size: var(--copyFontSize4);
    text-align: var(--copyTextAlign4);
    line-height: var(--copyLineHeight4);
    max-width: 700px;
}

::ng-deep.DynamicMessagecontent p {
    padding: var(--copyPadding) !important;
    margin: var(--copyMargin) !important;
}

::ng-deep.DynamicMessagecontent1 p {
    padding: var(--copyPadding1);
    margin: var(--copyMargin1);
}

::ng-deep.DynamicMessagecontent2 p {
    padding: var(--copyPadding2);
    margin: var(--copyMargin2);
}

::ng-deep.DynamicMessagecontent3 p {
    padding: var(--copyPadding3);
    margin: var(--copyMargin3);
}

::ng-deep.DynamicMessagecontent4 p {
    padding: var(--copyPadding4);
    margin: var(--copyMargin4);
}

.columnBlockcontainer {
    display: flex;
    padding: var(--columnBlockPadding);
}

.button-group {
    display: flex;
}

.Emailbutton,
.Printbutton,
.CopyLinkbutton {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.brandheaderleftcol {
    width: 50%;
    font-size: var(--brandheaderfontsize);
    text-align: var(--brandheadertextAlignment);
    display: flex;
    align-items: center;
}

.brandheaderleftcol2 {
    width: 50%;
    font-size: var(--brandheaderfontsize2);
    text-align: var(--brandheadertextAlignment2);
}

.DynamicMessagecontiner {
    width: 90%;
}

.DynamicMessagecontiner1 {
    width: 10%;
}

.healthToolsres-content {
    display: block;
    margin: var(--healthToolsTitleMargin);
}

::ng-deep .headerIsitext p{
    margin: 0 !important ;
}
::ng-deep .headerIsitextmobile p{
    margin: 0 !important ;
}