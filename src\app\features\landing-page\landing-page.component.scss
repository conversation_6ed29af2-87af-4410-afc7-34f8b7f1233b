.container{
    //padding: 0
}

.container-fluid{
    //position: relative;
    // left: 1.5rem;
    // top: 3rem;
    width: 100vw; /* make it 100% of the viewport width (vw) */
    margin-left: calc((100% - 100vw) / 2);  /* then remove the gap to the left of the container with this equation */
    padding: 0 !important
}

a{
    color: var(--resourceprimarycolor);    
}

a:hover{
    color: var(--resourcesecondarycolor);
    //font-weight: 500;
}

a, u {
    text-decoration: none;
  }

.para{
    position: relative;
    left: 1rem;
    top: 0.5rem;
}

.copyicon{
    position: relative;
    top: -1rem;
}

.pdf-info{
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    position: relative;
    text-align: center;
    // left: -2rem;
    // padding-top: 22px;
}
.HizentraLogo{
    position: relative;
    left: 0rem;
    margin-top: 20px;
    margin-bottom: 20px;
}

.top-content-button{
    border: none;
    background-color: var(--resourceprimarycolor);
    color: var(--resourcebuttonfontcolor);
    border-radius: 6px;
    padding: 11px 50px;
    font-size: 16px;
}

.ulHead{
    font-size: 1.125rem;
    font-family: var(--fontStyle);
    font-weight: 400;
    color: #282828;
    line-height: 1.875rem;
}

ul li {
    font-size:1.125rem ;
    font-weight:400 ;
    font-family: var(--fontStyle);
    line-height: 1.875rem;
    color: #282828;
}

#foot_links {
    /* styles for the host element go here */
    :host {
        a{
            color: white 
        
        }
    }
    
    /* styles for child elements go here */
    ::ng-deep {
        a{
            color: white 
        
        }
    }
  }

:host::ng-deep a{
    color: var(--resourceprimarycolor);   
    cursor: pointer; 
    text-decoration: none;
}

:host::ng-deep a:hover{
    color: var(--resourcesecondarycolor);
    //font-weight: bold;
    cursor: pointer; 
}

.captchaclass{
    text-align: center;
}
.captchadiv{
    display: inline-block;
}


.top-content-button:hover{
    background-color: var(--resourcebuttonBackgroundRollOverColor);
    color: var(--resourcebuttonhoverfontcolor)
}

.info{
    color: var(--resourceprimarycolor);
}

.info:hover{
    color: var(--resourcesecondarycolor);
    //font-weight: 500;
}

.tool-row{
    justify-content: center;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    
}

.contentbox{
    border-top: 3px solid var(--resourcehorizontalRuleColor);
    border-bottom: 3px solid var(--resourcehorizontalRuleColor);
    position: relative;
    height: 50%;
    padding: 0px 40px 40px 40px;;
    
    //background-color: var(--backgroundColor);
}

.isi-text{
    font-size: var(--isiTextFontSize);
}

.qsa-sample{
    max-width: 100% ;
}

.box{
    margin-bottom: 5rem;
    position: relative;
    left: -33px;
    top: 2rem;
    // width: 85%;
   //height: 375px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);
    padding: 30px 40px 30px 40px;
    border-radius: 11px; 
    margin-left: 2rem;
}

ul li{
    margin-top: 10px;
}

.from-buttom{
    background-color: var(--resourceprimarycolor);
    color:  var(--resourcebuttonfontcolor);
    border: none;
    font-family: "Roboto", Sans-serif;
    font-size: 15px;
    font-weight: 400;
    border-radius: 5px 5px 5px 5px;
    padding: 11px 50px;
}
.from-buttom:hover{
    background-color: var(--resourcebuttonBackgroundRollOverColor);
    color: var(--resourcebuttonhoverfontcolor)
}


.medicines {
    margin-top: 17px;
    //font-size: 1.125rem;
    font-size: var(--bodyTextFontSize);
    font-weight: var(--bodyTextFontWeight);
    line-height: var(--bodyTextLineHeight);
    font-family: var(--fontStyle);
}

.example-section{
    margin-bottom: 10px;
}



.button-contentbox{
    border-radius: 15px;
    width: 90%;
    position: relative;
    left: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.content-button{
    width: 140px;
    height: 50px;
    border-radius: 10px;
    font-size: medium;
}

.content-button:hover{
    background-color: var(--resourcebuttonBackgroundRollOverColor);
    color: var(--resourcebuttonhoverfontcolor)
}

.contentbox3{
    background-color: rgb(254, 254, 254);
    position: relative;
    //padding: 10px 0 0 0;
    padding-left: 3%;
    padding-right: 3%;
    z-index: 1;
}

.scrolltotop{
    margin: 20px 0 20px 0;
    border: none;
    background: #fff;
}


.fixed-footer {
    width: 100%;
    color: white;
    background-color: #2c3e50;
    padding: 0 2rem;
    transition: bottom 0.3s ease-in-out;
}

.botton-content{
    position: relative;
    top: 2rem;
    margin-bottom: 7rem;
}

.mainContent {
    font-size: var(--bodyTextFontSize);
    font-weight: var(--bodyTextFontWeight);
    line-height: var(--bodyTextLineHeight);
}

.license{
    font-size: 0.9rem;
    position: relative;
    top: 1rem;
}

.request-input{
    position: relative;
    top: 0.5rem;
    margin: 5px 0 25px 0;
}

.request-button{
    width: auto;
    padding: 12px;
    position: relative;
    background-color: var(--resourceprimarycolor);
    color: var(--resourcebuttonfontcolor);
    border: none;
    border-radius: 5px 5px 5px 5px;
}

.request-button:hover{
    background-color: var(--resourcebuttonBackgroundRollOverColor);
    color: var(--resourcebuttonhoverfontcolor)
}


::ng-deep .mat-horizontal-stepper-header{
    pointer-events: none !important;
    display: none;
}


// @media (max-width:768px){
//     .qsa-sample{
//         width: 90%;
//     }
// }



@media (max-width:768px){

    .container{
        position: relative;
        width: 100%;
        padding-left: 0;
        padding-right: 0;
    }

    .main-content{
        position: relative;
    }

    .top-content{
        display: flex;
        justify-content: center;
        align-self:center;
        // position: relative;
        // left: 1.2rem;
    }

    .HizentraLogo{
        position: relative;
        left: 0;
        justify-content: center;
        display: flex;
    }


    .pdf-info{
        font-size: 16px;
        position: relative;
        left: 0.3rem;
    }

    .info{
        position: relative;
        left: 1rem;
    }

    

    .tool-row{
        display: block;
        position: relative;
        left: 1rem;
    }

    .second-tr{
        position: relative;
        left: 7px;
    }

    .contentbox{
        position: relative;
        top: 2rem;
        left: 0rem;
        padding: 20px;
        width: 100%;
    }

    .contentbox2{
        position: relative;
        left: 1rem;
    }

    .button-contentbox{
        display: block;
        position: relative;
        left: 0;
    }

    // #captchaElem{
    //     position: relative;
    //     left: 1.15rem;
    //     width: 84%;
    // }

    .license{
        font-size: 10px;
    }

    .box{
        height: auto;
        // width: 106%;
        margin-left: 0;
        position: relative;
        left: 0px;
        padding: 25px;
    }

    // .request-button{
    //     position: relative;
    //     left: 2rem;
    // }
}


@media (max-width:576px) {
    .pdf-info{
        display: block;
    }
    .pdf-info h5{
        content-visibility: hidden !important;

        visibility: hidden;
        padding-top: 10px;
        height: 10px;
    }
}

@media (max-width:480px) {
    #captcha_custom{
        transform: scale(0.77);
    }
}

@media (max-width:280px) {
    #captcha_custom{
        transform: scale(0.60);
    }
}

@media (min-width:1200px) {
    .container{
        max-width: 1170px !important;
    }
}

#headgradient{
    background: var(--headergradientColor);
}
#footgradient{
    background: var(--resourceprimarycolor);
}

.paraA{
    color: var(--resourceLinkColor)
}

.bottom-isi h4{
    color: var(--isiHeadersColors);
    font-size: var(--isiHeadersFontSize);
    padding-top: 20px;
    padding-bottom: 10px;
}
.header-link{
    color: var(--headerIndicationFontColor)
}

@supports (-webkit-overflow-scrolling: touch) {
    /* CSS specific to iOS devices */
    input, textarea {
        transform: translateZ(0px) !important;
    }
}

.reference{
    margin-top: 40px;
    color: #778288;
    font-size: .85rem;
    font-weight: 400;
}

.copyBlockFootnotes{
    font-size: .75rem;
    line-height: 1rem;
     font-weight: 400;
     margin-top: 20px;
}

.isi-text{
    font-size: var(--isiTextFontSize);
    font-weight: var(--isiTextFontWeight);
    line-height: var(--isiTextLineHeight);
}

.contentheadingText{
    font-size: var(--contentheadingFontSize);
    text-align: var(--contentheadingAlignment);
}

.contentBelowText{
    font-size: var(--contenttextFontSize);
    text-align: var(--contenttextAlignment);
}

.contentBelowrefText{
    font-size: var(--contenttextReferencesFontSize);
    text-align: var(--contenttextReferencesAlignment);
}

.video-container{
    position: relative;
    margin-bottom: 20px;
    margin-top: 30px;
}




::ng-deep iframe{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

.video-wrapper{
    position: relative;
    width: 100%;
    overflow: hidden;
    padding-top: 56.25%;
}