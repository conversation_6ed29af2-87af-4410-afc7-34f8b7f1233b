.containers{
    // padding: 33px;
    // width: 11%;
    //position: fixed;
    //right: 0;
    //width: 7%;
    //position: -webkit-sticky;
    //bottom: 0;
   //margin: 0px 27px 155px 0px;
    //top: calc(100vh - 30%);
    //left: calc(100vw - 15%);

    position: fixed;
    width: 10%;
    //position: -webkit-sticky;
    top: calc(100vh - 26%) ;
    left: calc(100vw - 15%) ;
    z-index: 3;
    -webkit-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    
}

img{
    //width: 200% ;
    width: 200px;
}

/*@media (max-width: 768px) 
{
    .containers{
    top: calc(100vh - 40%);
    }
}

@media (max-width: 992px) 
{
    .containers{
    top: calc(100vh - 40%);
    }
}
@media (max-width: 576px) 
{
    .containers{
    top: calc(100vh - 45%);
    }
}
@media (max-width: 452px) 
{
    .containers{
    top: calc(100vh - 50%);
    }
} */

.gina{
    clear:both;
    display: flex;
    justify-content: flex-end;
}

@media (max-width:567px) {
    img{
        //width: 500% !important;
        width: 150px;
    }
}

@media (max-width:992px) {
    img{
        //width: 350% ;
        width: 200px;
    }
}

// @media (min-width:768px) and (max-width:1198px) {
//     .container{
//         width: 19%;
//     }
// }