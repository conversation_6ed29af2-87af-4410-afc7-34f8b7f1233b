<div class="main row">
  <div style="color: var(--primaryColor);">
    <h2>{{drugname}}</h2>
  </div>
  
  <div class="row">
   <!-- <div class="col">
    <form class="example-form">
      <mat-form-field class="example-full-width" appearance="fill">
        <mat-label>Number</mat-label>
        <input type="text"
               placeholder="Pick one"
               aria-label="Number"
               matInput
               [formControl]="myControl"
               [matAutocomplete]="auto">
        <mat-autocomplete #auto="matAutocomplete">
          <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
            {{option}}
          </mat-option>
        </mat-autocomplete>
      </mat-form-field>
    </form>
   </div> -->
    
   <!-- <div class="col-4">
    <form class="example-form">
      <select class="select">
          <option class="option" *ngFor="let item of this.medList;let i = index" value="{{item.code}}" [selected]="i == 0">
            {{item.name}}
          </option>
        </select>
    </form>
   </div>

   <div class="col-4">
    <form class="example-form">
      <select class="select">
          <option *ngFor="let item of this.medGram;let i = index" value="{{item.name}}">
            {{item.name}}
          </option>
        </select>
    </form>
   </div>

   <div class="col-4">
    <form class="example-form">
      <select class="select">
          <option *ngFor="let item of this.medQty;let i = index" value="{{item.code}}" [selected]="i == 0">
            {{item.name}}
          </option>
        </select>
    </form>
   </div> -->
  </div>
</div>

<div class="container">
  <div class="tablets" *ngFor="let data of groupArr; let i = index">
    <div class="row">
      <div class="col-md col-sm">
        <h3 class="head" id="pharmacyname">{{data?.groupItem[0]?.pharmacyName}}</h3>
        <p>{{data?.groupItem[0]?.address}} {{data?.groupItem[0]?.city}} {{data?.groupItem[0]?.state}} </p>
      </div>
      <div class=" col-md col-sm paymentContent">
       <div class="row">
        <div class=" col-md-12 col-sm-6">
          <h3 class="float-right miniHead">You Pay</h3>
        </div>
        <div class=" col-md-12 col-sm-6">
          <p class="float-right" style="font-size: 25px; font-weight: 500;color: var(--primaryColor);">${{data?.groupItem[0]?.price | number : '1.2-2'}}</p>
        </div>
       </div>
      </div>
        <div class="col-md-4 col-sm-4 raisedBtn">        
            <button mat-raised-button class="disBtn" (click)="discount(data?.groupItem[0]?.pharmacyName)" color="primary">Get Discount</button>
      </div>
    </div>
    <div>
      <div *ngFor="let groupData of data?.groupItem  | slice:0:2;let j = index">
        <mat-accordion *ngIf="j !=0">
          <mat-expansion-panel (opened)="panelOpenState = true"
                               (closed)="panelOpenState = false">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <div>
                    <p >More Locations</p>
                  </div>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div *ngFor="let groupData of data?.groupItem;let j = index">
               <p *ngIf="j !=0"> {{groupData.address}} {{groupData.city}} {{groupData.state}} </p>
              </div>
                <!-- <p>{{data?.groupItem[0]?.address}} {{data?.groupItem[0]?.city}} {{data?.groupItem[0]?.state}} </p> -->
              </mat-expansion-panel>
         </mat-accordion>
    </div>
    </div>
  </div>
</div>

<!-- <div class="secondcontainer">
  <div class="secondBox">
    <h4 style="display: flex;"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-shop" viewBox="0 0 16 16">
      <path d="M2.97 1.35A1 1 0 0 1 3.73 1h8.54a1 1 0 0 1 .76.35l2.609 3.044A1.5 1.5 0 0 1 16 5.37v.255a2.375 2.375 0 0 1-4.25 1.458A2.371 2.371 0 0 1 9.875 8 2.37 2.37 0 0 1 8 7.083 2.37 2.37 0 0 1 6.125 8a2.37 2.37 0 0 1-1.875-.917A2.375 2.375 0 0 1 0 5.625V5.37a1.5 1.5 0 0 1 .361-.976l2.61-3.045zm1.78 4.275a1.375 1.375 0 0 0 2.75 0 .5.5 0 0 1 1 0 1.375 1.375 0 0 0 2.75 0 .5.5 0 0 1 1 0 1.375 1.375 0 1 0 2.75 0V5.37a.5.5 0 0 0-.12-.325L12.27 2H3.73L1.12 5.045A.5.5 0 0 0 1 5.37v.255a1.375 1.375 0 0 0 2.75 0 .5.5 0 0 1 1 0zM1.5 8.5A.5.5 0 0 1 2 9v6h1v-5a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v5h6V9a.5.5 0 0 1 1 0v6h.5a.5.5 0 0 1 0 1H.5a.5.5 0 0 1 0-1H1V9a.5.5 0 0 1 .5-.5zM4 15h3v-5H4v5zm5-5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-3zm3 0h-2v3h2v-3z"/>
    </svg>&nbsp;Other Pharmacies</h4>
  </div>
  <div class="tablets">
    <div class="row">
      <div class="col-md col-sm">
        <h3 class="head">FRYS FOOD AND DRUG</h3>
        <p>4150 E 22nd St, Tucson, AZ 85711 (1.35mi)</p>
      </div>
      <div class=" col-md col-sm paymentContent">
       <div class="row">
        <div class=" col-md-12 col-sm-6">
          <h3 class="float-right miniHead">You Pay</h3>
        </div>
        <div class=" col-md-12 col-sm-6">
          <p class="float-right" style="font-size: 25px; font-weight: 500;color: var(--primaryColor);">$2130.38</p>
        </div>
       </div>
      </div>
        <div class="col-md-3 col-sm-3 raisedBtn">
          <div>
            <button mat-raised-button class="disBtn" color="primary">Get Discount</button>
          </div>
      </div>
    </div>
    <div>
      <mat-accordion>
      <mat-expansion-panel (opened)="panelOpenState = true"
                       (closed)="panelOpenState = false">
    <mat-expansion-panel-header>
      <mat-panel-title>
        More Locations
      </mat-panel-title>
    </mat-expansion-panel-header>
    <p>2480 N Swan Rd, Tucson, AZ 85712
      (2.56 mi)</p>

     <p>7050 E 22nd St, Tucson, AZ 85710
      (2.57 mi)</p>
  </mat-expansion-panel>
</mat-accordion>
    </div>
  </div>
</div> -->
