import { Component, HostListener, OnInit } from '@angular/core';
import { documentToHtmlString } from '@contentful/rich-text-html-renderer';
import { BLOCKS, INLINES } from '@contentful/rich-text-types';
import { Observable } from 'rxjs';
import { ContentfulService } from 'src/app/services/contentful.service';
@Component({
  selector: 'app-subresources-page-preview',
  templateUrl: './subresources-page-preview.component.html',
  styleUrls: ['./subresources-page-preview.component.scss']
})
export class SubresourcesPagePreviewComponent implements OnInit {

  responseData: any[] | undefined = [];
  subResourceContent = [];
  resourcespaceID: number;
  isi_id: any;
  resid: any;
  resid2: any;
  resourceprimarycolor: any;
  resourcetertiaryColor: any;
  resourcesecondarycolor: any;
  resourcefontColor: any;
  resourcehorizontalRule: any;
  indication_header: any;
  isi_header: any;
  indication_text: any;
  isi_text: any;
  headerIndicationHeaderFontSize: any;
  headerIndicationCopyFontSize: any;
  headerIndicationFontColor: any;
  isiHeadersFontSize: any;
  isiHeadersColors: any;
  drugdata: any;
  versionone_med: any;
  qsaUrl: string;
  versionone_medname: any;
  result2: any;
  drug: any;
  shortStickyIsi_text: any;
  shortStickyHeader: any;
  shortStickyContentheading: string;
  shortStickyContenttextAlignment: any;
  shortStickyContentfontSize: any;
  shortStickyContentlineHeight: any;
  shortStickyContentfontSizeMobile: any;
  shortStickyContentlineHeightMobile: any;
  shortStickyContentfontColor: any;
  shortStickyContenttextBlockPadding: any;
  shortStickyContenttextBlockMargin: any;
  shortStickyContenttext: string;
  shortStickyContenttextAlignment1: any;
  shortStickyContentfontSize1: any;
  shortStickyContentlineHeight1: any;
  shortStickyContentfontSizeMobile1: any;
  shortStickyContentlineHeightMobile1: any;
  shortStickyContentfontColor1: any;
  shortStickyContenttextBlockPadding1: any;
  shortStickyContenttextBlockMargin1: any;
  shortStickyContentwidth1: any;
  shortStickyPadding: any;
  shortStickyHeaderFontSize: any;
  shortStickyHeaderFontSizeMobile: any;
  shortStickyHeaderFontWeight: any;
  shortStickyHeaderFontColor: any;
  shortStickyHeaderBackgroundColor: any;
  shortStickyHeaderBackgroundPadding: any;



  constructor(private contentfulservice: ContentfulService) {
    let str = window.location.href;
    this.qsaUrl = str;
    let wordinurl = (this.qsaUrl.substring(this.qsaUrl.lastIndexOf('/') + 1));
    // console.log("wordinurl",wordinurl)
    this.resid = wordinurl
    console.log(this.resid)

    this.getshortstickIsiwithoutid(this.resid);

    this.getColor();

  }

  offsetFlag = true;
  mainText: Observable<[]>;
  heading: Observable<[]>;
  paragraph: Observable<[]>;
  unorderedList: Observable<[]>;
  listItem: Observable<[]>;
  listItem1: Observable<[]>;
  subhead: Observable<[]>;
  mainTexts
  shortStickyIsi: any;

  ngOnInit() {


  }



  scrollToElement(element: HTMLElement) {
    element.scrollIntoView({
      behavior: "smooth"
    });
  }

  private getElementById(id: string): HTMLElement {
    //console.log("element id : ", id);
    const element = document.getElementById(id);
    return element;
  }

  scrollToElementById(id: string) {
    const element = this.getElementById(id);
    this.scrollToElement(element);
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll($event) {
    const numb = window.scrollY;
    if (numb >= 1200) {
      this.offsetFlag = false;
    } else {
      this.offsetFlag = true;
    }
  }

  getshortstickIsiwithoutid(value) {
    this.contentfulservice.getdatapreview(value).subscribe(res => {
      // console.log("StickyIsiId : ", res)
      this.isi_id = res.fields.branddiseaseResources.sys.id;
      this.contentfulservice.getdatapreview(this.isi_id).subscribe(res => {
        console.log("isi_id", res.fields);
        this.shortStickyIsi = res.fields.stickyIsi.sys.id

        this.contentfulservice.getdatapreview(this.shortStickyIsi).subscribe(res => {
          if (res.fields.hasOwnProperty("shortStickyHeader")) {
            this.shortStickyHeader = res.fields.shortStickyHeader;
          } else { console.log("shortStickyHeader not exists") }
          if (res.fields.hasOwnProperty("shortStickyContent")) {
            res.fields.shortStickyContent.forEach((block, index) => {

              if (index === 0) {
                let shortStickyContent = res.fields.shortStickyContent[0].sys.id
                this.contentfulservice.getdatapreview(shortStickyContent).subscribe(res => {
                  this.shortStickyContentheading = this._returnHtmlFromRichText(res.fields.text);

                  this.shortStickyContenttextAlignment = res.fields.textAlignment
                  document.documentElement.style.setProperty('--shortStickyContenttextAlignment', this.shortStickyContenttextAlignment);
                  this.shortStickyContentfontSize = res.fields.fontSize
                  document.documentElement.style.setProperty('--shortStickyContentfontSize', this.shortStickyContentfontSize);
                  this.shortStickyContentlineHeight = res.fields.lineHeight
                  document.documentElement.style.setProperty('--shortStickyContentlineHeight', this.shortStickyContentlineHeight);
                  this.shortStickyContentfontSizeMobile = res.fields.fontSizeMobile
                  document.documentElement.style.setProperty('--shortStickyContentfontSizeMobile', this.shortStickyContentfontSizeMobile);
                  this.shortStickyContentlineHeightMobile = res.fields.lineHeightMobile
                  document.documentElement.style.setProperty('--shortStickyContentlineHeightMobile', this.shortStickyContentlineHeightMobile);
                  this.shortStickyContentfontColor = res.fields.fontColor.value
                  document.documentElement.style.setProperty('--shortStickyContentfontColor', this.shortStickyContentfontColor);
                  this.shortStickyContenttextBlockPadding = res.fields.textBlockPadding
                  document.documentElement.style.setProperty('--shortStickyContenttextBlockPadding', this.shortStickyContenttextBlockPadding);
                  this.shortStickyContenttextBlockMargin = res.fields.textBlockMargin
                  document.documentElement.style.setProperty('--shortStickyContenttextBlockMargin', this.shortStickyContenttextBlockMargin);
                });
              } else if (index === 1) {

                let shortStickyContent1 = res.fields.shortStickyContent[1].sys.id
                this.contentfulservice.getdatapreview(shortStickyContent1).subscribe(res => {
                  this.shortStickyContenttext = this._returnHtmlFromRichText(res.fields.text);


                  this.shortStickyContenttextAlignment1 = res.fields.textAlignment
                  document.documentElement.style.setProperty('--shortStickyContenttextAlignment1', this.shortStickyContenttextAlignment1);
                  this.shortStickyContentfontSize1 = res.fields.fontSize
                  document.documentElement.style.setProperty('--shortStickyContentfontSize1', this.shortStickyContentfontSize1);
                  this.shortStickyContentlineHeight1 = res.fields.lineHeight
                  document.documentElement.style.setProperty('--shortStickyContentlineHeight1', this.shortStickyContentlineHeight1);
                  this.shortStickyContentfontSizeMobile1 = res.fields.fontSizeMobile
                  document.documentElement.style.setProperty('--shortStickyContentfontSizeMobile1', this.shortStickyContentfontSizeMobile1);
                  this.shortStickyContentlineHeightMobile1 = res.fields.lineHeightMobile
                  document.documentElement.style.setProperty('--shortStickyContentlineHeightMobile1', this.shortStickyContentlineHeightMobile1);
                  this.shortStickyContentfontColor1 = res.fields.fontColor.value
                  document.documentElement.style.setProperty('--shortStickyContentfontColor1', this.shortStickyContentfontColor1);
                  this.shortStickyContenttextBlockPadding1 = res.fields.textBlockPadding
                  document.documentElement.style.setProperty('--shortStickyContenttextBlockPadding1', this.shortStickyContenttextBlockPadding1);
                  this.shortStickyContenttextBlockMargin1 = res.fields.textBlockMargin
                  document.documentElement.style.setProperty('--shortStickyContenttextBlockMargin1', this.shortStickyContenttextBlockMargin1);
                  this.shortStickyContentwidth1 = res.fields.width
                  document.documentElement.style.setProperty('--shortStickyContentwidth1', this.shortStickyContentwidth1);
                });
              }
            });
          } else { console.log("shortStickyIsiImportantSafetyInformationText not exists") }


          this.shortStickyHeaderBackgroundPadding = res.fields.shortStickyHeaderBackgroundPadding
          document.documentElement.style.setProperty('--shortStickyHeaderBackgroundPadding', this.shortStickyHeaderBackgroundPadding);
          this.shortStickyPadding = res.fields.shortStickyPadding
          document.documentElement.style.setProperty('--shortStickyPadding', this.shortStickyPadding);
          this.shortStickyHeaderFontSize = res.fields.shortStickyHeaderFontSize
          document.documentElement.style.setProperty('--shortStickyHeaderFontSize', this.shortStickyHeaderFontSize);
          this.shortStickyHeaderFontSizeMobile = res.fields.shortStickyHeaderFontSizeMobile
          document.documentElement.style.setProperty('--shortStickyHeaderFontSizeMobile', this.shortStickyHeaderFontSizeMobile);
          this.shortStickyHeaderFontWeight = res.fields.shortStickyHeaderFontWeight
          document.documentElement.style.setProperty('--shortStickyHeaderFontWeight', this.shortStickyHeaderFontWeight);
          this.shortStickyHeaderFontColor = res.fields.shortStickyHeaderFontColor.value
          document.documentElement.style.setProperty('--shortStickyHeaderFontColor', this.shortStickyHeaderFontColor);
          this.shortStickyHeaderBackgroundColor = res.fields.shortStickyHeaderBackgroundColor.value
          document.documentElement.style.setProperty('--shortStickyHeaderBackgroundColor', this.shortStickyHeaderBackgroundColor);

        });
      });
    })
  }

  /*getshortstickIsiforresourcepage(value) {
      console.log("for resource id", value)
      this.contentfulservice.getdatapreview(value).subscribe(res => {
          //console.log("StickyIsiId : ", res.fields.isi.sys.id)
          this.isi_id = res.fields.isi.sys.id;
          this.contentfulservice.getdatapreview(this.isi_id).subscribe(res => {
              //console.log(res.fields);
              this.shortStickyIsi = res.fields.shortStickyIsi
 
              this.indication_header = res.fiels.shortStickyIsiIndicationsHeader;
              this.isi_header = res.fiels.shortStickyIsiSafetyInformationHeader;
              this.indication_text = res.fields.shortStickyIsiIndicationsText;
              this.isi_text = res.fields.shortStickyIsiImportantSafetyInformationText
 
          });
      })
  }*/

  public options: any = {
    renderNode: {
      [INLINES.HYPERLINK]: (node, next) => `<a href="${node.data.uri}" target="_blank" rel="noopener noreferrer">${next(node.content)}</a>`,
      [BLOCKS.PARAGRAPH]: (node, next) => `<p>${next(node.content).replace(/\n/g, '<br/>')}</p>`,
    }
  }


  _returnHtmlFromRichText(richText) {
    if (richText === undefined || richText === null || richText.nodeType !== 'document') {
      return '<p>Loading</p>';
    } else {
      return documentToHtmlString(richText, this.options);
    }
  }

  //getContent(){
  //   this.contentfulservice.getdatapreview('5cyjaHnjjHq652K59o2LuN').subscribe(res =>{
  //this.contentfulservice.getdatapreview('60ilGfimhTUiGmXOxai0n9').subscribe(res =>{
  //  this.mainText = res.fields.prescribingInformationCopy.content[0].content[0].value;
  //console.log(res)
  //  })
  //}

  // masterContent:any;
  // getAllContents(){
  //   this.contentfulservice.getAlldata().subscribe(res =>{
  //     this.masterContent = res.items;
  //     this.getShortSticky();
  //   });
  // }

  // getShortSticky(){
  //   this.contentfulservice.getdatapreview('5cyjaHnjjHq652K59o2LuN').subscribe(res =>{
  //     res.fields.shortStickyIsi.content.forEach((ele)=>{
  //       ele.content.forEach((elem)=>{
  //         this.subResourceContent = elem
  //         // console.log(this.subResourceContent)
  //       })
  //     })
  //   })
  // }

  getColor() {

    //console.log("works!")
    this.contentfulservice.getdatapreview(this.resid).subscribe(res => {
      let brandresources = res.fields.branddiseaseResources.sys.id;
      this.contentfulservice.getdatapreview(brandresources).subscribe(res => {
        if (res.fields.hasOwnProperty("branding")) {
          let branding = res.fields.branding.sys.id;
          this.contentfulservice.getdatapreview(branding).subscribe(res => {
            if (res.fields.hasOwnProperty("primaryColor")) { this.resourceprimarycolor = res.fields.primaryColor.value; } else { console.log("resourceprimarycolor not exists") }
            if (res.fields.hasOwnProperty("tertiaryColor")) { this.resourcetertiaryColor = res.fields.tertiaryColor.value; } else { console.log("tertiaryColor not exists") }
            if (res.fields.hasOwnProperty("secondaryColor")) { this.resourcesecondarycolor = res.fields.secondaryColor.value; } else { console.log("resourcesecondarycolor not exists") }
            if (res.fields.hasOwnProperty("fontColor")) { this.resourcefontColor = res.fields.fontColor.value } else { console.log("resourcefontColor not exists") }
            if (res.fields.hasOwnProperty("horizontalRule")) { this.resourcehorizontalRule = res.fields.horizontalRule.value } else { console.log("horizontalRule not exists") }

            if (res.fields.hasOwnProperty("headerIndicationHeaderFontSize")) { this.headerIndicationHeaderFontSize = res.fields.headerIndicationHeaderFontSize } else { console.log("headerIndicationHeaderFontSize not exists") }
            if (res.fields.hasOwnProperty("headerIndicationCopyFontSize")) { this.headerIndicationCopyFontSize = res.fields.headerIndicationCopyFontSize } else { console.log("headerIndicationCopyFontSize not exists") }
            if (res.fields.hasOwnProperty("headerIndicationFontColor")) { this.headerIndicationFontColor = res.fields.headerIndicationFontColor.value } else { console.log("headerIndicationFontColor not exists") }
            if (res.fields.hasOwnProperty("isiHeadersFontSize")) { this.isiHeadersFontSize = res.fields.isiHeadersFontSize } else { console.log("isiHeadersFontSize not exists") }
            if (res.fields.hasOwnProperty("isiHeadersColors")) { this.isiHeadersColors = res.fields.isiHeadersColors.value } else { console.log("isiHeadersColors not exists") }

            document.documentElement.style.setProperty('--resourceprimarycolor', this.resourceprimarycolor ? this.resourceprimarycolor : "#3254a2");
            document.documentElement.style.setProperty('--resourcesecondarycolor', this.resourcesecondarycolor ? this.resourcesecondarycolor : "#691c32");
            document.documentElement.style.setProperty('--resourcehorizontalRuleColor', this.resourcehorizontalRule ? this.resourcehorizontalRule : "#3254a2");

            document.documentElement.style.setProperty('--headerIndicationHeaderFontSize', this.headerIndicationHeaderFontSize ? this.headerIndicationHeaderFontSize : "19px");
            document.documentElement.style.setProperty('--headerIndicationCopyFontSize', this.headerIndicationCopyFontSize ? this.headerIndicationCopyFontSize : "18px");
            document.documentElement.style.setProperty('--headerIndicationFontColor', this.headerIndicationFontColor ? this.headerIndicationFontColor : "#ffffff");
            document.documentElement.style.setProperty('--isiHeadersFontSize', this.isiHeadersFontSize ? this.isiHeadersFontSize : "1 REM");
            document.documentElement.style.setProperty('--isiHeadersColors', this.isiHeadersColors ? this.isiHeadersColors : "#0072ce");
          })
        } else { console.log("branding not exists") }
      })
    })

  }



}
