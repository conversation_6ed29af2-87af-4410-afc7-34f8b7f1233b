body {
    font-size: var(--bodyTextFontSize);
}

.container {
    //padding: 0
}

.container-fluid {
    //position: relative;
    // left: 1.5rem;
    // top: 3rem;
    width: 100vw;
    /* make it 100% of the viewport width (vw) */
    margin-left: calc((100% - 100vw) / 2);
    /* then remove the gap to the left of the container with this equation */
    padding: 0 !important
}

a {
    color: var(--resourceprimarycolor);
}

a:hover {
    color: var(--resourcesecondarycolor);
    //font-weight: 500;
}

a,
u {
    text-decoration: none;
}

.para {
    position: relative;
    left: 1rem;
    top: 0.5rem;
}

.copyicon {
    position: relative;
    top: -1rem;
}

.pdf-info {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    position: relative;
    text-align: center;
    // left: -2rem;
    // padding-top: 22px;
}
 

.top-content-button {
    border: none;
    background-color: var(--resourceprimarycolor);
    color: var(--resourcebuttonfontcolor);
    border-radius: 6px;
    padding: 11px 50px;
    font-size: 16px;
}

.ulHead {
    font-size: 1.125rem;
    font-family: var(--fontStyle);
    font-weight: 400;
    color: #282828;
    line-height: 1.875rem;
}

ul li {
    font-size: 1.125rem;
    font-weight: 400;
    font-family: var(--fontStyle);
    line-height: 1.875rem;
    color: #282828;
}

#foot_links {

    /* styles for the host element go here */
    :host {
        a {
            color: white
        }
    }

    /* styles for child elements go here */
    ::ng-deep {
        a {
            color: white
        }
    }
}

:host::ng-deep a {
    color: var(--resourceprimarycolor);
    cursor: pointer;
    text-decoration: none;
}

:host::ng-deep a:hover {
    color: var(--resourcesecondarycolor);
    //font-weight: bold;
    cursor: pointer;
}

.captchaclass {
    text-align: center;
}

.captchadiv {
    display: inline-block;
}


.top-content-button:hover {
    background-color: var(--resourcebuttonBackgroundRollOverColor);
    color: var(--resourcebuttonhoverfontcolor)
}

.info {
    color: var(--resourceprimarycolor);
}

.info:hover {
    color: var(--resourcesecondarycolor);
    //font-weight: 500;
}

.tool-row {
    justify-content: center;
    display: flex;
    align-items: center;
    margin-bottom: 20px;

}

.contentbox {
    // border-top: 3px solid var(--resourcehorizontalRuleColor);
    // border-bottom: 3px solid var(--resourcehorizontalRuleColor);
    position: relative;
    height: 50%;
    padding: 0px 40px 40px 40px;
    ;
    //background-color: var(--backgroundColor);
}

.qsa-sample {
    max-width: 100%;
}

.box {
    margin-bottom: 5rem;
    position: relative;
    left: -33px;
    top: 2rem;
    // width: 85%;
    //height: 375px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);
    padding: 30px 40px 30px 40px;
    border-radius: 11px;
    margin-left: 2rem;
}

ul li {
    margin-top: 10px;
}

.from-buttom {
    background-color: var(--resourceprimarycolor);
    color: var(--resourcebuttonfontcolor);
    border: none;
    font-family: "Roboto", Sans-serif;
    font-size: 15px;
    font-weight: 400;
    border-radius: var(--buttonCornerRadius);
    padding: 11px 50px;
}

.from-buttom:hover {
    background-color: var(--resourcebuttonBackgroundRollOverColor);
    color: var(--resourcebuttonhoverfontcolor)
}


.medicines {
    margin-top: 17px;
    //font-size: 1.125rem;
    font-size: var(--bodyTextFontSize);
    font-weight: var(--bodyTextFontWeight);
    line-height: var(--bodyTextLineHeight);
    font-family: var(--fontStyle);
}

.example-section {
    margin-bottom: 10px;
}



.button-contentbox {
    border-radius: 15px;
    width: 90%;
    position: relative;
    left: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.content-button {
    width: 140px;
    height: 50px;
    border-radius: 10px;
    font-size: medium;
}

.content-button:hover {
    background-color: var(--resourcebuttonBackgroundRollOverColor);
    color: var(--resourcebuttonhoverfontcolor)
}

.contentbox3 {
    background-color: rgb(254, 254, 254);
    position: relative;
    z-index: 1;
    margin-bottom: 80px;
}

.scrolltotop {
    margin: 20px 0 20px 0;
    border: none;
    background: #fff;
}


.fixed-footer {
    width: 100%;
    color: white;
    background-color: #2c3e50;
    padding: 0 2rem;
    transition: bottom 0.3s ease-in-out;
}

.botton-content {
    position: relative;
    top: 2rem;
    margin-bottom: 7rem;
}

.mainContent {
    font-size: var(--bodyTextFontSize);
    font-weight: var(--bodyTextFontWeight);
    line-height: var(--bodyTextLineHeight);
}

.license {
    font-size: 0.9rem;
    position: relative;
    top: 1rem;
}

.request-input {
    position: relative;
    top: 0.5rem;
    margin: 5px 0 25px 0;
}

.request-button {
    width: auto;
    padding: 12px;
    position: relative;
    background-color: var(--resourceprimarycolor);
    color: var(--resourcebuttonfontcolor);
    border: none;
    border-radius: var(--buttonCornerRadius);
}

.request-button:hover {
    background-color: var(--resourcebuttonBackgroundRollOverColor);
    color: var(--resourcebuttonhoverfontcolor)
}


::ng-deep .mat-horizontal-stepper-header {
    pointer-events: none !important;
    display: none;
}


// @media (max-width:768px){
//     .qsa-sample{
//         width: 90%;
//     }
// }


@media (min-width:768px) {
    .p_filetext{
        font-size: var(--p_filefontSize) !important;
        line-height: var(--p_filelineHeight) !important;
        // padding: var(--p_filetextBlockPadding) !important;
        margin: var(--p_filetextBlockMargin) !important; 
        color: var(--p_filefontColor) !important;
    }
    .p_filetextmobile{
        display: none;
    }
    .p_file { 
        text-align: var(--p_filetextAlignment) !important; 
    }
    .textBoxIndicationMobile{
        display: none;
    }
    .textBoxIndication{
        font-size: var(--textBoxIndicationfontSize) !important;
        line-height: var(--textBoxIndicationlineHeight) !important;
        padding: var(--textBoxIndicationtextBlockPadding) !important;
        margin: var(--textBoxIndicationtextBlockMargin) !important; 
        text-align: var(--textBoxIndicationtextAlignment) !important;
        color: var(--textBoxIndicationfontColor) !important;
    }
    .textBoxIndicationtextMobile{
        display: none;
    }
    .textBoxIndicationtext{
        font-size: var(--IndicationtextfontSize) !important;
        line-height: var(--IndicationtextlineHeight) !important;
        padding: var(--IndicationtexttextBlockPadding) !important;
        margin: var(--IndicationtexttextBlockMargin) !important; 
        text-align: var(--IndicationtexttextAlignment) !important;
        color: var(--IndicationtextfontColor) !important;
    }
    .columnContenttextMobile{
        display: none;
    }
    .columnContenttext{
        font-size: var(--columnContenttextcopyFontSize) !important;
        line-height: var(--columnContentlineHeight) !important;
        padding: var(--columnContenttexttextBlockPadding) !important;
        margin: var(--columnContenttextBlockMargin) !important; 
        text-align: var(--columnContenttextcopyTextAlign) !important;
        color: var(--columnContenttextcopyFontColor) !important;
    }
    ::ng-deep {
        .mainIsiContentheading p {
            text-align: var(--mainIsiContenttextAlignment);
            font-size: var(--mainIsiContentfontSize);
            line-height: var(--mainIsiContentlineHeight);
            color: var(--mainIsiContentfontColor);
            padding: var(--mainIsiContenttextBlockPadding);
            margin: var(--mainIsiContenttextBlockMargin);
            width: var(--mainIsiContentwidth);
        }
    }
    .mainIsiContentheadingMobile{
        display: none;
    }
}   

@media (max-width:768px) {
    .columnContent2 .formbox {
        padding: var(--containerPaddingMobile) !important;
        margin: var(--containerMarginMobile) !important;
    }
    .containerContent{ 
        margin: var(--containerContentmarginMobile) !important;
        padding: var(--containerContentpaddingMobile) !important;
    }
.columnContent1 {    
    margin: var(--columnContent1marginMobile) !important;
    padding: var(--columnContent1paddingMobile) !important;
}

    ::ng-deep {
        .mainIsiContentheadingMobile p {
            text-align: var(--mainIsiContenttextAlignment) !important;
            font-size: var(--mainIsiContentfontSizeMobile) !important;
            line-height: var(--mainIsiContentlineHeightMobile) !important;
            color: var(--mainIsiContentfontColor) !important;
            padding: var(--mainIsiContenttextBlockPaddingMobile) !important;
            margin: var(--mainIsiContenttextBlockMarginMobile) !important;
            width: var(--mainIsiContentwidth) !important;
        }
    }
    .mainIsiContentheading{
        display: none;
    }
    .columnContenttextMobile{
        font-size: var(--columnContenttextcopyFontSizeMobile) !important;
        line-height: var(--columnContentlineHeightMobile) !important;
        padding: var(--columnContenttexttextBlockPaddingMobile) !important;
        margin: var(--columnContenttextBlockMarginMobile) !important; 
        text-align: var(--columnContenttextcopyTextAlignMobile) !important;
        color: var(--columnContenttextcopyFontColor) !important;
    }
    .columnContenttext{
        display: none;
    }
    .textBoxIndicationtextMobile{
        font-size: var(--IndicationtextfontSizeMobile) !important;
        line-height: var(--IndicationlineHeightMobile) !important;
        padding: var(--IndicationtexttextBlockPaddingMobile) !important;
        margin: var(--IndicationtexttextBlockMarginMobile) !important; 
        text-align: var(--IndicationtexttextAlignmentMobile) !important;
        color: var(--IndicationtextfontColor) !important;
    }
    .textBoxIndicationMobile{
        font-size: var(--textBoxIndicationfontSizeMobile) !important;
        line-height: var(--textBoxIndicationlineHeightMobile) !important;
        padding: var(--textBoxIndicationtextBlockPaddingMobile) !important;
        margin: var(--textBoxIndicationtextBlockMarginMobile) !important; 
        text-align: var(--textBoxIndicationtextAlignmentMobile) !important;
        color: var(--textBoxIndicationfontColor) !important;
    }
    .textBoxIndication{
        display: none;
    }
    .textBoxIndicationtext{
        display: none;
    }
    .p_filetextmobile{
        font-size: var(--p_filefontSizeMobile) !important;
        line-height: var(--p_filelineHeightMobile) !important;
        // padding: var(--p_filetextBlockPaddingMobile) !important;
        margin: var(--p_filetextBlockMarginMobile) !important; 
        color: var(--p_filefontColor) !important;
    }

    .p_filetext{
        display: none;
    }
    .p_file { 
        text-align: var(--p_filetextAlignmentMobile) !important; 
    }
    .main-content {
        position: relative;
    }

 

    .pdf-info {
        font-size: 16px;
        position: relative;
        left: 0.3rem;
    }

    .info {
        position: relative;
        left: 1rem;
    }
 

    .aboveBrandtext {
        font-size: var(--abovetextFontSizeMobile) !important;
    }


    .tool-row {
        display: block;
        position: relative;
        left: 1rem;
    }

    .second-tr {
        position: relative;
        left: 7px;
    }

    .contentbox {
        position: relative;
        top: 2rem;
        left: 0rem;
        padding: 20px;
        width: 100%;
    }

    .contentbox2 {
        position: relative;
        left: 1rem;
    }

    .button-contentbox {
        display: block;
        position: relative;
        left: 0;
    }

    // #captchaElem{
    //     position: relative;
    //     left: 1.15rem;
    //     width: 84%;
    // }

    .license {
        font-size: 10px;
    }

    .box {
        height: auto;
        // width: 106%;
        margin-left: 0;
        position: relative;
        left: 0px;
        padding: 25px;
    }

    // .request-button{
    //     position: relative;
    //     left: 2rem;
    // }

    .containerContenttext {
        font-size: var(--containerContenttextcopyFontSizeMobile) !important;
        text-align: var(--containerContenttextcopyTextAlignMobile) !important;
        line-height: var(--copyLineHeightMobile) !important;
        padding: var(--copyPaddingMobile) !important;
        margin: var(--copyMarginMobile) !important;
    }

    .containerContenttext1 {
        font-size: var(--containerContenttextcopyFontSizeMobile1) !important;
        text-align: var(--containerContenttextcopyTextAlignMobile1) !important;
        line-height: var(--copyLineHeightMobile1) !important;
        padding: var(--copyPaddingMobile1) !important;
        margin: var(--copyMarginMobile1) !important;
    }
}


@media (max-width:576px) {
    .pdf-info {
        display: block;
    }

    .pdf-info h5 {
        content-visibility: hidden !important;

        visibility: hidden;
        padding-top: 10px;
        height: 10px;
    }
}

@media (max-width:480px) {
    #captcha_custom {
        transform: scale(0.77);
    }
}

@media (max-width:280px) {
    #captcha_custom {
        transform: scale(0.60);
    }
}

@media (min-width:1200px) {
    .container {
        max-width: 1170px !important;
    }
}

#headgradient {
    background: var(--headergradientColor);
}

#footgradient {
    background: var(--resourceprimarycolor);
}

.paraA {
    color: var(--resourceLinkColor)
}

.bottom-isi h4 {
    color: var(--isiHeadersColors);
    font-size: var(--isiHeadersFontSize);
    padding-top: 20px;
    padding-bottom: 10px;
}

.header-link {
    color: var(--headerIndicationFontColor)
}

.reference {
    margin-top: 40px;
    color: #778288;
    font-size: .85rem;
    font-weight: 400;
}

.copyBlockFootnotes {
    font-size: .75rem;
    line-height: 1rem;
    font-weight: 400;
    margin-top: 20px;
}

.contain {
    background-color: #fff;
    padding: 15px;
    font-weight: 600;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
}

.video-container {
    position: relative;
    margin-bottom: 20px;
    margin-top: 30px;
}




::ng-deep iframe {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

.video-wrapper {
    position: relative;
    width: 100%;
    overflow: hidden;
    padding-top: 56.25%;
}

.isi-text {
    font-size: var(--isiTextFontSize);
    font-weight: var(--isiTextFontWeight);
    line-height: var(--isiTextLineHeight);
}

.contentheadingText {
    font-size: var(--contentheadingFontSize);
    text-align: var(--contentheadingAlignment);
}

.contentBelowText {
    font-size: var(--contenttextFontSize);
    text-align: var(--contenttextAlignment);
}

.contentBelowrefText {
    font-size: var(--contenttextReferencesFontSize);
    text-align: var(--contenttextReferencesAlignment);
}

.head-container {
    background-color: var(--topcontentbg);
    padding: var(--topcontentpadding);
    margin: var(--topcontentmargin);
}

.top-content-container {
    width: var(--topcontentwidth);
}

.topheadingtext {
    text-align: var(--toptextAlignment);
    font-size: var(--toptextFontSize);
    color: var(--toptextFontColor);
}

@media only screen and (min-width: 768px) {

    .headingMobile,
    .topheadingtextMobile,
    .headingMobilelist {
        display: none !important;
    }

    .headinglist {
        text-align: var(--textAlignment);
        font-size: var(--textFontSize1);
        line-height: var(--textLineHeight);
    }

    .heading {
        display: flex;
        align-items: center;
        text-align: var(--headingAlignment);
        font-size: var(--headingFontSize);
        color: var(--headingFontColor);
    }
}

@media only screen and (max-width: 768px) {

    .heading,
    .topheadingtext,
    .headinglist {
        display: none !important;
    }

    .head-container {
        padding: var(--topcontentpaddingMobile);
        margin: var(--topcontentmarginMobile);
    }
    .headingMobile {
        text-align: var(--topheadingAlignmentMobile);
        font-size: var(--headingFontSizeMobile);
    }

    .headingMobilelist {
        text-align: var(--textAlignmentMobile);
        font-size: var(--textFontSizeMobile1);
        line-height: var(--textLineHeightMobile);
    }

    .mainIsiHeader {
        font-size: var(--mainIsiHeaderFontSizeMobile) !important;
    }

    .mainIsiContentheading {
        font-size: var(--mainIsiContentfontSizeMobile) !important;
        line-height: var(--mainIsiContentlineHeightMobile) !important;
    }

    .mainIsiContenttext {
        font-size: var(--mainIsiContentfontSizeMobile1) !important;
        line-height: var(--mainIsiContentlineHeightMobile1) !important;
    }

    .mainIsiContentheading1 {
        font-size: var(--mainIsiContentfontSizeMobile2) !important;
        line-height: var(--mainIsiContentlineHeightMobile2) !important;
    }

    .mainIsiContentheading2 {
        font-size: var(--mainIsiContentfontSizeMobile4) !important;
        line-height: var(--mainIsiContentlineHeightMobile4) !important;
    }

    .mainIsiContenttext2 {
        font-size: var(--mainIsiContentfontSizeMobile5) !important;
        line-height: var(--mainIsiContentlineHeightMobile5) !important;
    }

    .mainIsiContenttext1 {
        font-size: var(--mainIsiContentfontSizeMobile3) !important;
        line-height: var(--mainIsiContentlineHeightMobile3) !important;
    }

    .headingMobile {
        display: flex;
        align-items: center;
        text-align: var(--headingAlignmentMobile) !important;
        font-size: var(--headingFontSizeMobile) !important;
        color: var(--headingFontColor) !important;
    }

    .topheadingtextMobile {
        text-align: var(--toptextAlignmentMobile) !important;
        font-size: var(--toptextFontSizeMobile) !important;
        color: var(--toptextFontColor) !important;
    }
  

 

    .botton-content {
        text-align: var(--copyrighttextAlignmentMobile) !important;
    }

    .LandingImage {
        width: 100%;
    }

    .dynamicicon {
        width: var(--iconWidthMobile) !important;
        height: var(--iconHeightMobile) !important;
    }

    .ResourceLogo {
        width: var(--branddiseaselogoWidthMobile) !important;
        height: var(--branddiseaselogoHeightMobile) !important;
    }

    .dropdownMenuicon {
        width: var(--dropdowniconWidthMobile) !important;
        height: var(--dropdowniconHeightMobile) !important;
    }

    .qsa-sample {
        width: var(--sampleImg1WidthMobile) !important;
        height: var(--sampleImg1HeightMobile) !important;
    }
    .columnContentimg1 img{
        width: var(--imageWidthMobile1) !important; 
        padding: var(--imagePaddingMobile1) !important;
        margin: var(--imageMarginMobile1) !important;
}
.columnContent {
    font-size: var(--columnContent1textFontSizeMobile) !important; 
}
}

.aboveBrand {
    background-color: var(--aboveBrandbg);
}

.aboveBrand-container {
    width: var(--aboveBrandwidth);
    padding: var(--columnBlockPadding);
}

.aboveBrandtext {
    display: flex;
    align-items: center;
    text-align: var(--abovetextAlignment);
    color: var(--textFontColor);
    font-size: var(--abovetextFontSize);
}

::ng-deep {
    .aboveBrandtext p {
        margin: 0 !important;
    }

    .p_file p {
        margin: 0 !important;
    }

    .heading p{
        margin: 0 !important;
    }
    .headingMobile p{
        margin: 0 !important;
    }
}
.headinglist-content, .headingMobilelist-content{
    margin-top: 1rem;
}
.boveBrandcolumn1 {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.dropdown-container {
    position: relative;
    display: inline-block;
    font-size: var(--abovetextFontSize);
}


.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 160px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1;
    padding: 12px;
    right: 0;
    top: 100%;
}

.dropdown-content.show {
    display: block;
}

.dropdown-content span {
    display: block;
    padding: 8px;
    cursor: pointer;
}

.dropdown-content span:hover {
    background-color: #f5f5f5;
}

.dropdownBackground {
    background-color: var(--dropdownBackgroundColor);
    color: var(--dropdownFontColor);
    width: 185px;
    text-align: left;
}

.dropdownBackground:hover {
    width: 185px;
    background-color: var(--dropdownLinkRolloverBackgroundColor);
    color: var(--dropdownFontColorRollover);
}

.top-content {
    text-align: var(--brandheaderalignment);
}

.p_filesection {
    background-color: var(--p_filebg);
    padding: var(--p_filetextBlockPadding);
}

.p_filecontainer {
    display: flex;
    align-items: center;
}

.p_file {
    width: var(--p_filewidth); 
}
 

.textBoxBelowHeaderIndicationsection {
    margin: var(--textBlockMargin);
}

.textBoxIndicationactionbuttonText button {
    background-color: var(--customButtonBackgroundColor);
    color: var(--customButtonTextColor);
    border-radius: var(--buttonBorderRadius);
    padding: var(--buttonPadding);
    width: var(--buttonWidth);
    height: var(--buttonHeight);
    border: none;
}

.textBoxIndicationactionbuttonText button:hover {
    background-color: var(--customButtonBackgroundRolloverColor);
    color: var(--customButtonTextRolloverColor);
}
 

.resourcehorizontalRuleColor {
    border-top: 4px solid var(--resourcehorizontalRuleColor);
    margin-top: 30px;
    margin-bottom: 10px;
}

.containerContent {
    margin: var(--containerContentmargin);
    background-color: var(--containerContentbg);
    border-radius: var(--containerContentradius);
    padding: var(--containerContentpadding);
    width: var(--containerContentwidth);
}

.containerContenttext {
    font-family: var(--containerContenttextcopyFont);
    font-size: var(--containerContenttextcopyFontSize);
    color: var(--containerContenttextcopyFontColor);
    text-align: var(--containerContenttextcopyTextAlign);
    line-height: var(--copyLineHeight);
    padding: var(--copyPadding);
    margin: var(--copyMargin);
}

.containerContenttext1 {
    font-family: var(--containerContenttextcopyFont1);
    font-size: var(--containerContenttextcopyFontSize1);
    color: var(--containerContenttextcopyFontColor1);
    text-align: var(--containerContenttextcopyTextAlign1);
    line-height: var(--copyLineHeight1);
    padding: var(--copyPadding1);
    margin: var(--copyMargin1);
}

.columnContent1 {
    display: flex;
    justify-content: center;
    align-items: center;
    width: var(--columnContent1width);
    margin: var(--columnContent1margin);
    padding: var(--columnContent1padding);
}

.columnContentimg1 {
    text-align: var(--imageOnlyAlignment1);
}

.columnContentimg1 img {
    width: var(--imageWidth1);
    padding: var(--imagePadding1);
    margin: var(--imageMargin1);
}

.columnContent {
    font-size: var(--columnContent1textFontSize);
    text-align: var(--columnContent1textAlignment);
    color: var(--columnContent1textFontColor);
}

.columnContent2 .formbox {
    width: var(--columnContent2containerWidth);
    border: var(--borderWidth) solid var(--borderColor);
    border-radius: var(--borderRadius);
    padding: var(--containerPadding);
    margin: var(--containerMargin);
}
 

.mainIsi {
    margin: var(--mainIsiMargin);
    padding: var(--mainIsiPadding);
}

.mainIsiHeader {
    font-size: var(--mainIsiHeaderFontSize);
    font-weight: var(--mainIsiHeaderFontWeight);
    color: var(--mainIsiHeaderFontColor);
}



.mainIsiContenttext {
    text-align: var(--mainIsiContenttextAlignment1);
    font-size: var(--mainIsiContentfontSize1);
    line-height: var(--mainIsiContentlineHeight1);
    color: var(--mainIsiContentfontColor1);
    padding: var(--mainIsiContenttextBlockPadding1);
    margin: var(--mainIsiContenttextBlockMargin1);
    width: var(--mainIsiContentwidth1);
}

.mainIsiContentheading1 {
    text-align: var(--mainIsiContenttextAlignment2);
    font-size: var(--mainIsiContentfontSize2);
    line-height: var(--mainIsiContentlineHeight2);
    color: var(--mainIsiContentfontColor2);
    padding: var(--mainIsiContenttextBlockPadding2);
    margin: var(--mainIsiContenttextBlockMargin2);
    width: var(--mainIsiContentwidth2);
}

.mainIsiContentheading2 {
    text-align: var(--mainIsiContenttextAlignment3);
    font-size: var(--mainIsiContentfontSize3);
    line-height: var(--mainIsiContentlineHeight3);
    color: var(--mainIsiContentfontColor3);
    padding: var(--mainIsiContenttextBlockPadding3);
    margin: var(--mainIsiContenttextBlockMargin3);
    width: var(--mainIsiContentwidth3);
}

.mainIsiContenttext2 {
    text-align: var(--mainIsiContenttextAlignment5);
    font-size: var(--mainIsiContentfontSize5);
    line-height: var(--mainIsiContentlineHeight5);
    color: var(--mainIsiContentfontColor5);
    padding: var(--mainIsiContenttextBlockPadding5);
    margin: var(--mainIsiContenttextBlockMargin5);
    width: var(--mainIsiContentwidth5);
}

.mainIsiContenttext1 {
    text-align: var(--mainIsiContenttextAlignment3);
    font-size: var(--mainIsiContentfontSize3);
    line-height: var(--mainIsiContentlineHeight3);
    color: var(--mainIsiContentfontColor3);
    padding: var(--mainIsiContenttextBlockPadding3);
    margin: var(--mainIsiContenttextBlockMargin3);
    width: var(--mainIsiContentwidth3);
}


.dropdown-container2 {
    width: 100%;
    position: relative;
    display: inline-block;
}

.dropdown-content2 {
    display: none;
    width: 100%;
}

.dropdown-content2.show {
    display: block;
}

.dropdown-content2 span {
    display: block;
    padding: 8px;
    cursor: pointer;
}

.dropdown-header img {
    cursor: pointer;
}

.dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    background-color: transparent;
}

.dynamicicon {
    width: var(--iconWidth);
    height: var(--iconHeight);
}

.ResourceLogo {
    width: var(--branddiseaselogoWidth);
    height: var(--branddiseaselogoHeight);
    padding: var(--branddiseasepadding);
    margin: var(--branddiseasemargin);
}

.dropdownMenuicon {
    width: var(--dropdowniconWidth);
    height: var(--dropdowniconHeight);
}

.qsa-sample {
    width: var(--sampleImg1Width);
    height: var(--sampleImg1Height);
}

.footerres-container {
    display: flex;
    justify-content: var(--copyrightfooterContentAlignment);
}

.footerres {
    width: fit-content !important;
    text-align: var(--copyrighttextAlignment);
}

.sampleImg1imageAlignment {
    text-align: var(--sampleImg1imageAlignment);
}

.arrowdownicon {
    background-color: var(--heading_iconBackgroundColor);
    padding: var(--iconpadding);
    margin: var(--iconMargin);
    border-radius: var(--heading_iconRadius);
}