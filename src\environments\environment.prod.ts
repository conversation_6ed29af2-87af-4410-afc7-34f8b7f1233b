import { AppEnvironment } from '../app/core/interfaces/app-environment.interface';

export const environment: AppEnvironment = {
  production: true,
  // baseApiUrl: 'https://cdn.contentful.com',
  // baseImageUrl: 'https://images.ctfassets.net',
  // baseFileURL: 'https://assets.ctfassets.net',
  // siteKey:"6Lf3WEMjAAAAAMZ4UY1XwAn18XS5XaNswNtWc1pC",
  // baseMailUrl: 'https://api.qsasupport.com/v1/resources',
  // previewUrl: 'https://preview.contentful.com',
  // spaceId:'h2axairjfqha',
  // accessToken:'Nr129JpVmw1hjQAv-zy1UM8Pk8vahuU228O0bUScylU',
  // previewaccessToken:'Qv33QTCrNOKtcCHQkjibEc_6zPts0WML3cnV-xfS8SY',
  // security: {
  //   allowedOrigins: 'https://cdn.contentful.com'
  // }

  baseApiUrl: 'https://cdn.contentful.com',
  baseImageUrl: 'https://images.ctfassets.net',
  baseFileURL: 'https://assets.ctfassets.net',
  baseMailUrl: 'https://api.qsasupport.com/v1/resources',
  previewUrl: 'https://preview.contentful.com',
  //siteKey:"6Lf3WEMjAAAAAMZ4UY1XwAn18XS5XaNswNtWc1pC",
  siteKey:"6Lf3xWMkAAAAALOL7IyJsFvrFVOTVBGvAQIBj3QF",
  spaceId:'h2axairjfqha',
  accessToken:'Nr129JpVmw1hjQAv-zy1UM8Pk8vahuU228O0bUScylU',
  previewaccessToken:'Qv33QTCrNOKtcCHQkjibEc_6zPts0WML3cnV-xfS8SY',
  // baseAPI: PROXY_PREFIX_API_URL // using by http proxy,
  security: {
    allowedOrigins: 'https://cdn.contentful.com',
    // allowedOrigins: 'https://localhost:4200'
  },
};
