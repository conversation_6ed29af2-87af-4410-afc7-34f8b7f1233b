<div class="container">
  
   <div class="printbtn">
      <button class="content-button" (click)="print()" [disabled]="!isPdfLoaded" mat-raised-button color="primary">Print</button>
   </div>

   <!--<div class="float-end">
    <button class="content-button" (click)="download()" mat-raised-button color="primary">Download</button>
 </div>-->
  <!--<div *ngFor="let data of datas" id="sectionToPrint">
      <nav *ngIf="isLoaded">
         <button (click)="prevPage()" mat-raised-button color="primary" [disabled]="page === 1" class="previous">
           Prev
         </button>
         &nbsp;
         <button (click)="nextPage()" mat-raised-button color="primary" [disabled]="page === totalPages" class="next">
           Next
         </button>
         <p>{{ page }} / {{ totalPages }}</p>
       </nav>
      <pdf-viewer
            [src]="data"
            id="pdf"
            [external-link-target]="'blank'"
            [original-size]="false"
            [show-all]="false"
            [page]="page"
            (after-load-complete)="onLoaded($event); afterLoadComplete($event)"
            style="display: block;width: 100%;">
      </pdf-viewer>
   </div>
   <div *ngFor="let info of prescribes" id="sectionToPrint1">
      <nav *ngIf="isLoadeds">
         <button (click)="prevPageP()" mat-raised-button color="primary" [disabled]="page1 === 1" class="previous">
           Prev
         </button>
         &nbsp;
         <button (click)="nextPageP()" mat-raised-button color="primary" [disabled]="page1 === totalPages1" class="next">
           Next
         </button>
         <p>{{ page1 }} / {{ totalPages1 }}</p>
       </nav>
      <pdf-viewer
            [src]="info"
            id="prescribePdf"
            [external-link-target]="'blank'"
            [original-size]="false"
            [show-all]="false"
            [page]="page1"
            (after-load-complete)="onLoaded($event); afterLoadCompleteInfo($event)"
            style="display: block;width: 100%;">
      </pdf-viewer>
    </div>-->

  <!--<div class="pdfviewer">
    <pdf-viewer [src]="viewpdfurl"
              id="fullpdf"
              [render-text]="true"
              [original-size]="false"
              (after-load-complete)="onLoaded($event); afterLoadCompleteInfo($event)"
  ></pdf-viewer>
  </div>-->

  <div id="sectionToPrint">
   <!----<nav *ngIf="isLoaded" style="display: flex; justify-content: flex-end;">
      <button (click)="prevPage()" mat-raised-button color="accent" [disabled]="page === 1" class="previous">
        Prev
      </button>
      &nbsp;
      <button (click)="nextPage()" mat-raised-button color="accent" [disabled]="page === totalPages" class="next">
        Next
      </button>
    </nav>
    
    <p style="display: flex; justify-content: flex-end;padding:1%">{{ page }} / {{ totalPages }}</p>-->
    <pdf-viewer
      [src]="viewpdfurl"
      id="pdf"
      [external-link-target]="'blank'"
      [original-size]="false"
      [show-all]="true"
      [page]="page"
      (after-load-complete)="onLoaded($event); afterLoadComplete($event)"
      style="display: block;width: 100%;">
    </pdf-viewer>
    <!--<nav *ngIf="isLoaded" style="display: flex; justify-content: flex-end;">
      <button (click)="prevPage()" mat-raised-button color="accent" [disabled]="page === 1" class="previous">
        Prev
      </button>
      &nbsp;
      <button (click)="nextPage()" mat-raised-button color="accent" [disabled]="page === totalPages" class="next">
        Next
      </button>
    </nav>
    <p style="display: flex; justify-content: flex-end;padding:1%">{{ page }} / {{ totalPages }}</p>-->
  </div>
  

  </div>
