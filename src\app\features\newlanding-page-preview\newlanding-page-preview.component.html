<div class="container-fluid" id="top" *ngIf="allcontents">
    <div> 
        <div class="head-container" *ngIf="heading || headingMobile || topheadingtext || topheadingtextMobile" >
            <div class="main-content container top_heading_container">
    
                <div>
                    <div class="dropdown-container2">
                        <div style="display: flex;justify-content: space-between;">
                            <div class="heading" [innerHTML]="heading"></div>
                            <div class="headingMobile" [innerHTML]="headingMobile"
                                [ngStyle]="{ 'text-align':headingAlignment }"></div>
                            <div *ngIf="arrowdown && hasValidContent(topheadingtext)" class="headinglist" (click)="toggleDropdown2()">
                                <div *ngIf="isDropdownOpen2" class="arrowdownicon" >
                                    <img class="dynamicicon" src={{arrowdown}}  *ngIf="isDropdownOpen2">
                                </div>
                            </div>
                            <div *ngIf="arrowdown && hasValidContent(topheadingtextMobile)" class="headingMobilelist" (click)="toggleDropdown2()">
                                <div *ngIf="isDropdownOpen2" class="arrowdownicon" >
                                    <img class="dynamicicon" src={{arrowdown}}  *ngIf="isDropdownOpen2">
                                </div>
                            </div>
                        </div>
                        <div  *ngIf="topheadingtext || topheadingtextMobile" class="dropdown-content2" [class.show]="!isDropdownOpen2">
                            <div style="display: flex;justify-content: space-between;">
                                <div class="headinglist headinglist-content" [innerHTML]="topheadingtext" ></div>
                                <div class="headingMobilelist headingMobilelist-content" [innerHTML]="topheadingtextMobile" ></div>
                                <div *ngIf="arrowup"  (click)="toggleDropdown2()">
                                    <div class="arrowdownicon" >
                                        <img class="dynamicicon" src={{arrowup}}  *ngIf="!isDropdownOpen2">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="aboveBrandtext || dropdownMenuiconurl">
        <diV class="aboveBrand">
            <diV class=" container">
                <div class=" aboveBrand-container boveBrandcolumn1">
                    <div class="aboveBrandtext" [innerHTML]="aboveBrandtext"></div>
                    <div class="dropdown-container">
                        <div  (click)="toggleDropdown()">
                            <img class="dropdownMenuicon" src={{dropdownMenuiconurl}} *ngIf="!isDropdownOpen">
                            <img class="dropdownMenuicon" src={{dropdownMenuiconurl}} *ngIf="isDropdownOpen">
                        </div>
                        <div class="dropdown-content dropdownBackground" [class.show]="isDropdownOpen">
                            <a href="{{branddropdownLinks}}" target="_blank"
                                style="text-decoration: none;">{{branddropdowntext}}</a>
                        </div>
                    </div>
                </div>
            </diV>
        </diV>
        </div>
        <div class="top-content ">
            <div class="container" *ngIf="logo" >
                <img class="ResourceLogo" src={{logo}}  alt="ResourceLogo">
            </div>
            <div class="p_filesection" *ngIf="indicationtextContenttext || indicationtextContenttext1 " >
                <div class="container p_filecontainer">
                    <div class="p_file">
                    <div class="p_filetext" [innerHTML]="p_file" (click)="handlePFileClick($event)"></div>
                    <div class="p_filetextmobile" [innerHTML]="p_fileMobile" (click)="handlePFileClick($event)"></div>
                    </div>
                    <!-- <div class="p_file">
                        <a class="p_filetext" href={{indicationtextContent}} target="_blank" rel="noopener">{{indicationtextContenttext}}</a>
                        <a class="p_filetextmobile" href={{indicationtextContentMobile}} target="_blank" rel="noopener">{{indicationtextContenttexttextMobile}}</a>
                    <span style="display:inline-block;margin-left:10px;margin-right:10px">
                        <h5>|</h5>
                    </span> 
                    <a class="p_filetext" (click)="scrollToElementById('contentbox')" target="_blank" rel="noopener">{{indicationtextContenttext1}}</a>
                    <a class="p_filetextmobile" (click)="scrollToElementById('contentbox')" target="_blank" rel="noopener">{{indicationtextContenttext1textMobile}}</a>
                        </div> -->
                </div>
               
            </div>
            <div class="textBoxBelowHeaderIndicationsection" *ngIf="textBoxIndication ||textBoxIndicationactionbuttonText || textBoxIndicationtext" >
                <div class="container">
               <div class="textBoxIndication" [innerHTML]="textBoxIndication"></div>
               <div class="textBoxIndicationMobile" [innerHTML]="textBoxIndicationMobile"></div>
               <div class="textBoxIndicationactionbuttonText">
                <button (click)="scrollToElementById(buttonLink)">{{textBoxIndicationactionbuttonText}}</button>
               </div>
               <div class="textBoxIndicationtext" [innerHTML]="textBoxIndicationtext"></div>
               <div class="textBoxIndicationtextMobile" [innerHTML]="textBoxIndicationtextMobile"></div>
               </div>
            </div>
            <div class="">
                <div class="row justify-content-center">
                    <div class="col-md-3 col-lg-auto" *ngFor="let btn of nonLinkButtons; let i = index"
                        style="padding-top:1.9rem !important;">
                        <button style="min-width:150px;border:0;background-color: transparent;"
                            (click)="onNonLinkButtonClick(i); scrollToElementById(btn.fields.buttonLink)">
                            <a style="text-decoration: none;
                               border-radius: 6px;
                               padding: 13px 50px;
                               font-size: 16px;" [style.color]="btn?.fields?.customButtonTextColor?.value"
                                [style.backgroundColor]="btn?.fields?.customButtonBackgroundColor?.value"
                                [ngStyle]="{ 'background-color': i === nonLinkHoveredIndex ? btn?.fields?.customButtonBackgroundRolloverColor?.value : btn?.fields?.customButtonBackgroundColor?.value }"
                                (mouseenter)="onNonLinkButtonMouseEnter(i)" (mouseleave)="onNonLinkButtonMouseLeave()">
                                {{btn.fields.actionbuttonText}}
                            </a>
                        </button>
                    </div>
                    <div class="col-md-9 col-lg-auto" *ngFor="let btn of linkButtons; let j = index"
                        style="padding-top:1.9rem !important;">
                        <button style="min-width:150px;border:0;background-color: transparent;"
                            (click)="onLinkButtonClick(j)" (mouseenter)="onLinkButtonMouseEnter(j)"
                            (mouseleave)="onLinkButtonMouseLeave()">
                            <a href="{{btn.fields.buttonLink}}{{userId}}" style="text-decoration: none;
                               border-radius: 6px;
                               padding: 13px 50px;
                               font-size: 16px;" [style.color]="btn?.fields?.customButtonTextColor?.value"
                                [style.backgroundColor]="btn?.fields?.customButtonBackgroundColor?.value"
                                [ngStyle]="{ 'background-color': j === linkHoveredIndex ? btn?.fields?.customButtonBackgroundRolloverColor?.value : btn?.fields?.customButtonBackgroundColor?.value }">
                                {{btn.fields.actionbuttonText}}
                            </a>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="contentbox">
            <div class="container">
                <div>
                    <h1 class="medicines"
                        style="color:var(--resourceh1Color);font-family: var(--fontStyle);font-size: 2.375rem;font-weight: 600;">
                        {{mainHead}}</h1>
                </div>

                <!-- <div>
            <h3 class="medicines" style="font-size:1.125rem;font-family: var(--fontStyle);font-weight: 400;line-height: 30px;">{{mainContent}}</h3>
          </div> -->
                <div class="mainContent" [innerHtml]="_returnHtmlFromRichText(mainContent)">
                </div>



                <div class="copyBlockFootnotes" [innerHtml]="_returnHtmlFromRichText(copyBlockFootnotes)"
                    *ngIf="copyBlockFootnotes"></div>

                <div *ngIf="videoTitle">
                    <h2 class="medicines"
                        style="color:var(--resourceh1Color);font-family: var(--fontStyle);font-size: 2.375rem;font-weight: 600;">
                        {{videoTitle}}</h2>
                </div>
                <div class="video-container" *ngIf="vimeo_video">
                    <div #playerContainer class="video-wrapper"></div>
                </div>
                <!-- text contentBelowVideo -->
                <div *ngIf="headingText && !contentBelowVideoImage" style="margin-top: 30px;  ">
                    <div>
                        <div class="contentheadingText" [innerHtml]="_returnHtmlFromRichText(headingText)"></div>
                        <div class="contentBelowText" *ngIf="contentBelowText"
                            [innerHtml]="_returnHtmlFromRichText(contentBelowText)"></div>
                        <div class="contentBelowrefText" *ngIf="contentBelowrefText"
                            [innerHtml]="_returnHtmlFromRichText(contentBelowrefText)"></div>
                    </div>
                </div>
                <!-- image+text contentBelowVideo -->
                <div *ngIf="headingText && contentBelowVideoImage" style="margin-top: 30px; display: flex;  ">
                    <div>
                        <div class="contentheadingText" [innerHtml]="_returnHtmlFromRichText(headingText)"></div>
                        <div class="contentBelowText" *ngIf="contentBelowText"
                            [innerHtml]="_returnHtmlFromRichText(contentBelowText)"></div>
                        <div class="contentBelowrefText" *ngIf="contentBelowrefText"
                            [innerHtml]="_returnHtmlFromRichText(contentBelowrefText)"></div>
                    </div>
                    <img class="qsa-sample" src={{contentBelowVideoImage}} alt="contentBelowVideoImage">
                </div>

                <!-- image contentBelowVideo -->
                <div *ngIf="contentBelowVideoImage && !headingText"
                    style="margin-top: 30px; display: flex; justify-content: var(--imageOnlyAlignment) ;">
                    <img *ngIf="contentBelowVideoImage" class="qsa-sample" src={{contentBelowVideoImage}}
                        alt="contentBelowVideoImage">
                </div>
                
                <!-- new form -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-xl" style="display: flex;justify-content: var(--columnVerticalAlignment);flex-direction: column;" *ngIf="sampleImg1">
                        <div class="sampleImg1imageAlignment" ><img class="qsa-sample" src={{sampleImg1}} alt="QSA-Sample"></div>
                        <div class="columnContenttext" [innerHTML]="columnContenttext"></div>
                        <div class="columnContenttextMobile" [innerHTML]="columnContenttextMobile"></div>
                    </div>
                    <div class="col-md medicines">
                        <div class="columnContent0">
                            <diV class="containerContent">
                                <div class="containerContenttext" [innerHTML]="containerContenttext" ></div> 
                                <div class="containerContenttext1" [innerHTML]="containerContenttext1" ></div>
                            </diV>
                        </div>
                        <div class="columnContent1" *ngIf="columnContent1 || columnContent1imgurl">
                            <div class="columnContentimg1" *ngIf="columnContent1imgurl">
                                <img  src={{columnContent1imgurl}}>
                            </div>
                            <div *ngIf="columnContent1" class="columnContent" [innerHTML]="columnContent1" ></div> 
                        </div>
                        <div class="columnContent2">  
                            <form id={{buttonLink}} class="formbox"  [formGroup]="landingForm" (ngSubmit)="userDatanew()"
                                *ngIf="containerContentheading">
                                <form [formGroup]="captchaForm">
                                    <div class="form-group row" *ngIf="step == 1">
                                        <h6
                                            style="color:var(--primaryColor); font-weight: 500; font-size: 1.5rem;font-family: var(--fontStyle);">
                                            {{containerContentheading}}</h6>
                                        <div class="form-check form-group medicines">
                                            <input class="form-check-input" formControlName="acceptTerms"
                                                type="checkBox" id="acceptTerms" name="policy"
                                                [ngClass]="{ 'is-invalid': submitted && f.acceptTerms.errors }" />
                                            <label [innerHtml]="privacyContentlabel">
                                            </label>
                                            <div *ngIf="submitted && f.acceptTerms.errors" class="invalid-feedback"
                                                style="position: relative;top:-1.2rem ;">
                                                *Confirmation is required
                                            </div>
                                        </div> 
                                    </div>
                                </form>
                                <div class="form-group" *ngIf="step == 2">
                                    <h6
                                        style="color:var(--primaryColor); font-weight: 500; font-size: 1.5rem;font-family: var(--fontStyle);">
                                        {{containerContentheading}}</h6>
                                    <div class="row">
                                        <div class="col-md request-input">
                                            <input class="form-control form-control-lg" type="text" id="firstName"
                                                formControlName="firstName" name="firstName" placeholder="First Name"
                                                aria-label=".form-control-lg example"
                                                [ngClass]="{ 'is-invalid': isSubmitted && for.firstName.errors}" />
                                            <div *ngIf="isSubmitted && for.firstName.errors" class="invalid-feedback">
                                                *First Name is required
                                            </div>
                                        </div>

                                        <div class="col-md request-input">
                                            <input class="form-control form-control-lg" id="lastName" type="text"
                                                formControlName="lastName" name="lastName" placeholder="Last Name"
                                                aria-label=".form-control-lg example"
                                                [ngClass]="{ 'is-invalid': isSubmitted && for.lastName.errors}" />
                                            <div *ngIf="isSubmitted && for.lastName.errors" class="invalid-feedback">
                                                *Last Name is required
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md request-input">
                                            <input class="form-control form-control-lg" type="email" id="email"
                                                formControlName="toEmail" name="toEmail" placeholder="Email"
                                                aria-label=".form-control-lg example"
                                                [ngClass]="{ 'is-invalid': isSubmitted && for.toEmail.errors}" />
                                            <div *ngIf="isSubmitted && for.toEmail.errors" class="invalid-feedback">
                                                *Email is required
                                            </div>
                                        </div>
                                        <div class="col-md request-input">
                                            <input class="form-control form-control-lg" type="text"
                                                formControlName="npi" name="npi" placeholder="NPI #"
                                                aria-label=".form-control-lg example">
                                        </div>
                                        <div class="mb-3" hidden>
                                            <label for="recipient-qsaUrl" class="col-form-label">qsaUrl</label>
                                            <input type="text" ngModel={{qsaUrl}} class="form-control"
                                                formControlName="qsaUrl" name="qsaUrl" id="recipient-qsaUrl">
                                        </div>
                                        <div class="mb-3" hidden>
                                            <label for="recipient-qsaUrl" class="col-form-label">drug</label>
                                            <input type="text" ngModel={{drug}} class="form-control"
                                                formControlName="drug" name="drug" id="recipient-drug">
                                        </div>
                                    </div>
                                </div>
                                <div style="display: flex;justify-content: center;">
                                    <button type="button" (click)="next()" *ngIf="step != 2"
                                        class="from-buttom medicines mx-2">Next</button>
                                </div>
                                <div style="display: flex;justify-content: center;">
                                    <button type="submit" *ngIf="step == 2" class="btn medicines request-button">Go to
                                        Your QSA</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- old form -->
                <div class="row" style="margin-top: 30px;" *ngIf="sampleImg">
                    <div class="col-xl" style="display: flex;justify-content: center;" *ngIf="sampleImg">
                        <img class="qsa-sample" src={{sampleImg}} alt="QSA-Sample">
                    </div>
                    <div class="col-md medicines">
                        <h6
                            style="color:var(--primaryColor);margin-bottom:25px;font-weight: 500;font-size:1.5rem;font-family: var(--fontStyle);">
                            {{mainSub}}</h6>
                        <!--<div class="ulHead" style="margin-bottom:25px;">{{unordered}}</div>-->
                        <!--<ul>
                <li>{{listed1}}</li>
                <li>{{listed2}}</li>
                <li>{{listed3}}</li>
              </ul>-->
                        <div>

                            <div class="mainContent" [innerHtml]="_returnHtmlFromRichText(copyblocklist)"></div>
                            <form class="box" id="request-qsa-form" [formGroup]="landingForm" (ngSubmit)="userData()" *ngIf="dynamicMsg && captchaForm">
                                <div class="form-group row" *ngIf="step == 1">
                                    <h6 style="color:var(--primaryColor); font-weight: 500; font-size: 1.5rem;font-family: var(--fontStyle);">
                                        {{dynamicMsg}}</h6>
                                    <div class="form-check form-group medicines">
                                        <input class="form-check-input" 
                                               [formGroup]="captchaForm"
                                               formControlName="acceptTerms"
                                               type="checkBox" 
                                               id="acceptTerms" 
                                               name="policy"
                                               [ngClass]="{ 'is-invalid': submitted && f.acceptTerms.errors }" />
                                        <label [innerHtml]="_returnHtmlFromRichText(privacyContent)"></label>
                                        <div *ngIf="submitted && f.acceptTerms.errors" class="invalid-feedback" style="position: relative;top:-1.2rem ;">
                                            *Confirmation is required
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md request-input">
                                        <input class="form-control form-control-lg" type="text" id="firstName"
                                            formControlName="firstName" name="firstName" placeholder="First Name"
                                            aria-label=".form-control-lg example"
                                            [ngClass]="{ 'is-invalid': isSubmitted && for.firstName.errors}" />
                                        <div *ngIf="isSubmitted && for.firstName.errors" class="invalid-feedback">
                                            *First Name is required
                                        </div>
                                    </div>

                                    <div class="col-md request-input">
                                        <input class="form-control form-control-lg" id="lastName" type="text"
                                            formControlName="lastName" name="lastName" placeholder="Last Name"
                                            aria-label=".form-control-lg example"
                                            [ngClass]="{ 'is-invalid': isSubmitted && for.lastName.errors}" />
                                        <div *ngIf="isSubmitted && for.lastName.errors" class="invalid-feedback">
                                            *Last Name is required
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md request-input">
                                        <input class="form-control form-control-lg" type="email" id="email"
                                            formControlName="toEmail" name="toEmail" placeholder="Email"
                                            aria-label=".form-control-lg example"
                                            [ngClass]="{ 'is-invalid': isSubmitted && for.toEmail.errors}" />
                                        <div *ngIf="isSubmitted && for.toEmail.errors" class="invalid-feedback">
                                            *Email is required
                                        </div>
                                    </div>
                                    <div class="col-md request-input">
                                        <input class="form-control form-control-lg" type="text"
                                            formControlName="npi" name="npi" placeholder="NPI #"
                                            aria-label=".form-control-lg example">
                                    </div>
                                    <div class="mb-3" hidden>
                                        <label for="recipient-qsaUrl" class="col-form-label">qsaUrl</label>
                                        <input type="text" ngModel={{qsaUrl}} class="form-control"
                                            formControlName="qsaUrl" name="qsaUrl" id="recipient-qsaUrl">
                                    </div>
                                    <div class="mb-3" hidden>
                                        <label for="recipient-qsaUrl" class="col-form-label">drug</label>
                                        <input type="text" ngModel={{drug}} class="form-control"
                                            formControlName="drug" name="drug" id="recipient-drug">
                                    </div>
                                </div>
                                <div style="display: flex;justify-content: center;">
                                    <button type="button" (click)="next()" *ngIf="step != 2"
                                        class="from-buttom medicines mx-2">Next</button>
                                </div>
                                <div style="display: flex;justify-content: center;">
                                    <button type="submit" *ngIf="step == 2" class="btn medicines request-button">Go to
                                        Your QSA</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <app-newlanding-preview-page-subresource></app-newlanding-preview-page-subresource>
        <div class="contentbox3" id="contentbox3">
            <div class="container bottom-isi">
                <div class="resourcehorizontalRuleColor"></div>
                <div class="mainIsi">
                <div class="mainIsiHeader">{{mainIsiHeader}}</div>
                <div id="contentbox" class="mainIsiContentheading" [innerHtml]="mainIsiContentheading"></div>
                <div class="mainIsiContentheadingMobile" [innerHtml]="mainIsiContentheadingMobile"></div>
                <div class="mainIsiContenttext" [innerHtml]="mainIsiContenttext"></div>
                <div class="mainIsiContentheading1" [innerHtml]="mainIsiContentheading1"></div>
                <div class="mainIsiContenttext1" [innerHtml]="mainIsiContenttext1"></div> 
                <div class="mainIsiContentheading2" [innerHtml]="mainIsiContentheading2"></div>
                <div class="mainIsiContenttext2" [innerHtml]="mainIsiContenttext2"></div> 
                </div>
                <div class="row">
                    <div class="text-end">
                        <span>
                            <span><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                    class="bi bi-chevron-double-up" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd"
                                        d="M7.646 2.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 3.707 2.354 9.354a.5.5 0 1 1-.708-.708l6-6z" />
                                    <path fill-rule="evenodd"
                                        d="M7.646 6.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 7.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z" />
                                </svg></span>
                            <button (click)="scrollToElementById('top')" class="elementor-button-text scrolltotop">BACK
                                TO
                                TOP</button>
                        </span>
                    </div>
                </div>
                <div class="footerres-container">
                    <div class="footerres">
                        <div class=""  *ngIf="logoimage">
                            <img src={{logoimage}} title="LandingImage" alt="LandingImage" class="imgcsl">
                        </div>
                        <div class="license" *ngIf="copyRights">
                            <div [innerHtml]="_returnHtmlFromRichText(copyRights)" id="foot_links"></div>
                        </div>
                    </div>
                    </div>
            </div>
        </div>
    </div>
    <app-chat-bot></app-chat-bot>
</div>
<div id="alertmssg" class="text-center" *ngIf="alertmssg">
    <h4 style="color:var(--primaryColor);padding-top:10%">No Matching Drug Found</h4>
</div>