import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Meta, Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { documentToHtmlString } from '@contentful/rich-text-html-renderer';
import { BLOCKS, INLINES } from '@contentful/rich-text-types';
import Player from '@vimeo/player';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { ToastrService } from 'ngx-toastr';
import { Observable, from, throwError, timer, zip } from 'rxjs';
import { concatMap, mergeMap, retryWhen } from 'rxjs/operators';
import { ContentfulService } from 'src/app/services/contentful.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-newlanding-page-preview',
  templateUrl: './newlanding-page-preview.component.html',
  styleUrls: ['./newlanding-page-preview.component.scss']
})
export class NewlandingPagePreviewComponent implements OnInit {

  panelOpenState = false;
  isLinear = false;
  firstFormGroup: FormGroup;
  secondFormGroup: FormGroup;
  step: number = 1;
  logo: any;
  mainHead: Observable<[]>;
  mainContent: Observable<[]>;
  mainSub: Observable<[]>;
  unordered: Observable<[]>;
  listed1: Observable<[]>;
  listed2: Observable<[]>;
  listed3: Observable<[]>;
  landFootImg: String;
  copyRights: Observable<[]>;
  siteKey = environment.siteKey
  sampleImg: string;
  prescribe: any;
  dynamicMsg: any;
  landingForm: FormGroup;
  captchaForm!: FormGroup;
  submitted = false;
  isSubmitted = false;
  reqId: any
  indication: any;
  isi: any;
  sponseredhero_id: any;
  resid: string;
  landingpageid: string;
  heading_id: any;
  drugdata: any;
  versionone_med: any;
  versionone_medname: any;
  urllink: any;
  result2: any;
  seotitle: any;
  seodes: any;
  hidePagesFromSearchEnginesNoindex: any;
  excluedLinksFromSearchRankingsNofollow: any;
  pagekeywords: any;
  qsaUrl: any;
  drug: any;
  seoid: any;
  content_id: any;
  content_id2: any;
  content_id3: any;
  isi_id: any;
  presid: any;
  branddiseaseResources_id: any;
  copyRights_id: any;
  sessionid: any;
  sid: any;
  footerimage_id: any;
  sponserdherores: any;
  seodatares: any;
  resourcedatares: any;
  shortstickyisi: any;
  resourceprimarycolor: any;
  resourcetertiaryColor: any;
  resourcesecondarycolor: any;
  gradient1: any;
  gradient2: any;
  gradient3: any;
  footgradient1: any;
  footgradient2: any;
  gradientrotation2: any;
  footgradientrotation2: any;
  resourcefontColor: any;
  resourcefontLinkColor: any;
  resourcefontLinkRolloverColor: any;
  brandresourceres: any;
  presinfo_id: any;
  presinternalname: any;
  p_file: any;
  resourceh1color: any;
  resourcehorizontalRule: any;
  resourcebuttonBackgroundRollOverColor: any;
  resourceh2color: any;
  resourceh3color: any;
  resourcebuttonBackgroundColor: any;
  resourcebuttonFontColor: any;
  resourcebuttonRolloverFontColor: any;
  resourcebuttonBackgroundActiveColor: any;
  resourcebuttonActiveFontColor: any;
  resourcefontLink: any;
  resourcefontFamily: any;

  readonly SITE_KEY = environment.siteKey;
  action = "register";

  token: string = undefined;
  resourceform_url: any;
  resourceform_urlnew: any;
  copyblocklist: any;
  resourcebrandFooterBackgroundColor: any;
  headerIndicationHeaderFontSize: any;
  headerIndicationCopyFontSize: any;
  headerIndicationFontColor: any;
  isiHeadersFontSize: any;
  isiHeadersColors: any;
  segments: any;
  drungnameinurl: string;
  landingpageemailbody: string;
  landingpageemailbodynew: any;
  landingpageemailsubject: any;
  references: any;
  copyBlockFootnotes: any;
  alertmssg: boolean;
  allcontents: boolean;
  CTA_id: any;
  CTAButtonidarray: any;
  CTAbuttonarray: any[];
  videoUrl: any;

  @ViewChild('playerContainer', { static: false }) playerContainer: ElementRef;
  videoTitle: any;
  vimeo_video: boolean;
  disclaimer: any;
  isiTextFontSize: any;
  bodyTextFontSize: any;
  bodyTextFontWeight: any;
  isiTextFontWeight: any;
  bodyTextLineHeight: any;
  isiTextLineHeight: any;
  linkButtons: any[];
  nonLinkButtons: any[];

  nonLinkSelectedButtonIndex: number = -1;
  nonLinkHoveredIndex: number = -1;

  linkSelectedButtonIndex: number = -1;
  linkHoveredIndex: number = -1;
  contentBelowVideoImage: any;
  headingText: string;
  contentBelowText: any;
  contentBelowrefText: any;
  contentheadingFontSize: any;
  contentheadingAlignment: any;
  contenttextFontSize: any;
  contenttextAlignment: any;
  contenttextReferencesFontSize: any;
  contenttextReferencesAlignment: any;
  imageOnlyAlignment: any;
  dynamicSystemMessages_id: any;
  getdynamicSystemMessages: any;
  topheading: any;
  topheadingtext: any;
  heading: string;
  topheadingtextMobile: string;
  headingMobile: string;
  headingAlignment: string;
  headingFontSize: any;
  headingFontColor: any;
  headingAlignmentMobile: any;
  headingFontSizeMobile: any;
  toptextAlignment: any;
  toptextFontSize: any;
  toptextFontColor: any;
  toptextAlignmentMobile: any;
  toptextFontSizeMobile: any;
  topcontentwidth: string;
  topcontentbg: string;
  topcontentpadding: string;
  brandheading_id: any;
  aboveBrandLogo: any;
  aboveBrandcolumns1: any;
  aboveBrandbg: any;
  columnBlockPadding: any;
  aboveBrandwidth: any;
  columnContent: any;
  dropdownMenu: any;
  aboveBrandtext: string;
  textFontColor: any;
  abovetextAlignment: any;
  textFontSize: any;
  dropdownMenuicon: any;
  dropdownMenuiconurl: any;
  branddropdownLinks: any;
  branddropdowntext: any;
  brandnavigationLink: any;
  dropdownBackgroundColor: any;
  dropdownLinkRolloverBackgroundColor: any;
  dropdownFontColor: any;
  abovetextFontSize: any;
  brandheaderalignment: string;
  p_filebg: any;
  p_filewidth: any;
  p_filetextAlignment: any;
  p_filefontSize: any;
  p_filefontColor: any;
  p_filetextBlockPadding: any;
  textBoxIndication: string;
  textBoxIndicationfontSizeMobile: any;
  textBoxIndicationlineHeightMobile: any;
  textBlockMargin: string;
  textBoxIndicationtextAlignment: any;
  textBoxIndicationfontSize: any;
  textBoxIndicationlineHeight: any;
  textBoxIndicationfontColor: any;
  columnContenttext: string;
  sampleImg1: any;
  containerContenttext: string;
  containerContenttext1: string;
  columnContent1: any;
  columnContent1img: any;
  columnContent1imgurl: any;
  containerContentheading: any;
  privacyContentlabel: string;
  content_idnew: any;
  firstname: string;
  lastName: string;
  Email: string;
  NPI: string;
  shortStickyIsiImportantSafetyInformationText: any;
  textBoxIndicationtext: string;
  textBoxIndicationactionbuttonText: string;
  buttonLink: any;
  customButtonBackgroundColor: any;
  customButtonTextColor: any;
  buttonBorderRadius: any;
  buttonPadding: any;
  buttonWidth: any;
  buttonHeight: any;
  customButtonBackgroundRolloverColor: any;
  customButtonTextRolloverColor: any;
  IndicationtextfontColor: any;
  IndicationtextlineHeight: any;
  IndicationtexttextAlignment: any;
  IndicationtextfontSize: any;
  IndicationtexttextBlockMargin: any;
  IndicationtextfontSizeMobile: any;
  IndicationlineHeightMobile: any;
  containerContentbg: any;
  containerContentradius: any;
  containerContentpadding: any;
  containerContentwidth: any;
  containerContenttextcopyFont: any;
  containerContenttextcopyFontSize: any;
  containerContenttextcopyFontColor: any;
  containerContenttextcopyTextAlign: any;
  containerContenttextcopyFontSizeMobile: any;
  containerContenttextcopyFont1: any;
  containerContenttextcopyFontSize1: any;
  containerContenttextcopyFontColor1: any;
  containerContenttextcopyTextAlign1: any;
  containerContenttextcopyFontSizeMobile1: any;
  columnContent1textFontSize: any;
  columnContent1textAlignment: any;
  columnContent1textFontColor: any;
  columnContent2containerWidth: any;
  borderWidth: any;
  borderColor: any;
  borderRadius: any;
  containerPadding: any;
  columnContentlineHeight: any;
  columnContenttextcopyFontSize: any;
  columnContenttextcopyFontColor: any;
  columnContenttextcopyTextAlign: any;
  columnContenttextBlockMargin: any;
  columnContenttexttextBlockPadding: any;
  columnContentlineHeightMobile: any;
  columnContenttextcopyFontSizeMobile: any;
  footer_id: any;
  copyrighttextAlignment: any;
  copyrighttextAlignmentMobile: any;
  copyrightfooterContentAlignment: any;
  indicationtextContent: string;
  indicationtextContenttext: string;
  indicationtextContenttext1: string;
  columnContent1width: any;
  columnContent1margin: any;
  columnContent1padding: any;
  buttonCornerRadius: any;
  mainIsiContentheading: string;
  mainIsiContenttext: string;
  mainIsiContentheading1: string;
  mainIsiContenttext1: string;
  mainIsiHeader: string;
  mainIsiMargin: any;
  mainIsiPadding: any;
  mainIsiHeaderFontSize: any;
  mainIsiHeaderFontSizeMobile: any;
  mainIsiHeaderFontWeight: any;
  mainIsiHeaderFontColor: any;
  mainIsiContenttextAlignment: any;
  mainIsiContentfontSize: any;
  mainIsiContentlineHeight: any;
  mainIsiContentfontSizeMobile: any;
  mainIsiContentlineHeightMobile: any;
  mainIsiContentfontColor: any;
  mainIsiContenttextBlockPadding: any;
  mainIsiContenttextBlockMargin: any;
  mainIsiContentwidth: any;
  mainIsiContenttextAlignment1: any;
  mainIsiContentfontSize1: any;
  mainIsiContentlineHeight1: any;
  mainIsiContentfontSizeMobile1: any;
  mainIsiContentlineHeightMobile1: any;
  mainIsiContentfontColor1: any;
  mainIsiContenttextBlockPadding1: any;
  mainIsiContenttextBlockMargin1: any;
  mainIsiContentwidth1: any;
  mainIsiContenttextAlignment2: any;
  mainIsiContentfontSize2: any;
  mainIsiContentlineHeight2: any;
  mainIsiContentfontSizeMobile2: any;
  mainIsiContentlineHeightMobile2: any;
  mainIsiContentfontColor2: any;
  mainIsiContenttextBlockPadding2: any;
  mainIsiContenttextBlockMargin2: any;
  mainIsiContentwidth2: any;
  mainIsiContenttextAlignment3: any;
  mainIsiContentfontSize3: any;
  mainIsiContentlineHeight3: any;
  mainIsiContentfontSizeMobile3: any;
  mainIsiContentlineHeightMobile3: any;
  mainIsiContentfontColor3: any;
  mainIsiContenttextBlockPadding3: any;
  mainIsiContenttextBlockMargin3: any;
  mainIsiContentwidth3: any;
  heading_backgroundColor: any;
  copypadding: any;
  heading_iconBackgroundColor: any;
  heading_iconRadius: any;
  heading_openIcon: any;
  arrowdown: any;
  heading_closeIcon: any;
  arrowup: any;
  textAlignment: any;
  textAlignmentMobile: any;
  textFontSize1: any;
  textFontSizeMobile1: any;
  textLineHeight: any;
  textLineHeightMobile: any;
  dyanamicMessagingWidth: any;
  topheadingAlignment: any;
  topheadingAlignmentMobile: any;
  iconWidth: any;
  iconHeight: any;
  iconWidthMobile: any;
  iconHeightMobile: any;
  branddiseaselogoWidth: any;
  branddiseaselogoHeight: any;
  branddiseaselogoWidthMobile: any;
  branddiseaselogoHeightMobile: any;
  dropdowniconWidth: any;
  dropdowniconHeight: any;
  dropdowniconWidthMobile: any;
  dropdowniconHeightMobile: any;
  sampleImg1Width: any;
  sampleImg1Height: any;
  sampleImg1WidthMobile: any;
  sampleImg1HeightMobile: any;
  columnVerticalAlignment: any;
  copyLineHeight: any;
  copyLineHeight1: any;
  mainIsiContentheading2: string;
  mainIsiContenttextAlignment4: any;
  mainIsiContentfontSize4: any;
  mainIsiContentlineHeight4: any;
  mainIsiContentfontSizeMobile4: any;
  mainIsiContentlineHeightMobile4: any;
  mainIsiContentfontColor4: any;
  mainIsiContenttextBlockPadding4: any;
  mainIsiContenttextBlockMargin4: any;
  mainIsiContentwidth4: any;
  mainIsiContenttext2: string;
  mainIsiContenttextAlignment5: any;
  mainIsiContentfontSize5: any;
  mainIsiContentlineHeight5: any;
  mainIsiContentfontSizeMobile5: any;
  mainIsiContentlineHeightMobile5: any;
  mainIsiContentfontColor5: any;
  mainIsiContenttextBlockPadding5: any;
  mainIsiContenttextBlockMargin5: any;
  mainIsiContentwidth5: any;
  logoimage: any;
  sampleImg1imageAlignment: any;
  iconpadding: any;
  p_filefontSizeMobile: any;
  abovetextFontSizeMobile: any;
  copyPadding: any;
  copyMargin: any;
  copyPadding1: any;
  copyMargin1: any;
  containerContenttextcopyTextAlignMobile: any;
  copyLineHeightMobile: any;
  copyPaddingMobile: any;
  copyMarginMobile: any;
  containerContenttextcopyTextAlignMobile1: any;
  copyLineHeightMobile1: any;
  copyPaddingMobile1: any;
  copyMarginMobile1: any;
  collapsibleDefaultPosition: any;
  imageOnlyAlignment1: any;
  imagePadding1: any;
  imageMargin1: any;
  imageWidth1: any;
  imageHeight1: any;
  imageWidthMobile1: any;
  imageHeightMobile1: any;
  columnContent1textFontSizeMobile: any;
  topcontentpaddingMobile: any;
  topcontentmargin: any;
  topcontentmarginMobile: any;
  iconMargin: any;
  branddiseasepadding: any;
  branddiseasemargin: any;
  indicationtextContentMobile: string;
  indicationtextContenttexttextMobile: string;
  indicationtextContenttext1textMobile: string;
  p_filetextBlockPaddingMobile: any;
  p_filetextBlockMargin: any;
  p_filetextBlockMarginMobile: any;
  p_filetextAlignmentMobile: any;
  p_filelineHeight: any;
  p_filelineHeightMobile: any;
  textBoxIndicationMobile: string;
  textBoxIndicationtextAlignmentMobile: any;
  textBoxIndicationtextBlockPadding: any;
  textBoxIndicationtextBlockMargin: any;
  textBoxIndicationtextBlockPaddingMobile: any;
  textBoxIndicationtextBlockMarginMobile: any;
  textBoxIndicationtextMobile: string;
  IndicationtexttextAlignmentMobile: any;
  IndicationtexttextBlockPadding: any;
  IndicationtexttextBlockPaddingMobile: any;
  IndicationtexttextBlockMarginMobile: any;
  columnContenttextMobile: string;
  columnContenttextcopyTextAlignMobile: any;
  columnContenttextBlockMarginMobile: any;
  columnContenttexttextBlockPaddingMobile: any;
  mainIsiContentheadingMobile: string;
  mainIsiContenttextAlignmentMobile: any;
  mainIsiContenttextBlockPaddingMobile: any;
  mainIsiContenttextBlockMarginMobile: any;
  mainIsiContentwidthMobile: any;
  columnContent1marginMobile: any;
  columnContent1paddingMobile: any;
  containerContentmarginMobile: any;
  containerContentpaddingMobile: any;
  containerContentmargin: any;
  containerMargin: any;
  containerPaddingMobile: any;
  containerMarginMobile: any;
  imagePaddingMobile1: any;
  imageMarginMobile1: any;
  p_fileMobile: string;


  constructor(private _formBuilder: FormBuilder, private contentfulservice: ContentfulService,
    private router: Router, private titleService: Title, private http: HttpClient,
    private toast: ToastrService, private metaservice: Meta, private reCaptchaV3Service: ReCaptchaV3Service, private actroute: ActivatedRoute) {


    this.actroute.url.subscribe(segments => {
      this.segments = segments.map(segment => segment.path).slice(1, 4);
    });

    this.getalllandingpagecontents();


  }


  ngOnInit() {
    this.alertmssg = false;
    this.allcontents = true;

    // Initialize forms first
    this.initializeForms();

    this.getSeoData();
  }

  private initializeForms() {
    // Initialize first form group
    this.firstFormGroup = this._formBuilder.group({
      firstCtrl: ['', Validators.required]
    });

    // Initialize second form group
    this.secondFormGroup = this._formBuilder.group({
      secondCtrl: ['', Validators.required]
    });

    // Initialize landing form
    this.landingForm = new FormGroup({
      toEmail: new FormControl('', [Validators.required]),
      firstName: new FormControl('', [Validators.required]),
      lastName: new FormControl('', [Validators.required]),
      npi: new FormControl(''),
      qsaUrl: new FormControl(''),
      drug: new FormControl('drugName'),
      fromEmail: new FormControl('<EMAIL>'),
      body: new FormControl(''),
      subject: new FormControl('Quick Support & Access (QSA) resource you ordered is here!')
    });

    // Initialize captcha form
    this.captchaForm = new FormGroup({
      acceptTerms: new FormControl('', [Validators.required]),
    });
  }

  getalllandingpagecontents() {
    let str = window.location.href;
    this.qsaUrl = str;
    let wordinurl = (this.qsaUrl.substring(this.qsaUrl.lastIndexOf('/') + 1));
    this.landingpageid = wordinurl
    console.log(this.landingpageid)

    this.contentfulservice.getAlldatapreview().subscribe(res => {
      let masterContent = res.items;
      // console.log(masterContent)
      let id = res.items.map(item => {
        //console.log(item);
        return item.sys.id;
      });
      //console.log(id)
      let filteredid = (filterItems(id, this.landingpageid))
      //console.log(filteredid.length)
      if (filteredid.length == 0) {
        this.alertmssg = true;
        this.allcontents = false;
      }
    });

    function filterItems(arr, query) {
      // return arr.filter((el) => el.toLowerCase().includes(query.toLowerCase()));
      return arr.filter(element => element.toLowerCase() === query.toLowerCase());
    }

    this.getlandingpagedata();
    //this.getlandingpagedatathroughresid();
    this.getHizentraimage();
    //this.getCslimage();
    this.getFooterimg();
    this.getColor();
    // this.getVimeo();

  }

  // retryWithDelay(maxRetry: number = 3, delay: number = 2000): (src: Observable<any>) => Observable<any> {
  //     console.log("retried")
  //     return (src: Observable<any>) =>
  //         src.pipe(
  //             retryWhen((errors: Observable<any>) =>
  //                 errors.pipe(
  //                     mergeMap((error, i) =>
  //                         i < maxRetry ? timer(delay) : throwError(error)
  //                     )
  //                 )
  //             )
  //         );
  // }

  scrollToElement(element: HTMLElement) {
    element.scrollIntoView({
      behavior: "smooth"
    });
  }

  private __getElementById(id: string): HTMLElement {
    //console.log("element id : ", id);
    const element = document.getElementById(id);
    return element;
  }

  scrollToElementById(id: string) {
    const element = this.__getElementById(id);
    this.scrollToElement(element);
  }

  next() {
    this.submitted = true;

    // Add defensive check with more detailed logging
    if (!this.captchaForm) {
      console.error('captchaForm is not initialized. This might be due to timing issues.');
      // Initialize the form if it's not already initialized
      this.captchaForm = new FormGroup({
        acceptTerms: new FormControl('', [Validators.required]),
      });
    }

    console.warn("Token", this.captchaForm);
    if (!this.captchaForm.invalid) {

      this.reCaptchaV3Service.execute(
        this.SITE_KEY,
        this.action,
        token => {
          this.token = token;
        },
        {
          useGlobalDomain: false
        }
      );
      this.step = this.step + 1;
    }
  }

  get f() {
    if (!this.captchaForm) {
      console.warn('captchaForm is not initialized in get f()');
      return {};
    }
    return this.captchaForm.controls;
  }
  get for() {
    return this.landingForm.controls;
  }

  getresourceformURL() {
    if (this.sponserdherores.fields.hasOwnProperty("ogeqsaColumnRight")) {
      this.content_id = this.sponserdherores.fields.ogeqsaColumnRight.sys.id;
    } else { console.log("ogeqsaColumnRight not exists") }
    this.contentfulservice.getdatapreview(this.content_id).subscribe(res => {

      let resourceform_id = res.fields.resourceForm.sys.id;
      //console.log(resourceform_id)
      this.contentfulservice.getdatapreview(resourceform_id).subscribe(res => {
        this.resourceform_url = res.fields.resourceUrlToRedirectToOnSubmit;
        //console.log(this.resourceform_url)
        if (res.fields.hasOwnProperty('emailContentSentOnSubmit')) {
          let body = res.fields.emailContentSentOnSubmit;
          this.landingpageemailbody = this._returnHtmlFromRichText(body)
          //console.log(this.landingpageemailbody)
        } else {
          console.log("emailContentSentOnSubmit not exits")
        }
        if (res.fields.hasOwnProperty('emailSubjectSentOnSubmit')) {
          let body = res.fields.emailSubjectSentOnSubmit;
          this.landingpageemailsubject = body
          console.log("this.landingpageemailbody", this.landingpageemailbody)
        } else {
          console.log("emailSubjectSentOnSubmit not exits")
        }
      })
    })
  }
  getresourceformURLnew() {
    if (this.sponserdherores.fields.hasOwnProperty("columnBlock")) {
      this.content_idnew = this.sponserdherores.fields.columnBlock.sys.id;
    } else { console.log("ogeqsacolumnBlock not exists") }
    this.contentfulservice.getdatapreview(this.content_idnew).subscribe(res => {
      // console.log("emailbodynew1",res)
      let columns2 = res.fields.columns[1].sys.id;
      this.contentfulservice.getdatapreview(columns2).subscribe(res => {
        // console.log("columns2",res)
        let columnContent = res.fields.columnContent[2].sys.id;
        this.contentfulservice.getdatapreview(columnContent).subscribe(res => {
          // console.log("columnContent",res)
          let containerContent = res.fields.containerContent[0].sys.id;
          this.contentfulservice.getdatapreview(containerContent).subscribe(res => {
            this.resourceform_urlnew = res.fields.resourceUrlToRedirectToOnSubmit;
            //console.log(this.resourceform_urlnew)
            if (res.fields.hasOwnProperty('emailContentSentOnSubmit')) {
              let body = res.fields.emailContentSentOnSubmit;
              this.landingpageemailbodynew = this._returnHtmlFromRichText(body)
              // console.log("landingpageemailbodynew1",this.landingpageemailbodynew)
            } else {
              console.log("emailContentSentOnSubmit not exits")
            }
            if (res.fields.hasOwnProperty('emailSubjectSentOnSubmit')) {
              let body = res.fields.emailSubjectSentOnSubmit;
              this.landingpageemailsubject = body
              // console.log("landingpageemailbodynew2",this.landingpageemailbodynew)
            } else {
              console.log("emailSubjectSentOnSubmit not exits")
            }
          });
        });
      });
    })
  }
  getCTAactionbutton() {
    //alert("executed")
    if (this.sponserdherores.fields.hasOwnProperty("branddiseaseHeader")) {
      this.CTA_id = this.sponserdherores.fields.branddiseaseHeader.sys.id;
      //alert(this.CTA_id)
      this.contentfulservice.getdata(this.CTA_id).subscribe(res => {
        //console.log(res)
        if (res.fields.hasOwnProperty("ctaActions")) {
          this.CTAButtonidarray = res.fields.ctaActions
          let CTAbuttonids = this.CTAButtonidarray.map(id => id.sys.id)
          //console.log(CTAbuttonids)

          let responseCTADataArray = [];
          const requests$ = [];

          let i;

          for (let i = 0; i < CTAbuttonids.length; i++) {
            const value = CTAbuttonids[i];
            const urlWithQuery = `${value}`;
            //console.log(urlWithQuery)
            if (urlWithQuery !== undefined) {
              requests$.push(from(this.contentfulservice.getdata(urlWithQuery)));
            }
          }

          zip(...requests$).pipe(
            concatMap(responses => {
              responseCTADataArray = [...responseCTADataArray, ...responses];
              if (responseCTADataArray.length === CTAbuttonids.length) {
                //console.log(responseCTADataArray)
                this.CTAbuttonarray = responseCTADataArray;
                //console.log(this.CTAbuttonarray)

                this.linkButtons = [];
                this.nonLinkButtons = [];

                for (let i = 0; i < this.CTAbuttonarray.length; i++) {
                  const item = this.CTAbuttonarray[i];
                  const buttonLink = item.fields.buttonLink;

                  // Check if the value is a link
                  const isButtonLink = this.isLink(buttonLink);

                  // Output the result
                  //console.log(`Button in object ${i + 1} is a link: ${isButtonLink}`);

                  if (isButtonLink) {
                    this.linkButtons.push(item);
                  } else {
                    this.nonLinkButtons.push(item);
                  }

                }

                //console.log("Link Buttons:", this.linkButtons);
                //console.log("Non-Link Buttons:", this.nonLinkButtons);
              }
              return [];
            })
          ).subscribe();
        }
        else {
          console.log("ctaActions buttons not exists")
        }
      })
    } else {
      console.log("branddiseaseheader not exists")
    }
  }

  isLink(value: string): boolean {
    // Regular expression pattern to match a URL format
    const urlPattern = /^(http|https):\/\/[^ "]+$/;

    // Use the pattern to test the value
    return urlPattern.test(value);
  }

  getVimeo() {
    if (this.sponserdherores.fields.hasOwnProperty('embeddedVideo')) {

      this.vimeo_video = true;
      let embededvideo = this.sponserdherores.fields.embeddedVideo.sys.id
      this.contentfulservice.getdata(embededvideo).subscribe(res => {
        this.videoUrl = res.fields.videoUrl;
        this.videoTitle = res.fields.videoTitle;
        const player = new Player(this.playerContainer.nativeElement, { url: this.videoUrl });
        player.play().then(() => {
        });
      })
    } else {
      console.log("video not exists")
      this.vimeo_video = false;
    }

  }

  getcontentBelowVideo() {
    if (this.sponserdherores.fields.hasOwnProperty("contentBelowVideo")) {
      console.log("getcontentBelowVideo", this.sponserdherores.fields.contentBelowVideo[0].sys.id);
      let contentBelowVideo = this.sponserdherores.fields.contentBelowVideo[0].sys.id
      this.contentfulservice.getdata(contentBelowVideo).subscribe(res => {
        console.log("getcontentBelowVideo res", res);
        this.headingText = res.fields.headingText
        this.contentBelowText = res.fields.text
        this.contentBelowrefText = res.fields.textReferences

        if (res.fields.hasOwnProperty("headingFontSize")) { this.contentheadingFontSize = res.fields.headingFontSize } else { console.log("headingFontSize not exists") }
        if (res.fields.hasOwnProperty("headingAlignment")) { this.contentheadingAlignment = res.fields.headingAlignment } else { console.log("headingAlignment not exists") }
        if (res.fields.hasOwnProperty("textFontSize")) { this.contenttextFontSize = res.fields.textFontSize } else { console.log("textFontSize not exists") }
        if (res.fields.hasOwnProperty("textAlignment")) { this.contenttextAlignment = res.fields.textAlignment } else { console.log("textAlignment not exists") }
        if (res.fields.hasOwnProperty("textReferencesFontSize")) { this.contenttextReferencesFontSize = res.fields.textReferencesFontSize } else { console.log("textReferencesFontSize not exists") }
        if (res.fields.hasOwnProperty("textReferencesAlignment")) { this.contenttextReferencesAlignment = res.fields.textReferencesAlignment } else { console.log("textReferencesAlignment not exists") }
        if (res.fields.hasOwnProperty("imageOnlyAlignment")) { this.imageOnlyAlignment = res.fields.imageOnlyAlignment } else { console.log("imageOnlyAlignment not exists") }



        document.documentElement.style.setProperty('--contentheadingFontSize', this.contentheadingFontSize ? this.contentheadingFontSize : "19px");
        document.documentElement.style.setProperty('--contentheadingAlignment', this.contentheadingAlignment ? this.contentheadingAlignment : "left");
        document.documentElement.style.setProperty('--contenttextFontSize', this.contenttextFontSize ? this.contenttextFontSize : "19px");
        document.documentElement.style.setProperty('--contenttextAlignment', this.contenttextAlignment ? this.contenttextAlignment : "left");
        document.documentElement.style.setProperty('--contenttextReferencesFontSize', this.contenttextReferencesFontSize ? this.contenttextReferencesFontSize : "19px");
        document.documentElement.style.setProperty('--contenttextReferencesAlignment', this.contenttextReferencesAlignment ? this.contenttextReferencesAlignment : "left");
        document.documentElement.style.setProperty('--imageOnlyAlignment', this.imageOnlyAlignment ? this.imageOnlyAlignment : "left");
        let contentBelowVideoimage = res.fields.image.sys.id
        if (contentBelowVideoimage !== undefined) {
          this.contentfulservice.getAssetspreview('/' + contentBelowVideoimage + '/').subscribe(res => {
            console.log("getcontentBelowVideo image", res);
            this.contentBelowVideoImage = res.fields.file.url;
            console.log("getcontentBelowVideo resimage", this.contentBelowVideoImage);

          })
        }
      })


    }
  }
  userData() {
    this.isSubmitted = true;

    if (!this.landingForm.invalid) {
      let url = 'https://api.qsasupport.com/v1/resources/request';
      this.http.post(url, this.landingForm.value).subscribe(res => {
        this.sessionid = res;
        this.sid = this.sessionid._id;

        this.toast.success('Submitted');

        let url = this.resourceform_url + `${this.sid}`
        const updatedHtmlContent = this.landingpageemailbody.replace(/\[URL Link\]/g, `<a href="${url}" target="_blank">${url}</a>`);
        this.landingpageemailbody = updatedHtmlContent;
        this.landingForm.get('body').setValue(encodeURI(this.landingpageemailbody));
        this.landingForm.get('subject').setValue(this.landingpageemailsubject)
        //console.log(encodeURI(this.landingpageemailbody))
        //this.landingForm.value.body = encodeURI(this.landingpageemailbody);
        ///this.landingForm.get('body').setValue(this.resourceform_url + `${this.sid}`);
        this.landingForm.get('qsaUrl').setValue(this.resourceform_url + `${this.sid}`);
        //console.log(this.landingForm.value)
        let mailurl = 'https://api.qsasupport.com/v1/resources/access';
        this.http.post(mailurl, this.landingForm.value).subscribe(res => {
          //console.log(res);
          //window.location.href = this.resourceform_url + `${this.sid}`;
          window.open(this.resourceform_url + `${this.sid}`, '_blank');

          this.isSubmitted = false;
          this.submitted = false;
          this.landingForm.reset();
          this.step = this.step - 1;
          this.captchaForm.reset(); // Reset form after second request is executed
        });
      });
    }
  }

  userDatanew() {
    this.isSubmitted = true;

    if (!this.landingForm.invalid) {
      let url = 'https://api.qsasupport.com/v1/resources/request';
      this.http.post(url, this.landingForm.value).subscribe(res => {
        this.sessionid = res;
        this.sid = this.sessionid._id;

        this.toast.success('Submitted');

        let url = this.resourceform_urlnew + `${this.sid}`
        const updatedHtmlContent = this.landingpageemailbodynew.replace(/\[URL Link\]/g, `<a href="${url}" target="_blank">${url}</a>`);
        this.landingpageemailbodynew = updatedHtmlContent;
        this.landingForm.get('body').setValue(encodeURI(this.landingpageemailbodynew));
        this.landingForm.get('subject').setValue(this.landingpageemailsubject)
        //console.log(encodeURI(this.landingpageemailbodynew))
        //this.landingForm.value.body = encodeURI(this.landingpageemailbodynew);
        ///this.landingForm.get('body').setValue(this.resourceform_urlnew + `${this.sid}`);
        this.landingForm.get('qsaUrl').setValue(this.resourceform_urlnew + `${this.sid}`);
        //console.log(this.landingForm.value)
        let mailurl = 'https://api.qsasupport.com/v1/resources/access';
        this.http.post(mailurl, this.landingForm.value).subscribe(res => {
          //console.log(res);
          //window.location.href = this.resourceform_urlnew + `${this.sid}`;
          window.open(this.resourceform_urlnew + `${this.sid}`, '_blank');

          this.isSubmitted = false;
          this.submitted = false;
          this.landingForm.reset();
          this.step = this.step - 1;
          this.captchaForm.reset(); // Reset form after second request is executed
        });
      });
    }
  }

  getlandingpagedata() {
    this.contentfulservice.getdatapreview(this.landingpageid).subscribe(res => {
      // alert("executed landingpagedata")
      this.seodatares = res;
      this.dynamicSystemMessages();
      this.getsponseredhero();
      this.getInformation();
      this.getSeoData();
    })
  }

  dynamicSystemMessages() {
    console.log("dynamicSystemMessages")
    if (this.seodatares.fields.hasOwnProperty("dynamicSystemMessages")) {
      this.dynamicSystemMessages_id = this.seodatares.fields.dynamicSystemMessages.sys.id;
    } else {
      console.log("dynamicSystemMessages not exsits")
    }
    this.contentfulservice.getdatapreview(this.dynamicSystemMessages_id).subscribe(res => {
      if (res.fields.hasOwnProperty("backgroundColor")) {
        this.heading_backgroundColor = res.fields.backgroundColor.value;
        document.documentElement.style.setProperty('--heading_backgroundColor', this.heading_backgroundColor);
      };

      if (res.fields.hasOwnProperty("iconBackgroundColor")) {
        this.copypadding = res.fields.padding;
        document.documentElement.style.setProperty('--copypadding', this.copypadding);
      };
      if (res.fields.hasOwnProperty("iconBackgroundColor")) {
        this.heading_iconBackgroundColor = res.fields.iconBackgroundColor.value;
        document.documentElement.style.setProperty('--heading_iconBackgroundColor', this.heading_iconBackgroundColor);
      };
      if (res.fields.hasOwnProperty("iconBackgroundBorderRadius")) {
        this.heading_iconRadius = res.fields.iconBackgroundBorderRadius;
        document.documentElement.style.setProperty('--heading_iconRadius', this.heading_iconRadius);
      };

      this.collapsibleDefaultPosition = res.fields.collapsibleDefaultPosition;
      this.isDropdownOpen2 = this.collapsibleDefaultPosition === 'Closed' ? true : false;


      if (res.fields.hasOwnProperty("openIcon")) {
        this.heading_openIcon = res.fields.openIcon.sys.id;
        this.contentfulservice.getassets(this.heading_openIcon).subscribe(res => {
          this.arrowdown = res.fields.file.url;
        });


        this.iconpadding = res.fields.iconPadding;
        document.documentElement.style.setProperty('--iconpadding', this.iconpadding);
        this.iconMargin = res.fields.iconMargin;
        document.documentElement.style.setProperty('--iconMargin', this.iconMargin);
        this.iconWidth = res.fields.iconWidth;
        document.documentElement.style.setProperty('--iconWidth', this.iconWidth);
        this.iconHeight = res.fields.iconHeight;
        document.documentElement.style.setProperty('--iconHeight', this.iconHeight);
        this.iconWidthMobile = res.fields.iconWidthMobile;
        document.documentElement.style.setProperty('--iconWidthMobile', this.iconWidthMobile);
        this.iconHeightMobile = res.fields.iconHeightMobile;
        document.documentElement.style.setProperty('--iconHeightMobile', this.iconHeightMobile);
      };
      if (res.fields.hasOwnProperty("closeIcon")) {
        this.heading_closeIcon = res.fields.closeIcon.sys.id;
        this.contentfulservice.getassets(this.heading_closeIcon).subscribe(res => {
          this.arrowup = res.fields.file.url;
        });
      };
      this.topheading = res.fields.messages[0].sys.id;
      this.contentfulservice.getdatapreview(this.topheading).subscribe(res => {
        console.log("topheading", res);
        this.heading = this._returnHtmlFromRichText(res.fields.heading);
        this.headingMobile = this._returnHtmlFromRichText(res.fields.headingMobile);
        this.topheadingtext = this._returnHtmlFromRichText(res.fields.text);
        this.topheadingtextMobile = this._returnHtmlFromRichText(res.fields.textMobile);

        this.dyanamicMessagingWidth = res.fields.dyanamicMessagingWidth;
        document.documentElement.style.setProperty('--dyanamicMessagingWidth', this.dyanamicMessagingWidth);
        this.topheadingAlignment = res.fields.headingAlignment;
        document.documentElement.style.setProperty('--topheadingAlignment', this.topheadingAlignment);
        this.topheadingAlignmentMobile = res.fields.headingAlignmentMobile;
        document.documentElement.style.setProperty('--topheadingAlignmentMobile', this.topheadingAlignmentMobile);
        this.textAlignment = res.fields.textAlignment
        document.documentElement.style.setProperty('--textAlignment', this.textAlignment);
        this.textAlignmentMobile = res.fields.textAlignmentMobile
        document.documentElement.style.setProperty('--textAlignmentMobile', this.textAlignmentMobile);
        this.textFontSize1 = res.fields.textFontSize
        document.documentElement.style.setProperty('--textFontSize1', this.textFontSize1);
        this.textFontSizeMobile1 = res.fields.textFontSizeMobile
        document.documentElement.style.setProperty('--textFontSizeMobile1', this.textFontSizeMobile1);
        this.textLineHeight = res.fields.textLineHeight
        document.documentElement.style.setProperty('--textLineHeight', this.textLineHeight);
        this.textLineHeightMobile = res.fields.textLineHeightMobile
        document.documentElement.style.setProperty('--textLineHeightMobile', this.textLineHeightMobile);
        this.headingAlignment = res.fields.headingAlignment;
        document.documentElement.style.setProperty('--headingAlignment', this.headingAlignment);
        this.headingFontSize = res.fields.headingFontSize;
        document.documentElement.style.setProperty('--headingFontSize', this.headingFontSize);
        this.headingFontColor = res.fields.headingFontColor.value;
        document.documentElement.style.setProperty('--headingFontColor', this.headingFontColor);

        this.headingAlignmentMobile = res.fields.headingAlignmentMobile;
        document.documentElement.style.setProperty('--headingAlignmentMobile', this.headingAlignmentMobile);
        this.headingFontSizeMobile = res.fields.headingFontSizeMobile;
        document.documentElement.style.setProperty('--headingFontSizeMobile', this.headingFontSizeMobile);

        this.toptextAlignment = res.fields.textAlignment;
        document.documentElement.style.setProperty('--toptextAlignment', this.toptextAlignment);
        this.toptextFontSize = res.fields.textFontSize;
        document.documentElement.style.setProperty('--toptextFontSize', this.toptextFontSize);
        this.toptextFontColor = res.fields.textFontColor.value;
        document.documentElement.style.setProperty('--toptextFontColor', this.toptextFontColor);

        this.toptextAlignmentMobile = res.fields.textAlignmentMobile;
        document.documentElement.style.setProperty('--toptextAlignmentMobile', this.toptextAlignmentMobile);
        this.toptextFontSizeMobile = res.fields.textFontSizeMobile;
        document.documentElement.style.setProperty('--toptextFontSizeMobile', this.toptextFontSizeMobile);
        this.toptextFontColor = res.fields.textFontColor.value;

        this.topcontentwidth = res.fields.dyanamicMessagingWidth;
        document.documentElement.style.setProperty('--topcontentwidth', this.topcontentwidth);
      });

      this.topcontentbg = res.fields.backgroundColor.value;
      document.documentElement.style.setProperty('--topcontentbg', this.topcontentbg);
      this.topcontentpadding = res.fields.padding;
      document.documentElement.style.setProperty('--topcontentpadding', this.topcontentpadding);
      this.topcontentpaddingMobile = res.fields.paddingMobile;
      document.documentElement.style.setProperty('--topcontentpaddingMobile', this.topcontentpaddingMobile);
      this.topcontentmargin = res.fields.margin;
      document.documentElement.style.setProperty('--topcontentmargin', this.topcontentmargin);
      this.topcontentmarginMobile = res.fields.marginMobile;
      document.documentElement.style.setProperty('--topcontentmarginMobile', this.topcontentmarginMobile);
    });
  }
  getsponseredhero() {
    this.sponseredhero_id = this.seodatares.fields.sponsoredHero.sys.id;
    this.contentfulservice.getdatapreview(this.sponseredhero_id).subscribe(res => {
      // alert("executed sponsered hero")
      // console.log("getbranddiseaseHeader",res);
      this.sponserdherores = res;
      // console.log(this.sponserdherores)
      if (res.fields.hasOwnProperty("footer")) {
        this.copyRights_id = res.fields.footer.sys.id;
      } else { console.log("footer not exists") }
      this.getCopyright();
      // console.log("getbranddiseaseHeader",res);
      this.getbranddiseaseHeader();
      this.getprescribingInformation();
      this.gettextBoxBelowHeaderIndication();
      this.getcolumnBlock();
      this.getfootercontent();
      this.getMainhead();
      this.getPrivacyContent();
      this.getListedtext();
      this.getSampleimage();
      this.getDynamicmsg();
      this.getresourceformURL();
      this.getresourceformURLnew();
      this.getCTAactionbutton();
      this.getreferences();
      this.getdisclaimer();
      this.getVimeo();
      this.getcontentBelowVideo();
    })
  }
  getfootercontent() {
    if (this.sponserdherores.fields.hasOwnProperty("footer")) {
      this.footer_id = this.sponserdherores.fields.footer.sys.id;
      this.contentfulservice.getdatapreview(this.footer_id).subscribe(res => {
        this.copyrighttextAlignment = res.fields.textAlignment;
        document.documentElement.style.setProperty('--copyrighttextAlignment', this.copyrighttextAlignment);
        this.copyrighttextAlignmentMobile = res.fields.textAlignmentMobile;
        document.documentElement.style.setProperty('--copyrighttextAlignmentMobile', this.copyrighttextAlignmentMobile);
        this.copyrightfooterContentAlignment = res.fields.footerContentAlignment;
        document.documentElement.style.setProperty('--copyrightfooterContentAlignment', this.copyrightfooterContentAlignment);
      });
    }
  }
  getSeoData() {
    if (this.seodatares.fields.hasOwnProperty("pageTitle")) {
      this.titleService.setTitle(this.seodatares.fields.pageTitle)
    } else { console.log('pageTitle not exists') }
    if (this.seodatares.fields.hasOwnProperty("seoMetadata")) {
      this.seoid = this.seodatares.fields.seoMetadata.sys.id;
      this.contentfulservice.getdatapreview(this.seoid).subscribe(res => {
        this.seotitle = res.fields.seoTitle;
        this.seodes = res.fields.seoDescription;
        this.hidePagesFromSearchEnginesNoindex = res.fields.hidePagesFromSearchEnginesNoindex;
        this.excluedLinksFromSearchRankingsNofollow = res.fields.excluedLinksFromSearchRankingsNofollow;
        this.pagekeywords = res.fields.keywords;
        //console.log(this.seotitle, this.seodes, this.hidePagesFromSearchEnginesNoindex, this.excluedLinksFromSearchRankingsNofollow, this.pagekeywords )

        this.metaservice.addTag({ name: 'description', content: this.seodes });
        this.metaservice.addTag({ name: 'application-name', content: this.seotitle });
        this.metaservice.addTag({ name: 'keywords', content: this.pagekeywords });
        this.metaservice.addTag({ name: 'noindex', content: this.hidePagesFromSearchEnginesNoindex });
        this.metaservice.addTag({ name: 'nofollow', content: this.excluedLinksFromSearchRankingsNofollow });
      })
    } else {
      console.log("seoMetadata not exists ")
    }
  }



  getHizentraimage() {
    this.contentfulservice.getdatapreview(this.landingpageid).pipe(
      mergeMap(res => {
        let bid = res.fields.sponsoredHero.sys.id;
        return this.contentfulservice.getdatapreview(bid);
      }),
      mergeMap(res => {
        let bid1 = res.fields.branddiseaseHeader.sys.id;
        return this.contentfulservice.getdatapreview(bid1);
      }),

      mergeMap(res => {
        let bid2 = res.fields.logoimage.sys.id;
        return this.contentfulservice.getdatapreview(bid2);
      }),
      mergeMap(res => {
        let bid3 = res.fields.imageWrapper.sys.id;
        return this.contentfulservice.getdatapreview(bid3);
      }),
      mergeMap(res => {
        this.branddiseaselogoWidth = res.fields.imageWidth;
        document.documentElement.style.setProperty('--branddiseaselogoWidth', this.branddiseaselogoWidth);
        this.branddiseaselogoHeight = res.fields.imageHeight;
        document.documentElement.style.setProperty('--branddiseaselogoHeight', this.branddiseaselogoHeight);
        this.branddiseaselogoWidthMobile = res.fields.imageWidthMobile;
        document.documentElement.style.setProperty('--branddiseaselogoWidthMobile', this.branddiseaselogoWidthMobile);
        this.branddiseaselogoHeightMobile = res.fields.imageHeightMobile;
        document.documentElement.style.setProperty('--branddiseaselogoHeightMobile', this.branddiseaselogoHeightMobile);
        this.branddiseasepadding = res.fields.padding;
        document.documentElement.style.setProperty('--branddiseasepadding', this.branddiseasepadding);
        this.branddiseasemargin = res.fields.margin;
        document.documentElement.style.setProperty('--branddiseasemargin', this.branddiseasemargin); 

        let bid4 = res.fields.image.sys.id;
        return this.contentfulservice.getAssetspreview('/' + bid4 + '/');
      }),
      retryWhen(errors => {
        return errors.pipe(
          mergeMap(error => {
            if (error.status === 429) {
              //console.log("got 429, so retried")
              const retryAfterSeconds = Math.floor(Math.random() * 10) + 1; // Wait 1-10 seconds
              return timer(retryAfterSeconds * 1000);
            }
            return throwError(error);
          })
        );
      })
    ).subscribe(imgData => {
      this.logo = imgData.fields.file.url;
    });
  }

  getFooterimg() {
    this.contentfulservice.getdatapreview(this.landingpageid).pipe(
      mergeMap(res => {
        let bid = res.fields.sponsoredHero.sys.id;
        return this.contentfulservice.getdatapreview(bid);
      }),
      mergeMap(res => {
        let bid1 = res.fields.footer.sys.id;
        return this.contentfulservice.getdatapreview(bid1);
      }),
      mergeMap(res => {
        let bid2 = res.fields.logoimage[0].sys.id;
        return this.contentfulservice.getdatapreview(bid2);
      }),
      mergeMap(res => {
        let bid3 = res.fields.imageWrapper.sys.id;
        return this.contentfulservice.getdatapreview(bid3);
      }),
      mergeMap(res => {
        let bid4 = res.fields.image.sys.id;
        return this.contentfulservice.getAssetspreview('/' + bid4 + '/');
      }),
      retryWhen(errors => {
        return errors.pipe(
          mergeMap(error => {
            if (error.status === 429) {
              //console.log("got 429, so retried")
              const retryAfterSeconds = Math.floor(Math.random() * 10) + 1; // Wait 1-10 seconds
              return timer(retryAfterSeconds * 1000);
            }
            return throwError(error);
          })
        );
      })
    ).subscribe(imgData => {
      this.landFootImg = imgData.fields.file.url;
    });
  }
  getbranddiseaseHeader() {
    if (this.sponserdherores.fields.hasOwnProperty("branddiseaseHeader")) {
      this.brandheading_id = this.sponserdherores.fields.branddiseaseHeader.sys.id;
      this.contentfulservice.getdatapreview(this.brandheading_id).subscribe(res => {
        // console.log("getbranddiseaseHeader",res);

        if (res.fields.hasOwnProperty("aboveBrandLogo")) {
          this.aboveBrandLogo = res.fields.aboveBrandLogo[0].sys.id;
          this.contentfulservice.getdatapreview(this.aboveBrandLogo).subscribe(res => {
            if (res.fields.columns[0]) {
              this.aboveBrandcolumns1 = res.fields.columns[0].sys.id;
              this.contentfulservice.getdatapreview(this.aboveBrandcolumns1).subscribe(res => {
                this.columnContent = res.fields.columnContent[0].sys.id;
                this.contentfulservice.getdatapreview(this.columnContent).subscribe(res => {
                  console.log("columnContent", res);
                  this.aboveBrandtext = this._returnHtmlFromRichText(res.fields.text);
                  this.dropdownMenuicon = res.fields.image.sys.id;
                  this.contentfulservice.getassets(this.dropdownMenuicon).subscribe(res => {
                    this.dropdownMenuiconurl = res.fields.file.url;
                    console.log("columnContenturl", res);
                  });


                  this.dropdowniconWidth = res.fields.imageWidth;
                  document.documentElement.style.setProperty('--dropdowniconWidth', this.dropdowniconWidth);
                  this.dropdowniconHeight = res.fields.imageHeight;
                  document.documentElement.style.setProperty('--dropdowniconHeight', this.dropdowniconHeight);
                  this.dropdowniconWidthMobile = res.fields.imageWidthMobile;
                  document.documentElement.style.setProperty('--dropdowniconWidthMobile', this.dropdowniconWidthMobile);
                  this.dropdowniconHeightMobile = res.fields.imageHeightMobile;
                  document.documentElement.style.setProperty('--dropdowniconHeightMobile', this.dropdowniconHeightMobile);

                  this.textFontColor = res.fields.textFontColor.value;
                  document.documentElement.style.setProperty('--textFontColor', this.textFontColor);
                  this.abovetextAlignment = res.fields.textAlignment;
                  document.documentElement.style.setProperty('--abovetextAlignment', this.abovetextAlignment);
                  this.abovetextFontSize = res.fields.textFontSize;
                  document.documentElement.style.setProperty('--abovetextFontSize', this.abovetextFontSize);
                  this.abovetextFontSizeMobile = res.fields.textFontSizeMobile;
                  document.documentElement.style.setProperty('--abovetextFontSizeMobile', this.abovetextFontSizeMobile);
                });
                this.dropdownMenu = res.fields.dropdownMenu.sys.id;
                this.contentfulservice.getdatapreview(this.dropdownMenu).subscribe(res => {
                  console.log("dropdownMenu", res);
                  this.brandnavigationLink = res.fields.dropdownLinks[0].sys.id;
                  this.contentfulservice.getdatapreview(this.brandnavigationLink).subscribe(res => {
                    this.branddropdownLinks = res.fields.navigationLink;
                    this.branddropdowntext = res.fields.navigationText;
                  });
                  this.dropdownBackgroundColor = res.fields.dropdownBackgroundColor.value;
                  document.documentElement.style.setProperty('--dropdownBackgroundColor', this.dropdownBackgroundColor);

                  this.dropdownLinkRolloverBackgroundColor = res.fields.dropdownLinkRolloverBackgroundColor.value;
                  document.documentElement.style.setProperty('--dropdownLinkRolloverBackgroundColor', this.dropdownLinkRolloverBackgroundColor);

                  this.dropdownFontColor = res.fields.dropdownFontColor.value;
                  document.documentElement.style.setProperty('--dropdownFontColor', this.dropdownFontColor);
                });
              });
            } else {
              console.log("aboveBrandLogo columns[0] not exist")
            }
            this.aboveBrandbg = res.fields.backgroundColor.value;
            document.documentElement.style.setProperty('--aboveBrandbg', this.aboveBrandbg);
            this.columnBlockPadding = res.fields.columnBlockPadding;
            document.documentElement.style.setProperty('--columnBlockPadding', this.columnBlockPadding);
            this.aboveBrandwidth = res.fields.width;
            document.documentElement.style.setProperty('--aboveBrandwidth', this.aboveBrandwidth);
          });
        } else { console.log("aboveBrandLogo not exists"); }


        this.brandheaderalignment = res.fields.alignment
        document.documentElement.style.setProperty('--brandheaderalignment', this.brandheaderalignment);
      })
    } else { console.log("branddiseaseHeader not exists"); }
  }


  getMainhead() {
    if (this.sponserdherores.fields.hasOwnProperty("mainTextBlock")) {
      this.heading_id = this.sponserdherores.fields.mainTextBlock.sys.id;
    } else { console.log("MainTextBlock not exists") }
    this.contentfulservice.getdatapreview(this.heading_id).subscribe(res => {
      this.mainHead = res.fields.heading;
      this.mainContent = res.fields.copyBlock;
      //console.log(this.mainHead, this.mainContent);
    })
  }
  privacyContent: any;
  getPrivacyContent() {
    if (this.sponserdherores.fields.hasOwnProperty("ogeqsaColumnRight")) {
      this.content_id = this.sponserdherores.fields.ogeqsaColumnRight.sys.id;
    } else {
      // console.log("ogeqsaColumnRight not exists") 
    }

    this.contentfulservice.getdatapreview(this.content_id).subscribe(res => {

      let resourceform_id = res.fields.resourceForm.sys.id;
      //console.log(resourceform_id)
      this.contentfulservice.getdatapreview(resourceform_id).subscribe(res => {
        let resourceset_id = res.fields.resourceSet[0].sys.id;
        //console.log(resourceset_id)
        this.contentfulservice.getdatapreview(resourceset_id).subscribe(res => {
          let privacy_id = res.fields.formResources[0].sys.id;
          this.contentfulservice.getdatapreview(privacy_id).subscribe(res => {
            this.privacyContent = res.fields.label;
          })
        })
      })
    })
  }



  getListedtext() {

    if (this.sponserdherores.fields.hasOwnProperty("ogeqsaColumnRight")) {
      this.content_id2 = this.sponserdherores.fields.ogeqsaColumnRight.sys.id;
    } else {
      // console.log("ogeqsaColumnRight not exists") 
    }
    //alert(this.content_id2)
    this.contentfulservice.getdatapreview(this.content_id2).subscribe(res => {
      let maintext_id = res.fields.mainTextBlock.sys.id;
      this.contentfulservice.getdatapreview(maintext_id).subscribe(res => {
        this.copyblocklist = res.fields.copyBlock;
        if (res.fields.heading !== undefined) {
          this.mainSub = res.fields.heading;
        }
        if (res.fields.copyBlock.content[0].content[0].value !== undefined) {
          this.unordered = res.fields.copyBlock.content[0].content[0].value;
        }
        if (res.fields.copyBlock.content[1].content[0].content[0].content[0].value !== undefined) {
          this.listed1 = res.fields.copyBlock.content[1].content[0].content[0].content[0].value;
        }
        if (res.fields.copyBlock.content[1].content[1].content[0].content[0].value !== undefined) {
          this.listed2 = res.fields.copyBlock.content[1].content[1].content[0].content[0].value;
        }
        if (res.fields.copyBlock.content[1].content[2].content[0].content[0].value !== undefined) {
          this.listed3 = res.fields.copyBlock.content[1].content[2].content[0].content[0].value;
        }
        //console.log(this.listed3)
      })
    })
  }

  public safetyInformation: any;
  getInformation() {
    let a = this.seodatares.fields.sponsoredHero.sys.id;
    this.contentfulservice.getdatapreview(a).subscribe(res => {
      let b = res.fields.isiFooterAndSticky[0].sys.id
      this.contentfulservice.getdatapreview(b).subscribe(res => {
        if (res.fields.hasOwnProperty("mainIsiHeader")) {
          this.mainIsiHeader = res.fields.mainIsiHeader;
        } else { console.log("mainIsiHeader not exists") }
        if (res.fields.hasOwnProperty("mainIsiContent")) {
          res.fields.mainIsiContent.forEach((block, index) => {

            if (index === 0) {
              console.log("mainIsiContent", res.fields.mainIsiContent);
              let mainIsiContent = res.fields.mainIsiContent[0].sys.id
              this.contentfulservice.getdatapreview(mainIsiContent).subscribe(res => {
                this.mainIsiContentheading = this._returnHtmlFromRichText(res.fields.text);
                this.mainIsiContentheadingMobile = this._returnHtmlFromRichText(res.fields.textMobile);

                this.mainIsiContenttextAlignment = res.fields.textAlignment
                document.documentElement.style.setProperty('--mainIsiContenttextAlignment', this.mainIsiContenttextAlignment);
                this.mainIsiContentfontSize = res.fields.fontSize
                document.documentElement.style.setProperty('--mainIsiContentfontSize', this.mainIsiContentfontSize);
                this.mainIsiContentlineHeight = res.fields.lineHeight
                document.documentElement.style.setProperty('--mainIsiContentlineHeight', this.mainIsiContentlineHeight);
                this.mainIsiContentfontSizeMobile = res.fields.fontSizeMobile
                document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile', this.mainIsiContentfontSizeMobile);
                this.mainIsiContentlineHeightMobile = res.fields.lineHeightMobile
                document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile', this.mainIsiContentlineHeightMobile);
                this.mainIsiContentfontColor = res.fields.fontColor.value
                document.documentElement.style.setProperty('--mainIsiContentfontColor', this.mainIsiContentfontColor);
                this.mainIsiContenttextBlockPadding = res.fields.textBlockPadding
                document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding', this.mainIsiContenttextBlockPadding);
                this.mainIsiContenttextBlockMargin = res.fields.textBlockMargin
                document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin', this.mainIsiContenttextBlockMargin);
                this.mainIsiContentwidth = res.fields.width
                document.documentElement.style.setProperty('--mainIsiContentwidth', this.mainIsiContentwidth);
                this.mainIsiContenttextAlignmentMobile = res.fields.textAlignmentMobile;
                document.documentElement.style.setProperty('--mainIsiContenttextAlignmentMobile', this.mainIsiContenttextAlignmentMobile);
                this.mainIsiContenttextBlockPaddingMobile = res.fields.textBlockPaddingMobile;
                document.documentElement.style.setProperty('--mainIsiContenttextBlockPaddingMobile', this.mainIsiContenttextBlockPaddingMobile);
                this.mainIsiContenttextBlockMarginMobile = res.fields.textBlockMarginMobile;
                document.documentElement.style.setProperty('--mainIsiContenttextBlockMarginMobile', this.mainIsiContenttextBlockMarginMobile);
              });
            } else if (index === 1) {
              let mainIsiContent1 = res.fields.mainIsiContent[1].sys.id
              this.contentfulservice.getdatapreview(mainIsiContent1).subscribe(res => {
                this.mainIsiContenttext = this._returnHtmlFromRichText(res.fields.text);

                this.mainIsiContenttextAlignment1 = res.fields.textAlignment
                document.documentElement.style.setProperty('--mainIsiContenttextAlignment1', this.mainIsiContenttextAlignment1);
                this.mainIsiContentfontSize1 = res.fields.fontSize
                document.documentElement.style.setProperty('--mainIsiContentfontSize1', this.mainIsiContentfontSize1);
                this.mainIsiContentlineHeight1 = res.fields.lineHeight
                document.documentElement.style.setProperty('--mainIsiContentlineHeight1', this.mainIsiContentlineHeight1);
                this.mainIsiContentfontSizeMobile1 = res.fields.fontSizeMobile
                document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile1', this.mainIsiContentfontSizeMobile1);
                this.mainIsiContentlineHeightMobile1 = res.fields.lineHeightMobile
                document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile1', this.mainIsiContentlineHeightMobile1);
                this.mainIsiContentfontColor1 = res.fields.fontColor.value
                document.documentElement.style.setProperty('--mainIsiContentfontColor1', this.mainIsiContentfontColor1);
                this.mainIsiContenttextBlockPadding1 = res.fields.textBlockPadding
                document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding1', this.mainIsiContenttextBlockPadding1);
                this.mainIsiContenttextBlockMargin1 = res.fields.textBlockMargin
                document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin1', this.mainIsiContenttextBlockMargin1);
                this.mainIsiContentwidth1 = res.fields.width
                document.documentElement.style.setProperty('--mainIsiContentwidth1', this.mainIsiContentwidth1);
              });
            } else if (index === 2) {
              let mainIsiContent2 = res.fields.mainIsiContent[2].sys.id
              this.contentfulservice.getdatapreview(mainIsiContent2).subscribe(res => {
                this.mainIsiContentheading1 = this._returnHtmlFromRichText(res.fields.text);

                this.mainIsiContenttextAlignment2 = res.fields.textAlignment
                document.documentElement.style.setProperty('--mainIsiContenttextAlignment2', this.mainIsiContenttextAlignment2);
                this.mainIsiContentfontSize2 = res.fields.fontSize
                document.documentElement.style.setProperty('--mainIsiContentfontSize2', this.mainIsiContentfontSize2);
                this.mainIsiContentlineHeight2 = res.fields.lineHeight
                document.documentElement.style.setProperty('--mainIsiContentlineHeight2', this.mainIsiContentlineHeight2);
                this.mainIsiContentfontSizeMobile2 = res.fields.fontSizeMobile
                document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile2', this.mainIsiContentfontSizeMobile2);
                this.mainIsiContentlineHeightMobile2 = res.fields.lineHeightMobile
                document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile2', this.mainIsiContentlineHeightMobile2);
                this.mainIsiContentfontColor2 = res.fields.fontColor.value
                document.documentElement.style.setProperty('--mainIsiContentfontColor2', this.mainIsiContentfontColor2);
                this.mainIsiContenttextBlockPadding2 = res.fields.textBlockPadding
                document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding2', this.mainIsiContenttextBlockPadding2);
                this.mainIsiContenttextBlockMargin2 = res.fields.textBlockMargin
                document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin2', this.mainIsiContenttextBlockMargin2);
                this.mainIsiContentwidth2 = res.fields.width
                document.documentElement.style.setProperty('--mainIsiContentwidth2', this.mainIsiContentwidth2);
              });
            } else if (index === 3) {
              let mainIsiContent3 = res.fields.mainIsiContent[3].sys.id
              this.contentfulservice.getdatapreview(mainIsiContent3).subscribe(res => {
                this.mainIsiContenttext1 = this._returnHtmlFromRichText(res.fields.text);

                this.mainIsiContenttextAlignment3 = res.fields.textAlignment
                document.documentElement.style.setProperty('--mainIsiContenttextAlignment3', this.mainIsiContenttextAlignment3);
                this.mainIsiContentfontSize3 = res.fields.fontSize
                document.documentElement.style.setProperty('--mainIsiContentfontSize3', this.mainIsiContentfontSize3);
                this.mainIsiContentlineHeight3 = res.fields.lineHeight
                document.documentElement.style.setProperty('--mainIsiContentlineHeight3', this.mainIsiContentlineHeight3);
                this.mainIsiContentfontSizeMobile3 = res.fields.fontSizeMobile
                document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile3', this.mainIsiContentfontSizeMobile3);
                this.mainIsiContentlineHeightMobile3 = res.fields.lineHeightMobile
                document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile3', this.mainIsiContentlineHeightMobile3);
                this.mainIsiContentfontColor3 = res.fields.fontColor.value
                document.documentElement.style.setProperty('--mainIsiContentfontColor3', this.mainIsiContentfontColor3);
                this.mainIsiContenttextBlockPadding3 = res.fields.textBlockPadding
                document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding3', this.mainIsiContenttextBlockPadding3);
                this.mainIsiContenttextBlockMargin3 = res.fields.textBlockMargin
                document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin3', this.mainIsiContenttextBlockMargin3);
                this.mainIsiContentwidth3 = res.fields.width
                document.documentElement.style.setProperty('--mainIsiContentwidth3', this.mainIsiContentwidth3);
              });
            } else if (index === 4) {
              let mainIsiContent4 = res.fields.mainIsiContent[4].sys.id
              this.contentfulservice.getdatapreview(mainIsiContent4).subscribe(res => {
                this.mainIsiContentheading2 = this._returnHtmlFromRichText(res.fields.text);

                this.mainIsiContenttextAlignment4 = res.fields.textAlignment
                document.documentElement.style.setProperty('--mainIsiContenttextAlignment4', this.mainIsiContenttextAlignment4);
                this.mainIsiContentfontSize4 = res.fields.fontSize
                document.documentElement.style.setProperty('--mainIsiContentfontSize4', this.mainIsiContentfontSize4);
                this.mainIsiContentlineHeight4 = res.fields.lineHeight
                document.documentElement.style.setProperty('--mainIsiContentlineHeight4', this.mainIsiContentlineHeight4);
                this.mainIsiContentfontSizeMobile4 = res.fields.fontSizeMobile
                document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile4', this.mainIsiContentfontSizeMobile4);
                this.mainIsiContentlineHeightMobile4 = res.fields.lineHeightMobile
                document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile4', this.mainIsiContentlineHeightMobile4);
                this.mainIsiContentfontColor4 = res.fields.fontColor.value
                document.documentElement.style.setProperty('--mainIsiContentfontColor4', this.mainIsiContentfontColor4);
                this.mainIsiContenttextBlockPadding4 = res.fields.textBlockPadding
                document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding4', this.mainIsiContenttextBlockPadding4);
                this.mainIsiContenttextBlockMargin4 = res.fields.textBlockMargin
                document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin4', this.mainIsiContenttextBlockMargin4);
                this.mainIsiContentwidth4 = res.fields.width
                document.documentElement.style.setProperty('--mainIsiContentwidth4', this.mainIsiContentwidth4);
              });
            } else if (index === 5) {
              let mainIsiContent5 = res.fields.mainIsiContent[5].sys.id
              this.contentfulservice.getdatapreview(mainIsiContent5).subscribe(res => {
                this.mainIsiContenttext2 = this._returnHtmlFromRichText(res.fields.text);

                this.mainIsiContenttextAlignment5 = res.fields.textAlignment
                document.documentElement.style.setProperty('--mainIsiContenttextAlignment5', this.mainIsiContenttextAlignment5);
                this.mainIsiContentfontSize5 = res.fields.fontSize
                document.documentElement.style.setProperty('--mainIsiContentfontSize5', this.mainIsiContentfontSize5);
                this.mainIsiContentlineHeight5 = res.fields.lineHeight
                document.documentElement.style.setProperty('--mainIsiContentlineHeight5', this.mainIsiContentlineHeight5);
                this.mainIsiContentfontSizeMobile5 = res.fields.fontSizeMobile
                document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile5', this.mainIsiContentfontSizeMobile5);
                this.mainIsiContentlineHeightMobile5 = res.fields.lineHeightMobile
                document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile5', this.mainIsiContentlineHeightMobile5);
                this.mainIsiContentfontColor5 = res.fields.fontColor.value
                document.documentElement.style.setProperty('--mainIsiContentfontColor5', this.mainIsiContentfontColor5);
                this.mainIsiContenttextBlockPadding5 = res.fields.textBlockPadding
                document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding5', this.mainIsiContenttextBlockPadding5);
                this.mainIsiContenttextBlockMargin5 = res.fields.textBlockMargin
                document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin5', this.mainIsiContenttextBlockMargin5);
                this.mainIsiContentwidth5 = res.fields.width
                document.documentElement.style.setProperty('--mainIsiContentwidth5', this.mainIsiContentwidth5);
              });
            }
          });
        } else { console.log("shortStickyIsiImportantSafetyInformationText not exists") }


        this.mainIsiMargin = res.fields.mainIsiMargin
        document.documentElement.style.setProperty('--mainIsiMargin', this.mainIsiMargin);
        this.mainIsiPadding = res.fields.mainIsiPadding
        document.documentElement.style.setProperty('--mainIsiPadding', this.mainIsiPadding);
        this.mainIsiHeaderFontSize = res.fields.mainIsiHeaderFontSize
        document.documentElement.style.setProperty('--mainIsiHeaderFontSize', this.mainIsiHeaderFontSize);
        this.mainIsiHeaderFontSizeMobile = res.fields.mainIsiHeaderFontSizeMobile
        document.documentElement.style.setProperty('--mainIsiHeaderFontSizeMobile', this.mainIsiHeaderFontSizeMobile);
        this.mainIsiHeaderFontWeight = res.fields.mainIsiHeaderFontWeight
        document.documentElement.style.setProperty('--mainIsiHeaderFontWeight', this.mainIsiHeaderFontWeight);
        this.mainIsiHeaderFontColor = res.fields.mainIsiHeaderFontColor.value
        document.documentElement.style.setProperty('--mainIsiHeaderFontColor', this.mainIsiHeaderFontColor);
      });
    });
  }

  public options: any = {
    renderNode: {
      [INLINES.HYPERLINK]: (node, next) => `<a href="${node.data.uri}" target="_blank" rel="noopener noreferrer">${next(node.content)}</a>`,
      [BLOCKS.PARAGRAPH]: (node, next) => `<p>${next(node.content).replace(/\n/g, '<br/>')}</p>`,
    }
  }

  _returnHtmlFromRichText(richText) {
    if (richText === undefined || richText === null || richText.nodeType !== 'document') {
      return '<p></p>';
    }
    return documentToHtmlString(richText, this.options);
  }

  //getCslimage() {
  //    this.contentfulservice.getAssetspreview('/2aKt26etDN7EJwb2kqn1MI/').subscribe(res => {
  //this.landFootImg = res.fields.file.url
  //    });
  //}



  getSampleimage() {

    if (this.sponserdherores.fields.hasOwnProperty("ogeqsaColumnLeft")) {
      this.content_id3 = this.sponserdherores.fields.ogeqsaColumnLeft.sys.id;
    } else { console.log("ogeqsaColumnLeft not exists") }

    // alert(this.content_id3)
    this.contentfulservice.getdatapreview(this.content_id3).subscribe(res => {
      let imgwrap_id = res.fields.imageWrapper.sys.id;
      this.contentfulservice.getdatapreview(imgwrap_id).subscribe(res => {
        let imgid = res.fields.image.sys.id;
        //alert(imgid)
        this.contentfulservice.getAssetspreview('/' + imgid + '/').subscribe(imgData =>
          this.sampleImg = imgData.fields.file.url
        );
      })
    })
  }

  getCopyright() {
    console.log("copyRights_id", this.copyRights_id)
    this.contentfulservice.getdatapreview(this.copyRights_id).subscribe(res => {
      this.copyRights = res.fields.copyright
      if (res.fields.hasOwnProperty("logoimage")) {
        let logoimage = res.fields.logoimage[0].sys.id
        this.contentfulservice.getdatapreview(logoimage).subscribe(res => {
          let imageWrapper = res.fields.imageWrapper.sys.id
          this.contentfulservice.getdatapreview(imageWrapper).subscribe(res => {
            let logoimageurl = res.fields.image.sys.id
            this.contentfulservice.getassets(logoimageurl).subscribe(res => {
              this.logoimage = res.fields.file.url;
            });
          });
        });
      } else { console.log("logoimage not exists") }
    })
  }

  getPrescribe() {
    this.presid = this.resourcedatares.fields.isi.sys.id;
    this.contentfulservice.getdatapreview(this.presid).subscribe(res => {
      if (res.fields.hasOwnProperty("safetyInformation")) {
        this.prescribe = res.fields.safetyInformation;
        //console.log(this.prescribe);
      } else { console.log("safetyInformation not exists") }
      if (res.fields.hasOwnProperty("shortStickyIsi")) {
        this.shortstickyisi = res.fields.shortStickyIsi
      } else { console.log("shortStickyIsi not exists") }
    })
  }

  getDynamicmsg() {
    if (this.sponserdherores.fields.hasOwnProperty("ogeqsaColumnRight")) {
      this.content_id2 = this.sponserdherores.fields.ogeqsaColumnRight.sys.id;
    } else {
      // console.log("ogeqsaColumnRight not exists") 
    }
    //alert(this.content_id2)
    this.contentfulservice.getdatapreview(this.content_id2).subscribe(res => {
      let resourceform_id = res.fields.resourceForm.sys.id;
      this.contentfulservice.getdatapreview(resourceform_id).subscribe(res => {
        this.dynamicMsg = res.fields.heading
        //console.log(this.dynamicMsg)
      })
    })
  }

  getreferences() {

    if (this.sponserdherores.fields.hasOwnProperty("mainTextBlock")) {
      let a = this.sponserdherores.fields.mainTextBlock.sys.id;
      this.references = this.sponserdherores.fields.references;
      this.contentfulservice.getdatapreview(a).subscribe(res => {
        this.copyBlockFootnotes = res.fields.copyBlockFootnotes;
      })
      //console.log(this.references)
    } else { console.log("mainTextBlock not exists") }

  }

  getdisclaimer() {
    if (this.sponserdherores.fields.hasOwnProperty("disclaimer")) {
      this.disclaimer = this.sponserdherores.fields.disclaimer;
      //console.log(this.disclaimer)
    } else {
      console.log("disclaimer not exists")
    }
  }

  getColor() {

    //console.log("works!")
    this.contentfulservice.getdatapreview(this.landingpageid).subscribe(res => {
      let brandresources = res.fields.sponsoredHero.sys.id;
      this.contentfulservice.getdatapreview(brandresources).subscribe(res => {
        if (res.fields.hasOwnProperty("branding")) {
          let branding = res.fields.branding.sys.id;
          this.contentfulservice.getdatapreview(branding).subscribe(res => {
            if (res.fields.hasOwnProperty("primaryColor")) { this.resourceprimarycolor = res.fields.primaryColor.value; } else { console.log("resourceprimarycolor not exists") }
            if (res.fields.hasOwnProperty("secondaryColor")) { this.resourcesecondarycolor = res.fields.secondaryColor.value; } else { console.log("resourcesecondarycolor not exists") }
            if (res.fields.hasOwnProperty("brandHeaderBackgroundGradientColor1")) { this.gradient1 = res.fields.brandHeaderBackgroundGradientColor1.value } else { console.log("brandHeaderBackgroundGradientColor1 not exists") }
            if (res.fields.hasOwnProperty("brandHeaderBackgroundGradientColor2")) { this.gradient2 = res.fields.brandHeaderBackgroundGradientColor2.value } else { console.log("brandHeaderBackgroundGradientColor2 not exists") }
            if (res.fields.hasOwnProperty("brandHeaderBackgroundGradientColor3")) { this.gradient3 = res.fields.brandHeaderBackgroundGradientColor3.value } else { console.log("brandHeaderBackgroundGradientColor3 not exists") }
            if (res.fields.hasOwnProperty("brandFooterBackgroundGradientColor1")) { this.footgradient1 = res.fields.brandFooterBackgroundGradientColor1.value } else { console.log("brandFooterBackgroundGradientColor1 not exists") }
            if (res.fields.hasOwnProperty("brandFooterBackgroundGradientColor2")) { this.footgradient2 = res.fields.brandFooterBackgroundGradientColor2.value } else { console.log("brandFooterBackgroundGradientColor2 not exists") }

            if (res.fields.hasOwnProperty("buttonCornerRadius")) { this.buttonCornerRadius = res.fields.buttonCornerRadius } else { console.log("buttonCornerRadius not exists") }
            if (res.fields.hasOwnProperty("fontLinkColor")) { this.resourcefontLinkColor = res.fields.fontLinkColor.value } else { console.log("fontLinkColor not exists") }
            if (res.fields.hasOwnProperty("fontLinkRolloverColor")) { this.resourcefontLinkRolloverColor = res.fields.fontLinkRolloverColor.value } else { console.log("fontLinkRolloverColor not exists") }
            if (res.fields.hasOwnProperty("h1Color")) { this.resourceh1color = res.fields.h1Color.value } else { console.log("h1Color not exists") }
            if (res.fields.hasOwnProperty("horizontalRule")) { this.resourcehorizontalRule = res.fields.horizontalRule.value } else { console.log("horizontalRule not exists") }
            if (res.fields.hasOwnProperty("buttonBackgroundRollOverColor")) { this.resourcebuttonBackgroundRollOverColor = res.fields.buttonBackgroundRollOverColor.value } else { console.log("buttonBackgroundRollOverColor not exists") }
            if (res.fields.hasOwnProperty("buttonBackgroundColor")) { this.resourcebuttonBackgroundColor = res.fields.buttonBackgroundColor.value } else { console.log("buttonBackgroundColor not exists") }
            if (res.fields.hasOwnProperty("buttonFontColor")) { this.resourcebuttonFontColor = res.fields.buttonFontColor.value } else { console.log("buttonFontColor not exists") }
            if (res.fields.hasOwnProperty("buttonBackgroundRollOverColor")) { this.resourcebuttonBackgroundRollOverColor = res.fields.buttonBackgroundRollOverColor.value } else { console.log("buttonBackgroundRollOverColor not exists") }
            if (res.fields.hasOwnProperty("buttonRolloverFontColor")) { this.resourcebuttonRolloverFontColor = res.fields.buttonRolloverFontColor.value } else { console.log("buttonRolloverFontColor not exists") }
            if (res.fields.hasOwnProperty("brandFooterBackgroundColor")) { this.resourcebrandFooterBackgroundColor = res.fields.brandFooterBackgroundColor.value } else { console.log("brandFooterBackgroundColor not exists") }


            if (res.fields.hasOwnProperty("isiHeadersColors")) { this.isiHeadersColors = res.fields.isiHeadersColors.value } else { console.log("isiHeadersColors not exists") }


            //Fonts Size
            if (res.fields.hasOwnProperty("headerIndicationHeaderFontSize")) { this.headerIndicationHeaderFontSize = res.fields.headerIndicationHeaderFontSize } else { console.log("headerIndicationHeaderFontSize not exists") }
            if (res.fields.hasOwnProperty("headerIndicationCopyFontSize")) { this.headerIndicationCopyFontSize = res.fields.headerIndicationCopyFontSize } else { console.log("headerIndicationCopyFontSize not exists") }
            if (res.fields.hasOwnProperty("isiHeadersFontSize")) { this.isiHeadersFontSize = res.fields.isiHeadersFontSize } else { console.log("isiHeadersFontSize not exists") }
            if (res.fields.hasOwnProperty("isiTextFontSize")) {
              this.isiTextFontSize = res.fields.isiTextFontSize;
              //alert("isifontsize executed")
            } else { console.log("isiTextFontSize not exists") }
            if (res.fields.hasOwnProperty("bodyTextFontSize")) {
              this.bodyTextFontSize = res.fields.bodyTextFontSize;

            } else { console.log("bodyTextFontSize not exists") }

            //Font Colors
            if (res.fields.hasOwnProperty("fontColor")) { this.resourcefontColor = res.fields.fontColor.value } else { console.log("resourcefontColor not exists") }
            if (res.fields.hasOwnProperty("headerIndicationFontColor")) { this.headerIndicationFontColor = res.fields.headerIndicationFontColor.value } else { console.log("headerIndicationFontColor not exists") }

            //Font Weigth
            if (res.fields.hasOwnProperty("bodyTextFontWeight")) {
              this.bodyTextFontWeight = res.fields.bodyTextFontWeight;

            } else { console.log("bodyTextFontWeight not exists") }
            if (res.fields.hasOwnProperty("isiTextFontWeight")) {
              this.isiTextFontWeight = res.fields.isiTextFontWeight;

            } else { console.log("isiTextFontWeight not exists") }

            //Line Height
            if (res.fields.hasOwnProperty("bodyTextLineHeight")) {
              this.bodyTextLineHeight = res.fields.bodyTextLineHeight;

            } else { console.log("bodyTextLineHeight not exists") }
            if (res.fields.hasOwnProperty("isiTextLineHeight")) {
              this.isiTextLineHeight = res.fields.isiTextLineHeight;
            } else { console.log("isiTextLineHeight not exists") }

            /*this.resourceh2color = res.fields.h2Color.value
            this.resourceh3color = res.fields.h3Color.value
            this.resourcebuttonBackgroundActiveColor = res.fields.buttonBackgroundActiveColor.value
            this.resourcebuttonActiveFontColor = res.fields.buttonActiveFontColor.value
            this.resourcefontLink = res.fields.fontLink.value
            this.resourcefontFamily = res.fields.fontFamily*/
            document.documentElement.style.setProperty('--resourceprimarycolor', this.resourceprimarycolor ? this.resourceprimarycolor : "#3254a2");
            document.documentElement.style.setProperty('--resourcesecondarycolor', this.resourcesecondarycolor ? this.resourcesecondarycolor : "#691c32");
            document.documentElement.style.setProperty('--gradient1', this.gradient1);
            document.documentElement.style.setProperty('--gradient2', this.gradient2);
            document.documentElement.style.setProperty('--gradient3', this.gradient3);
            document.documentElement.style.setProperty('--footgradient1', this.footgradient1);
            document.documentElement.style.setProperty('--footgradient2', this.footgradient2);

            document.documentElement.style.setProperty('--buttonCornerRadius', this.buttonCornerRadius ? this.buttonCornerRadius : "15px");
            document.documentElement.style.setProperty('--resourceLinkColor', this.resourcefontLinkColor ? this.resourcefontLinkColor : "#3254a2");
            document.documentElement.style.setProperty('--resourceLinkRolloverColor', this.resourcefontLinkRolloverColor ? this.resourcefontLinkRolloverColor : "#691c32");
            document.documentElement.style.setProperty('--resourceh1Color', this.resourceh1color ? this.resourceh1color : "#3254a2");
            document.documentElement.style.setProperty('--resourcehorizontalRuleColor', this.resourcehorizontalRule ? this.resourcehorizontalRule : "#3254a2");
            document.documentElement.style.setProperty('--resourcebuttonBackgroundRollOverColor', this.resourcebuttonBackgroundRollOverColor ? this.resourcebuttonBackgroundRollOverColor : "#691c32");
            document.documentElement.style.setProperty('--resourcebuttonfontcolor', this.resourcebuttonFontColor ? this.resourcebuttonFontColor : "#ffffff");
            document.documentElement.style.setProperty('--resourcebuttonhoverfontcolor', this.resourcebuttonRolloverFontColor ? this.resourcebuttonRolloverFontColor : "#ffffff");
            document.documentElement.style.setProperty('--footergradientColor', this.resourcebrandFooterBackgroundColor ? this.resourcebrandFooterBackgroundColor : "#3254a2");



            //Font Size
            document.documentElement.style.setProperty('--headerIndicationHeaderFontSize', this.headerIndicationHeaderFontSize ? this.headerIndicationHeaderFontSize : "19px");
            document.documentElement.style.setProperty('--headerIndicationCopyFontSize', this.headerIndicationCopyFontSize ? this.headerIndicationCopyFontSize : "18px");
            document.documentElement.style.setProperty('--isiHeadersFontSize', this.isiHeadersFontSize ? this.isiHeadersFontSize : "1 REM");
            document.documentElement.style.setProperty('--isiTextFontSize', this.isiTextFontSize ? this.isiTextFontSize : "1 REM");
            document.documentElement.style.setProperty('--bodyTextFontSize', this.bodyTextFontSize ? this.bodyTextFontSize : "1 REM");

            //Font Colors
            document.documentElement.style.setProperty('--headerIndicationFontColor', this.headerIndicationFontColor ? this.headerIndicationFontColor : "#ffffff");
            document.documentElement.style.setProperty('--isiHeadersColors', this.isiHeadersColors ? this.isiHeadersColors : "#0072ce");

            //Font Weight
            document.documentElement.style.setProperty('--bodyTextFontWeight', this.bodyTextFontWeight ? this.bodyTextFontWeight : "400");
            document.documentElement.style.setProperty('--isiTextFontWeight', this.isiTextFontWeight ? this.isiTextFontWeight : "400");

            //Font Height
            document.documentElement.style.setProperty('--bodyTextLineHeight', this.bodyTextLineHeight ? this.bodyTextLineHeight : "1.25 REM");
            document.documentElement.style.setProperty('--isiTextLineHeight', this.isiTextLineHeight ? this.isiTextLineHeight : "1.25 REM");

            const gradientColors = [this.gradient1, this.gradient2, this.gradient3];
            const gradientString = `linear-gradient(to right, ${gradientColors.join(", ")})`;
            //document.documentElement.style.setProperty('--headergradientColor', "#b5b3b4");

            document.getElementById("headgradient").style.background = gradientString;
            // const footgradientColors = [this.footgradient1, this.footgradient2];
            // const footgradientString = `linear-gradient(to right, ${footgradientColors.join(", ")})`;
            // document.getElementById("footgradient").style.background = footgradientString;
            // document.documentElement.style.setProperty('--headergradientColor', "#4b71c9");
            document.getElementById("footgradient").style.background = this.resourcebrandFooterBackgroundColor;

            // console.log("isiTextFontSize", this.isiTextFontSize)
            // console.log("isiTextFontWeight", this.isiTextFontWeight)
            // console.log("isiTextLineHeight", this.isiTextLineHeight)
            // console.log("bodyTextFontSize", this.bodyTextFontSize)
            // console.log("bodyTextFontWeight", this.bodyTextFontWeight)
            // console.log("bodyTextLineHeight", this.bodyTextLineHeight)
          })
        } else { console.log("branding not exists") }
      })
    })

  }

  getprescribingInformation() {
    let a = this.sponserdherores.fields.branddiseaseHeader.sys.id;
    this.contentfulservice.getdatapreview(a).subscribe(res => {
      console.log("p_file", res)
      if (res.fields.hasOwnProperty('headerIsi')) {
        let b = res.fields.headerIsi.sys.id;
        this.contentfulservice.getdatapreview(b).subscribe(res => {
          console.log("p_filelink", res);
          const hardCodedText = {
            "data": {},
            "content": [
              {
                "data": {},
                "content": [
                  {
                    "data": {},
                    "marks": [],
                    "value": "",
                    "nodeType": "text"
                  },
                  {
                    "data": {
                      "uri": "https://assets.ctfassets.net/h2axairjfqha/5qvSMsdodC6Eh6Dz4TqaDI/c06c9169044869f8b26e0e67513e51ac/GENERIC_DRUG_PRESCRIBING_INFORMATION.pdf"
                    },
                    "content": [
                      {
                        "data": {},
                        "marks": [],
                        "value": "Prescribing Information",
                        "nodeType": "text"
                      }
                    ],
                    "nodeType": "hyperlink"
                  },
                  {
                    "data": {},
                    "marks": [],
                    "value": " | ",
                    "nodeType": "text"
                  },
                  {
                    "data": {
                      "scrollToElementById": "contentbox"
                    },
                    "content": [
                      {
                        "data": {},
                        "marks": [],
                        "value": "Important Safety Information",
                        "nodeType": "text"
                      }
                    ],
                    "nodeType": "hyperlink"
                  },
                  {
                    "data": {},
                    "marks": [],
                    "value": "",
                    "nodeType": "text"
                  }
                ],
                "nodeType": "paragraph"
              }
            ],
            "nodeType": "document"
          };
          // this.p_file = this._returnHtmlFromRichText(hardCodedText);
          this.p_file = this._returnHtmlFromRichText(res.fields.text);
          this.p_fileMobile = this._returnHtmlFromRichText(res.fields.textMobile);
          this.indicationtextContent = this.extractPrescribingInformation(res.fields.text);
          this.indicationtextContentMobile = this.extractPrescribingInformation(res.fields.textMobile);
          this.indicationtextContenttext = this.extractPrescribingInformationtext(res.fields.text);
          this.indicationtextContenttext1 = this.extractPrescribingInformationtext1(res.fields.text);
          this.indicationtextContenttexttextMobile = this.extractPrescribingInformationtext(res.fields.textMobile);
          this.indicationtextContenttext1textMobile = this.extractPrescribingInformationtext1(res.fields.textMobile);
          this.p_filebg = res.fields.backgroundColor.value;
          document.documentElement.style.setProperty('--p_filebg', this.p_filebg);
          this.p_filewidth = res.fields.width;
          document.documentElement.style.setProperty('--p_filewidth', this.p_filewidth);
          this.p_filetextAlignment = res.fields.textAlignment;
          document.documentElement.style.setProperty('--p_filetextAlignment', this.p_filetextAlignment);
          this.p_filetextAlignmentMobile = res.fields.textAlignmentMobile;
          document.documentElement.style.setProperty('--p_filetextAlignmentMobile', this.p_filetextAlignmentMobile);
          this.p_filelineHeight = res.fields.lineHeight;
          document.documentElement.style.setProperty('--p_filelineHeight', this.p_filelineHeight);
          this.p_filelineHeightMobile = res.fields.lineHeightMobile;
          document.documentElement.style.setProperty('--p_filelineHeightMobile', this.p_filelineHeightMobile);
          this.p_filetextBlockPadding = res.fields.textBlockPadding;
          document.documentElement.style.setProperty('--p_filetextBlockPadding', this.p_filetextBlockPadding);
          this.p_filetextBlockPaddingMobile = res.fields.textBlockPaddingMobile;
          document.documentElement.style.setProperty('--p_filetextBlockPaddingMobile', this.p_filetextBlockPaddingMobile);
          this.p_filefontSize = res.fields.fontSize;
          document.documentElement.style.setProperty('--p_filefontSize', this.p_filefontSize);
          this.p_filefontSizeMobile = res.fields.fontSizeMobile;
          document.documentElement.style.setProperty('--p_filefontSizeMobile', this.p_filefontSizeMobile);
          this.p_filefontColor = res.fields.fontColor.value;
          document.documentElement.style.setProperty('--p_filefontColor', this.p_filefontColor);
          this.p_filetextBlockPadding = res.fields.textBlockPadding;
          document.documentElement.style.setProperty('--p_filetextBlockPadding', this.p_filetextBlockPadding);
          this.p_filetextBlockPaddingMobile = res.fields.textBlockPaddingMobile;
          document.documentElement.style.setProperty('--p_filetextBlockPaddingMobile', this.p_filetextBlockPaddingMobile);
          this.p_filetextBlockMargin = res.fields.textBlockMargin;
          document.documentElement.style.setProperty('--p_filetextBlockMargin', this.p_filetextBlockMargin);
          this.p_filetextBlockMarginMobile = res.fields.textBlockMarginMobile;
          document.documentElement.style.setProperty('--p_filetextBlockMarginMobile', this.p_filetextBlockMarginMobile);
        });
      } else {
        console.log("prescribingInformationPdf not exists")
      }
    });
  }
  gettextBoxBelowHeaderIndication() {
    let a = this.sponserdherores.fields.branddiseaseHeader.sys.id;
    this.contentfulservice.getdatapreview(a).subscribe(res => {
      if (res.fields.hasOwnProperty('textBoxBelowHeaderIndication')) {
        let b = res.fields.textBoxBelowHeaderIndication[0].sys.id;
        this.contentfulservice.getdatapreview(b).subscribe(res => {
          console.log("textBoxBelowHeaderIndication", res)
          this.textBoxIndication = this._returnHtmlFromRichText(res.fields.text);
          this.textBoxIndicationMobile = this._returnHtmlFromRichText(res.fields.textMobile);

          this.textBoxIndicationfontSizeMobile = res.fields.fontSizeMobile;
          document.documentElement.style.setProperty('--textBoxIndicationfontSizeMobile', this.textBoxIndicationfontSizeMobile);
          this.textBoxIndicationlineHeightMobile = res.fields.lineHeightMobile;
          document.documentElement.style.setProperty('--textBoxIndicationlineHeightMobile', this.textBoxIndicationlineHeightMobile);
          this.textBoxIndicationtextAlignment = res.fields.textAlignment;
          document.documentElement.style.setProperty('--textBoxIndicationtextAlignment', this.textBoxIndicationtextAlignment);
          this.textBoxIndicationtextAlignmentMobile = res.fields.textAlignmentMobile;
          document.documentElement.style.setProperty('--textBoxIndicationtextAlignmentMobile', this.textBoxIndicationtextAlignmentMobile);
          this.textBoxIndicationfontSize = res.fields.fontSize;
          document.documentElement.style.setProperty('--textBoxIndicationfontSize', this.textBoxIndicationfontSize);
          this.textBoxIndicationlineHeight = res.fields.lineHeight;
          document.documentElement.style.setProperty('--textBoxIndicationlineHeight', this.textBoxIndicationlineHeight);
          this.textBoxIndicationfontColor = res.fields.fontColor.value;
          document.documentElement.style.setProperty('--textBoxIndicationfontColor', this.textBoxIndicationfontColor);

          this.textBlockMargin = res.fields.textBlockMargin;
          document.documentElement.style.setProperty('--textBlockMargin', this.textBlockMargin);
          this.textBoxIndicationtextBlockPadding = res.fields.textBlockPadding;
          document.documentElement.style.setProperty('--textBoxIndicationtextBlockPadding', this.textBoxIndicationtextBlockPadding);
          this.textBoxIndicationtextBlockMargin = res.fields.textBlockMargin;
          document.documentElement.style.setProperty('--textBoxIndicationtextBlockMargin', this.textBoxIndicationtextBlockMargin);
          this.textBoxIndicationtextBlockPaddingMobile = res.fields.textBlockPaddingMobile;
          document.documentElement.style.setProperty('--textBoxIndicationtextBlockPaddingMobile', this.textBoxIndicationtextBlockPaddingMobile);
          this.textBoxIndicationtextBlockMarginMobile = res.fields.textBlockMarginMobile;
          document.documentElement.style.setProperty('--textBoxIndicationtextBlockMarginMobile', this.textBoxIndicationtextBlockMarginMobile);
        });

        let c = res.fields.textBoxBelowHeaderIndication[1].sys.id;
        this.contentfulservice.getdatapreview(c).subscribe(res => {
          this.textBoxIndicationactionbuttonText = res.fields.actionbuttonText;
          this.buttonLink = res.fields.buttonLink;

          this.customButtonBackgroundColor = res.fields.customButtonBackgroundColor.value;
          document.documentElement.style.setProperty('--customButtonBackgroundColor', this.customButtonBackgroundColor);
          this.customButtonTextColor = res.fields.customButtonTextColor.value;
          document.documentElement.style.setProperty('--customButtonTextColor', this.customButtonTextColor);
          this.buttonBorderRadius = res.fields.buttonBorderRadius;
          document.documentElement.style.setProperty('--buttonBorderRadius', this.buttonBorderRadius);
          this.buttonPadding = res.fields.buttonPadding;
          document.documentElement.style.setProperty('--buttonPadding', this.buttonPadding);
          this.buttonWidth = res.fields.buttonWidth;
          document.documentElement.style.setProperty('--buttonWidth', this.buttonWidth);
          this.buttonHeight = res.fields.buttonHeight
          document.documentElement.style.setProperty('--buttonHeight', this.buttonHeight);

          this.customButtonBackgroundRolloverColor = res.fields.customButtonBackgroundRolloverColor.value;
          document.documentElement.style.setProperty('--customButtonBackgroundRolloverColor', this.customButtonBackgroundRolloverColor);
          this.customButtonTextRolloverColor = res.fields.customButtonTextRolloverColor.value;
          document.documentElement.style.setProperty('--customButtonTextRolloverColor', this.customButtonTextRolloverColor);

        });
        let d = res.fields.textBoxBelowHeaderIndication[2].sys.id;
        this.contentfulservice.getdatapreview(d).subscribe(res => {
          console.log("textBoxBelowHeaderIndication", res)
          this.textBoxIndicationtext = this._returnHtmlFromRichText(res.fields.text);
          this.textBoxIndicationtextMobile = this._returnHtmlFromRichText(res.fields.textMobile);

          this.IndicationtextfontColor = res.fields.fontColor.value;
          document.documentElement.style.setProperty('--IndicationtextfontColor', this.IndicationtextfontColor);
          this.IndicationtextlineHeight = res.fields.lineHeight;
          document.documentElement.style.setProperty('--IndicationtextlineHeight', this.IndicationtextlineHeight);
          this.IndicationtexttextAlignment = res.fields.textAlignment;
          document.documentElement.style.setProperty('--IndicationtexttextAlignment', this.IndicationtexttextAlignment);
          this.IndicationtexttextAlignmentMobile = res.fields.textAlignmentMobile;
          document.documentElement.style.setProperty('--IndicationtexttextAlignmentMobile', this.IndicationtexttextAlignmentMobile);
          this.IndicationtextfontSize = res.fields.fontSize;
          document.documentElement.style.setProperty('--IndicationtextfontSize', this.IndicationtextfontSize);

          this.IndicationtextfontSizeMobile = res.fields.fontSizeMobile;
          document.documentElement.style.setProperty('--IndicationtextfontSizeMobile', this.IndicationtextfontSizeMobile);
          this.IndicationlineHeightMobile = res.fields.lineHeightMobile;
          document.documentElement.style.setProperty('--IndicationlineHeightMobile', this.IndicationlineHeightMobile);

          this.IndicationtexttextBlockMargin = res.fields.textBlockMargin;
          document.documentElement.style.setProperty('--IndicationtexttextBlockMargin', this.IndicationtexttextBlockMargin); 
          this.IndicationtexttextBlockPadding = res.fields.textBlockPadding;
          document.documentElement.style.setProperty('--IndicationtexttextBlockPadding', this.IndicationtexttextBlockPadding); 
          this.IndicationtexttextBlockPaddingMobile = res.fields.textBlockPaddingMobile;
          document.documentElement.style.setProperty('--IndicationtexttextBlockPaddingMobile', this.IndicationtexttextBlockPaddingMobile);
          this.IndicationtexttextBlockMarginMobile = res.fields.textBlockMarginMobile;
          document.documentElement.style.setProperty('--IndicationtexttextBlockMarginMobile', this.IndicationtexttextBlockMarginMobile);

        });
      } else {
        console.log("textBoxIndication not exists")
      }
    });
  }
  getcolumnBlock() {

    let a = this.sponserdherores.fields.columnBlock.sys.id;
    console.log("getcolumnBlock", a);
    this.contentfulservice.getdatapreview(a).subscribe(res => {
      if (res.fields.hasOwnProperty('columns')) {
        if (res.fields.columns[0]) {
          let b = res.fields.columns[0].sys.id;
          this.contentfulservice.getdatapreview(b).subscribe(res => {
            let c = res.fields.columnContent[0].sys.id;
            this.contentfulservice.getdatapreview(c).subscribe(res => {
              let imgid = res.fields.image.sys.id;
              //alert(imgid)
              this.contentfulservice.getassets(imgid).subscribe(res => {
                this.sampleImg1 = res.fields.file.url;
              });

              this.sampleImg1Width = res.fields.imageWidth;
              document.documentElement.style.setProperty('--sampleImg1Width', this.sampleImg1Width);
              this.sampleImg1Height = res.fields.imageHeight;
              document.documentElement.style.setProperty('--sampleImg1Height', this.sampleImg1Height);
              this.sampleImg1WidthMobile = res.fields.imageWidthMobile;
              document.documentElement.style.setProperty('--sampleImg1WidthMobile', this.sampleImg1WidthMobile);
              this.sampleImg1imageAlignment = res.fields.imageAlignment;
              document.documentElement.style.setProperty('--sampleImg1imageAlignment', this.sampleImg1imageAlignment);
              this.sampleImg1HeightMobile = res.fields.imageHeightMobile;
              document.documentElement.style.setProperty('--sampleImg1HeightMobile', this.sampleImg1HeightMobile);

            });

            let d = res.fields.columnContent[1].sys.id;
            this.contentfulservice.getdatapreview(d).subscribe(res => {
              this.columnContenttext = this._returnHtmlFromRichText(res.fields.text);
              this.columnContenttextMobile = this._returnHtmlFromRichText(res.fields.textMobile);

              this.columnContentlineHeight = res.fields.lineHeight;
              document.documentElement.style.setProperty('--columnContentlineHeight', this.columnContentlineHeight); 
              this.columnContentlineHeightMobile = res.fields.lineHeightMobile;
              document.documentElement.style.setProperty('--columnContentlineHeightMobile', this.columnContentlineHeightMobile);
              this.columnContenttextcopyFontSize = res.fields.fontSize;
              document.documentElement.style.setProperty('--columnContenttextcopyFontSize', this.columnContenttextcopyFontSize); 
              this.columnContenttextcopyFontSizeMobile = res.fields.fontSizeMobile;
              document.documentElement.style.setProperty('--columnContenttextcopyFontSizeMobile', this.columnContenttextcopyFontSizeMobile);
              this.columnContenttextcopyFontColor = res.fields.fontColor.value;
              document.documentElement.style.setProperty('--columnContenttextcopyFontColor', this.columnContenttextcopyFontColor);
              this.columnContenttextcopyTextAlign = res.fields.textAlignment;
              document.documentElement.style.setProperty('--columnContenttextcopyTextAlign', this.columnContenttextcopyTextAlign);
              this.columnContenttextcopyTextAlignMobile = res.fields.textAlignmentMobile;
              document.documentElement.style.setProperty('--columnContenttextcopyTextAlignMobile', this.columnContenttextcopyTextAlignMobile);
              this.columnContenttextBlockMargin = res.fields.textBlockMargin;
              document.documentElement.style.setProperty('--columnContenttextBlockMargin', this.columnContenttextBlockMargin);
              this.columnContenttextBlockMarginMobile = res.fields.textBlockMarginMobile;
              document.documentElement.style.setProperty('--columnContenttextBlockMarginMobile', this.columnContenttextBlockMarginMobile);
              this.columnContenttexttextBlockPadding = res.fields.textBlockPadding;
              document.documentElement.style.setProperty('--columnContenttexttextBlockPadding', this.columnContenttexttextBlockPadding);
              this.columnContenttexttextBlockPaddingMobile = res.fields.textBlockPaddingMobile;
              document.documentElement.style.setProperty('--columnContenttexttextBlockPaddingMobile', this.columnContenttexttextBlockPaddingMobile);

            });

            this.columnVerticalAlignment = res.fields.columnVerticalAlignment;
            console.log("columnVerticalAlignment", res.fields);
            let flexAlignment: string;
            if (this.columnVerticalAlignment === 'middle') {
              flexAlignment = 'center';
            } else if (this.columnVerticalAlignment === 'top') {
              flexAlignment = 'flex-start';
            } else {
              flexAlignment = 'flex-end';
            }
            document.documentElement.style.setProperty('--columnVerticalAlignment', flexAlignment);
          });
        } else {
          console.log("columns[0] not exists");
        }

        if (res.fields.columns[1]) {
          let b = res.fields.columns[1].sys.id;
          this.contentfulservice.getdatapreview(b).subscribe(res => {

            if (res.fields.columnContent[0]) {
              let c = res.fields.columnContent[0].sys.id;
              this.contentfulservice.getdatapreview(c).subscribe(res => {
                let f = res.fields.containerContent[0].sys.id;
                this.contentfulservice.getdatapreview(f).subscribe(res => {
                  this.containerContenttext = this._returnHtmlFromRichText(res.fields.copy);
                  this.containerContenttextcopyFont = res.fields.copyFont;
                  document.documentElement.style.setProperty('--containerContenttextcopyFont', this.containerContenttextcopyFont);
                  this.containerContenttextcopyFontSize = res.fields.copyFontSize;
                  document.documentElement.style.setProperty('--containerContenttextcopyFontSize', this.containerContenttextcopyFontSize);
                  this.containerContenttextcopyFontColor = res.fields.copyFontColor.value;
                  document.documentElement.style.setProperty('--containerContenttextcopyFontColor', this.containerContenttextcopyFontColor);
                  this.containerContenttextcopyTextAlign = res.fields.copyTextAlign;
                  document.documentElement.style.setProperty('--containerContenttextcopyTextAlign', this.containerContenttextcopyTextAlign);
                  this.copyLineHeight = res.fields.copyLineHeight;
                  document.documentElement.style.setProperty('--copyLineHeight', this.copyLineHeight);
                  this.copyPadding = res.fields.copyPadding;
                  document.documentElement.style.setProperty('--copyPadding', this.copyPadding);
                  this.copyMargin = res.fields.copyMargin;
                  document.documentElement.style.setProperty('--copyMargin', this.copyMargin);

                  this.containerContenttextcopyFontSizeMobile = res.fields.copyFontSizeMobile;
                  document.documentElement.style.setProperty('--containerContenttextcopyFontSizeMobile', this.containerContenttextcopyFontSizeMobile);
                  this.containerContenttextcopyTextAlignMobile = res.fields.copyTextAlignMobile;
                  document.documentElement.style.setProperty('--containerContenttextcopyTextAlignMobile', this.containerContenttextcopyTextAlignMobile);
                  this.copyLineHeightMobile = res.fields.copyLineHeightMobile;
                  document.documentElement.style.setProperty('--copyLineHeightMobile', this.copyLineHeightMobile);
                  this.copyPaddingMobile = res.fields.copyPaddingMobile;
                  document.documentElement.style.setProperty('--copyPaddingMobile', this.copyPaddingMobile);
                  this.copyMarginMobile = res.fields.copyMarginMobile;
                  document.documentElement.style.setProperty('--copyMarginMobile', this.copyMarginMobile);
                });
                let g = res.fields.containerContent[1].sys.id;
                this.contentfulservice.getdatapreview(g).subscribe(res => {
                  this.containerContenttext1 = this._returnHtmlFromRichText(res.fields.copy);
                  this.containerContenttextcopyFont1 = res.fields.copyFont;
                  document.documentElement.style.setProperty('--containerContenttextcopyFont1', this.containerContenttextcopyFont1);
                  this.containerContenttextcopyFontSize1 = res.fields.copyFontSize;
                  document.documentElement.style.setProperty('--containerContenttextcopyFontSize1', this.containerContenttextcopyFontSize1);
                  this.containerContenttextcopyFontColor1 = res.fields.copyFontColor.value;
                  document.documentElement.style.setProperty('--containerContenttextcopyFontColor1', this.containerContenttextcopyFontColor1);
                  this.containerContenttextcopyTextAlign1 = res.fields.copyTextAlign;
                  document.documentElement.style.setProperty('--containerContenttextcopyTextAlign1', this.containerContenttextcopyTextAlign1);
                  this.copyLineHeight1 = res.fields.copyLineHeight;
                  document.documentElement.style.setProperty('--copyLineHeight1', this.copyLineHeight1);
                  this.copyPadding1 = res.fields.copyPadding;
                  document.documentElement.style.setProperty('--copyPadding1', this.copyPadding1);
                  this.copyMargin1 = res.fields.copyMargin;
                  document.documentElement.style.setProperty('--copyMargin1', this.copyMargin1);

                  this.containerContenttextcopyFontSizeMobile1 = res.fields.copyFontSizeMobile;
                  document.documentElement.style.setProperty('--containerContenttextcopyFontSizeMobile1', this.containerContenttextcopyFontSizeMobile1);
                  this.containerContenttextcopyTextAlignMobile1 = res.fields.copyTextAlignMobile;
                  document.documentElement.style.setProperty('--containerContenttextcopyTextAlignMobile1', this.containerContenttextcopyTextAlignMobile1);
                  this.copyLineHeightMobile1 = res.fields.copyLineHeightMobile;
                  document.documentElement.style.setProperty('--copyLineHeightMobile1', this.copyLineHeightMobile1);
                  this.copyPaddingMobile1 = res.fields.copyPaddingMobile;
                  document.documentElement.style.setProperty('--copyPaddingMobile1', this.copyPaddingMobile1);
                  this.copyMarginMobile1 = res.fields.copyMarginMobile;
                  document.documentElement.style.setProperty('--copyMarginMobile1', this.copyMarginMobile1);



                });

                this.containerContentbg = res.fields.backgroundColor.value;
                document.documentElement.style.setProperty('--containerContentbg', this.containerContentbg);
                if (res.fields.roundedCorners === true) {
                  this.containerContentradius = res.fields.borderRadius;
                  document.documentElement.style.setProperty('--containerContentradius', this.containerContentradius);
                }
                this.containerContentpadding = res.fields.containerPadding;
                document.documentElement.style.setProperty('--containerContentpadding', this.containerContentpadding);
                this.containerContentwidth = res.fields.containerWidth;
                document.documentElement.style.setProperty('--containerContentwidth', this.containerContentwidth); 
                this.containerContentmarginMobile = res.fields.containerMarginMobile;
                document.documentElement.style.setProperty('--containerContentmarginMobile', this.containerContentmarginMobile);
                this.containerContentmargin = res.fields.containerMargin;
                document.documentElement.style.setProperty('--containerContentmargin', this.containerContentmargin);
                this.containerContentpaddingMobile = res.fields.containerPaddingMobile;
                document.documentElement.style.setProperty('--containerContentpaddingMobile', this.containerContentpaddingMobile);
              });
            } else {
              console.log("res.fields.columnContent[0] not exists");
            }

            if (res.fields.columnContent[1]) {
              let d = res.fields.columnContent[1].sys.id;
              this.contentfulservice.getdatapreview(d).subscribe(res => {
                let f = res.fields.containerContent[0].sys.id;
                this.contentfulservice.getdatapreview(f).subscribe(res => {
                  this.columnContent1 = this._returnHtmlFromRichText(res.fields.text);
                  this.columnContent1textFontSize = res.fields.textFontSize;
                  document.documentElement.style.setProperty('--columnContent1textFontSize', this.columnContent1textFontSize);
                  this.columnContent1textFontSizeMobile = res.fields.textFontSizeMobile;
                  document.documentElement.style.setProperty('--columnContent1textFontSizeMobile', this.columnContent1textFontSizeMobile);
                  this.columnContent1textAlignment = res.fields.textAlignment;
                  document.documentElement.style.setProperty('--columnContent1textAlignment', this.columnContent1textAlignment);
                  this.columnContent1textFontColor = res.fields.textFontColor;
                  document.documentElement.style.setProperty('--columnContent1textFontColor', this.columnContent1textFontColor);

                  if (res.fields.hasOwnProperty("imageOnlyAlignment")) { this.imageOnlyAlignment1 = res.fields.imageOnlyAlignment } else { console.log("imageOnlyAlignment not exists") }
                  if (res.fields.hasOwnProperty("imagePadding")) { this.imagePadding1 = res.fields.imagePadding } else { console.log("imagePadding not exists") }
                  if (res.fields.hasOwnProperty("imageMargin")) { this.imageMargin1 = res.fields.imageMargin } else { console.log("imageMargin not exists") }
                  if (res.fields.hasOwnProperty("imageWidth")) { this.imageWidth1 = res.fields.imageWidth } else { console.log("imageWidth not exists") } 
                  if (res.fields.hasOwnProperty("imageWidthMobile")) { this.imageWidthMobile1 = res.fields.imageWidthMobile } else { console.log("imageWidthMobile not exists") } 
                  if (res.fields.hasOwnProperty("imagePaddingMobile")) { this.imagePaddingMobile1 = res.fields.imagePaddingMobile } else { console.log("imagePaddingMobile not exists") }
                  if (res.fields.hasOwnProperty("imageMarginMobile")) { this.imageMarginMobile1 = res.fields.imageMarginMobile } else { console.log("imageMarginMobile not exists") }

                  document.documentElement.style.setProperty('--imageOnlyAlignment1', this.imageOnlyAlignment1 ? this.imageOnlyAlignment1 : "left");
                  document.documentElement.style.setProperty('--imagePadding1', this.imagePadding1 ? this.imagePadding1 : "0");
                  document.documentElement.style.setProperty('--imageMargin1', this.imageMargin1 ? this.imageMargin1 : "0");
                  document.documentElement.style.setProperty('--imageWidth1', this.imageWidth1 ? this.imageWidth1 : "164px"); 
                  document.documentElement.style.setProperty('--imageWidthMobile1', this.imageWidthMobile1 ? this.imageWidthMobile1 : "164px"); 
                  document.documentElement.style.setProperty('--imagePaddingMobile1', this.imagePaddingMobile1 ? this.imagePaddingMobile1 : "0");
                  document.documentElement.style.setProperty('--imageMarginMobile1', this.imageMarginMobile1 ? this.imageMarginMobile1 : "0");

                  this.columnContent1img = res.fields.image.sys.id;
                  this.contentfulservice.getassets(this.columnContent1img).subscribe(res => {
                    this.columnContent1imgurl = res.fields.file.url;
                  });
                });
                this.columnContent1width = res.fields.containerWidth;
                document.documentElement.style.setProperty('--columnContent1width', this.columnContent1width);
                this.columnContent1margin = res.fields.containerMargin;
                document.documentElement.style.setProperty('--columnContent1margin', this.columnContent1margin);
                this.columnContent1padding = res.fields.containerPadding;
                document.documentElement.style.setProperty('--columnContent1padding', this.columnContent1padding); 
                this.columnContent1marginMobile = res.fields.containerMarginMobile;
                document.documentElement.style.setProperty('--columnContent1marginMobile', this.columnContent1marginMobile);
                this.columnContent1paddingMobile = res.fields.containerPaddingMobile;
                document.documentElement.style.setProperty('--columnContent1paddingMobile', this.columnContent1paddingMobile);
              });
            } else {
              console.log("res.fields.columnContent[1] not exists");
            }

            if (res.fields.columnContent[2]) {
              let e = res.fields.columnContent[2].sys.id;
              this.contentfulservice.getdatapreview(e).subscribe(res => {
                let h = res.fields.containerContent[0].sys.id;
                this.contentfulservice.getdatapreview(h).subscribe(res => {
                  this.containerContentheading = res.fields.heading;
                  // this.containerContentheading = this._returnHtmlFromRichText(res.fields.heading);
                  let i = res.fields.resourceSet[0].sys.id;
                  this.contentfulservice.getdatapreview(i).subscribe(res => {
                    if (res.fields.formResources[0]) {
                      let i = res.fields.formResources[0].sys.id;
                      this.contentfulservice.getdatapreview(i).subscribe(res => {
                        console.log("containerContent0", res.fields.label);
                        this.privacyContentlabel = this._returnHtmlFromRichText(res.fields.label);
                      });
                    } else { console.log("formResources is not exists") }
                    if (res.fields.formResources[1]) {
                      let i = res.fields.formResources[1].sys.id;
                      this.contentfulservice.getdatapreview(i).subscribe(res => {
                        // this.privacyContentlabel = this._returnHtmlFromRichText(res.fields.label);
                      });
                    } else { console.log("formResources is not exists") }
                    if (res.fields.formResources[2]) {
                      let i = res.fields.formResources[2].sys.id;
                      this.contentfulservice.getdatapreview(i).subscribe(res => {
                        this.firstname = this._returnHtmlFromRichText(res.fields.label);
                      });
                    } else { console.log("formResources is not exists") }
                    if (res.fields.formResources[3]) {
                      let i = res.fields.formResources[3].sys.id;
                      this.contentfulservice.getdatapreview(i).subscribe(res => {
                        this.lastName = this._returnHtmlFromRichText(res.fields.label);
                      });
                    } else { console.log("formResources is not exists") }
                    if (res.fields.formResources[4]) {
                      let i = res.fields.formResources[4].sys.id;
                      this.contentfulservice.getdatapreview(i).subscribe(res => {
                        this.Email = this._returnHtmlFromRichText(res.fields.label);
                      });
                    } else { console.log("formResources is not exists") }
                    if (res.fields.formResources[5]) {
                      let i = res.fields.formResources[5].sys.id;
                      this.contentfulservice.getdatapreview(i).subscribe(res => {
                        this.NPI = this._returnHtmlFromRichText(res.fields.label);
                      });
                    } else { console.log("formResources is not exists") }
                  });
                });

                this.columnContent2containerWidth = res.fields.textFontColor;
                document.documentElement.style.setProperty('--columnContent2containerWidth', this.columnContent2containerWidth);
                this.borderWidth = res.fields.borderWidth;
                document.documentElement.style.setProperty('--borderWidth', this.borderWidth);
                this.borderColor = res.fields.borderColor.value;
                document.documentElement.style.setProperty('--borderColor', this.borderColor);
                this.borderRadius = res.fields.borderRadius;
                document.documentElement.style.setProperty('--borderRadius', this.borderRadius);
                this.containerPadding = res.fields.containerPadding;
                document.documentElement.style.setProperty('--containerPadding', this.containerPadding);
                this.containerMargin = res.fields.containerMargin;
                document.documentElement.style.setProperty('--containerMargin', this.containerMargin);
                this.containerPaddingMobile = res.fields.containerPaddingMobile;
                document.documentElement.style.setProperty('--containerPaddingMobile', this.containerPaddingMobile);
                this.containerMarginMobile = res.fields.containerMarginMobile;
                document.documentElement.style.setProperty('--containerMarginMobile', this.containerMarginMobile);
              });
            } else {
              console.log("res.fields.columnContent[2] not exists");
            }

          });
        } else {
          console.log("columns[1] not exists");
        }
      } else {
        console.log("columnBlock not exists")
      }
    });
  }
  copy(arg0: string, copy: any) {
    throw new Error('Method not implemented.');
  }
  onNonLinkButtonClick(index: number) {
    this.nonLinkSelectedButtonIndex = index;
  }

  onNonLinkButtonMouseEnter(index: number) {
    this.nonLinkHoveredIndex = index;
  }

  onNonLinkButtonMouseLeave() {
    this.nonLinkHoveredIndex = -1;
  }

  onLinkButtonClick(index: number) {
    this.linkSelectedButtonIndex = index;
  }

  onLinkButtonMouseEnter(index: number) {
    this.linkHoveredIndex = index;
  }

  onLinkButtonMouseLeave() {
    this.linkHoveredIndex = -1;
  }

  isDropdownOpen = false;
  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  // Function to extract "Prescribing Information" text
  private extractPrescribingInformation(textNode: any): string {
    let prescribingInfo = '';

    // Check if the textNode has content
    if (textNode && textNode.content) {
      textNode.content.forEach((block: any) => {
        if (block.nodeType === 'paragraph') {
          block.content.forEach((inline: any) => {
            if (inline.nodeType === 'hyperlink' && inline.data.uri) {
              // Check if the hyperlink text is "Prescribing Information"
              if (inline.content[0].value === 'Prescribing Information') {
                prescribingInfo = inline.data.uri; // Get the URL or any other value you need
              }
            }
          });
        }
      });
    }

    return prescribingInfo; // Return the extracted value
  }

  private extractPrescribingInformationtext(textNode: any): string {
    let prescribingInfotext = '';

    // Check if the textNode has content
    if (textNode && textNode.content) {
      textNode.content.forEach((block: any) => {
        if (block.nodeType === 'paragraph') {
          block.content.forEach((inline: any) => {
            if (inline.nodeType === 'hyperlink' && inline.data.uri) {
              // Check if the hyperlink text is "Prescribing Information"
              if (inline.content[0].value === 'Prescribing Information') {
                prescribingInfotext = inline.content[0].value; // Get the URL or any other value you need
              }
            }
          });
        }
      });
    }

    return prescribingInfotext; // Return the extracted value
  }
  private extractPrescribingInformationtext1(textNode: any): string {
    let prescribingInfotext = '';

    // Check if the textNode has content
    if (textNode && textNode.content) {
      textNode.content.forEach((block: any) => {
        if (block.nodeType === 'paragraph') {
          block.content.forEach((inline: any) => {
            if (inline.nodeType === 'hyperlink' && inline.data.uri) {
              // Check if the hyperlink text is "Prescribing Information"
              if (inline.content[0].value === 'Important Safety Information') {
                prescribingInfotext = inline.content[0].value; // Get the URL or any other value you need
              }
            }
          });
        }
      });
    }

    return prescribingInfotext; // Return the extracted value
  }
  isDropdownOpen2: boolean;

  hasValidContent(content: string): boolean {
    if (!content) return false;
    // Remove all HTML tags and check if there's any text content
    const textContent = content.replace(/<[^>]*>/g, '').trim();
    return textContent.length > 0;
  }

  toggleDropdown2() {
    this.isDropdownOpen2 = !this.isDropdownOpen2;
  }
  handlePFileClick(event: MouseEvent) {
    let el = event.target as HTMLElement;
    const boundary = event.currentTarget as HTMLElement;
    while (el && el !== boundary) {
      if (el.tagName && el.tagName.toLowerCase() === 'a') {
        const anchor = el as HTMLAnchorElement;
        const text = (anchor.textContent || '').trim();
        if (text === 'Important Safety Information') {
          event.preventDefault();
          this.scrollToElementById('contentbox');
        }
        break;
      }
      el = el.parentElement as HTMLElement;
    }
  }
}
