<div *ngIf="offsetFlag" class="contain">
  <div class=" pe-3 stickyheader">
    <div class="container" style="display: flex;justify-content: space-between;">
    <div class="shortStickyHeader" >{{shortStickyHeader}}</div>
    <a style="color: var(--shortStickyContentfontColor);" (click)="scrollToElementById('contentbox')">See More
      <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
      </svg>
    </a>
  </div>
  </div>
    <div class="containers row shortStickyContent"> 
      <div class="bottom-isi" > 
        <div class="container">
        <div class="shortStickyContentheading" [innerHtml]="shortStickyContentheading"></div>
        <div class="shortStickyContenttext" [innerHtml]="shortStickyContenttext"></div>
      </div>
      </div>
    </div>
  </div>
  
   