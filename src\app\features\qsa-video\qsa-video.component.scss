.contain {
    background-color: #fff;
    padding: 15px;
    font-weight: 600;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top:20px;
}

.container{
    position: relative;
    margin-bottom: 20px;
    margin-top: 30px;
}

.about{
    color: var(--primaryColor);
    font-size: 45px;
    font-weight: 650;
}

.para{
    color: var(--fontColor);
    font-size: 25px;
}

::ng-deep body.vp-center{
    display: block !important;
}

::ng-deep iframe{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

.video-wrapper{
    position: relative;
    width: 100%;
    overflow: hidden;
    padding-top: 56.25%;
}