@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;900&display=swap');


.container{
    // position: relative;
    // left: 1.5rem;
    // top: 3rem;
    margin-top: 20px;
}

.about{
    color: var(--primaryColor);
    font-size: 45px;
    font-weight: 650;
}

.para{
    color: var(--fontColor);
    font-size: 25px;
}

// @media (max-width:768px){
//     .container{
//         position: relative;
//         left:0.5rem ;
//         top: -2rem;
//     }
// }


// *{
//     margin: 0;
//     padding: 0;
// }

// .content-left{
//     font-family: 'Roboto' , sans-serif;
//     margin-top: 10px;
//     margin: 50px 10px;
// }

// .content-left1{
//     font-family: 'Roboto' , sans-serif;
//     margin-bottom: 30px;
// }

// .main-text{
//     color: #3153A2;
//     font-family: 'Roboto' , sans-serif;
//     font-size: 45px;
//     font-weight: 600;
//     line-height: 50px;
//     letter-spacing: 0px;
// }

// .sub-text{
//     font-family: 'Roboto' , sans-serif;
//     margin-top: 30px;
//     font-size: 22px;
//     font-weight: 400;
//     line-height: 36px;
// }

// mat-panel-title{
//     font-size: 25px;
//     font-weight: 600;
//     color: #333333;
// }

// mat-panel-title:hover{
//     color: #3153A2;
// }

// mat-icon {
//     color: #FFC000;
//     font-weight: 800;
// }

// .mat-expansion-panel{
//     box-shadow:none;
// }

// .para-text{
//     border: 1px solid #e9e5e5;
//     padding: 15px;
//     -webkit-box-sizing: border-box;
//     box-sizing: border-box;
//     font-size: 1rem;
//     line-height: 1.7;
// }

// .qsa-form{
//     padding: 2px 40px 10px 40px;
//     box-shadow: -6px -6px 15px -1px #ebe9e9, 6px 6px 15px -1px #ebe9e9;
//     width: 74%;
//     height: 109%;
//     border-radius: 11px;
//     margin: 0px 0px 5px 89px;
//     position: relative;
//     top: -6rem;
// }

// #medicine{
//     color:#3153A2 ;
//     padding: 17px 0px 23px 0px;
// }

// #medicines{
//     padding: 10px 10px 10px 10px;
// }

// .medicines{
//     padding: 0 0 25px 0;
// }

// .form-check{
//     padding: 0px 0px 25px 29px;
// }

// .form-button{
//     display: flex;
//     justify-content: center;
// }

// .button{
//     padding: 13px;
//     font-size: 24px;
//     border-radius: 9px;
//     border: none;
//     color: #3254A2;
//     font-weight: 700;
//     background-color: #FFC000;
// }

// .button:hover{
//     color: #fff;
//     background-color: #3153A2;
// }

// @media (max-width:768px){
//     .col-6{
//         width: 100%;
//     }

//     mat-panel-title{
//         font-size: 16px;
//     }

//     .qsa-form{
//         padding: 0 40px 0 40px;
//         width: 100%;
//         margin: 0;
//         position: relative;
//         top: 0;
//     }

//     .main-text{
//         font-size: 40px;
//     }
// }
