import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AppComponent } from 'src/app/app.component';
import { AboutUsComponent } from './about-us/about-us.component';
import { InterventionMapComponent } from './intervention-map/intervention-map.component';
import { UsersComponent } from './users.component';

const routes: Routes = [
  {
    path: '',
    component: UsersComponent,
  },
  {
    path:'about',
    component:AboutUsComponent,
  },
  {
    path:'intervention',
    component:InterventionMapComponent,
  }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UsersRoutingModule { }
