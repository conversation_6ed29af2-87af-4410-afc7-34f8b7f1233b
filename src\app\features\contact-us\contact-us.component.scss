// .contain {
//     background-color: #fff;
//     padding: 15px;
//     font-weight: 600;
//     flex-grow: 1;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     margin-top:20px;
// }

.container{
    // position: relative;
    // left: 1.5rem;
    // top: 3rem;
    margin-top: 30px;
    min-height: calc(100vh - 125px);
}

.about{
    color: var(--primaryColor);
    font-size: 45px;
    font-weight: 650;
}

.para{
    color: var(--fontColor);
    font-size: 25px;
}

// @media (max-width:768px){
//     .container{
//         position: relative;
//         left:0.5rem ;
//         top: -2rem;
//     }
// }
