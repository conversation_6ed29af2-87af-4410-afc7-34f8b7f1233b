

.sidenav {
  width: 380px;
  background-color: var(--primaryColor);
  z-index: 9 !important;
}

.sidenav .mat-toolbar {
  background: inherit;
}

:host::ng-deep.mat-drawer-inner-container{
  overflow-x: hidden;
}

:host::ng-deep.mat-toolbar.mat-primary{
  background-color: var(--primaryColor);
}

.sidenav-container{
  position: relative;
  width: 100%;
}


.crossSvg{
  position: relative;
  top: 3.5rem;
  left: 18rem;
}

.crossSvg:hover{
  transition: all 0.3s ease;
  transform:rotateZ(10deg);
  font-size: 50px;
  width: 26px;
  height: 26px;
}

.sidenavMenuText{
  position: relative;
  top: 4rem;
  left: 1rem;
  
}

.sidenav-button:hover{
  color: var(--brandYellow);
}

mat-nav-list{
  position: relative;
  top: 3rem;
  left: 1rem;
  font-weight: 500;
}

.mat-toolbar.mat-primary {
  position: sticky;
  top: 0;
  z-index: 1;
  width: 100%;
  height: 13%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mat-toolbar{
  font-size:27px;
  color: #fff;
}

.mat-nav-list a{
  color: #fff;
}

.mat-nav-list a:hover{
  color: var(--brandYellow);
  background-color: var(--primaryColor);
}

svg{
  position: relative;
  left: 5rem;
  color: #fff;
}

.toggleIcon{
  font-size: 39px;
  position: relative;
  left: -9px;
}

.header-img{
  display: flex;
  justify-content: flex-start;
  cursor: pointer;
}


#sidenavButton{
  display: flex;
  justify-content: flex-end;
}

.section{
  width: 100%;
}

.imgContent{
  display: flex;
  margin-right: auto;
  margin-left: auto;
  position: relative;
  max-width: calc(100vw - 6vw);
}

.svgContent{
  display: flex;
  margin-right: auto;
  margin-left: auto;
  position: relative;
}

.imgContent1{
  position: relative;
  min-height: 1px;
  display: flex;
}

.imgCon{
    position: relative;
    width: 100%;
    flex-wrap: wrap;
    align-content: flex-start;
    display: flex;
}

.imgCon1{
  text-align: left;
}

.imgCont{
  margin: 0px 0px 0px 0px; 
  padding: 10px 0px 10px 0px;
}

.imgSvg{
    position: relative;
    width: 100%;
    flex-wrap: wrap;
    align-content: flex-start;
    padding: 17px 0px 10px 10px;
    display: flex;
    justify-content: flex-end;
}

.svgCon{
  text-align: left;
}

.svgCont{
  margin: 0px 0px 0px 0px; padding: 10px 0px 10px 70px;
}



@media (min-width: 970) and (max-width: 1200) {

  .sidenav-button{
    position: relative;
    left: -100rem;
  }
 
}


@media (min-width:968px){

  .headerContent{
    width: 78%;
    align-items: center;
  }

  .mat-icon{
    font-size: 50px;
  }
  

  .sidenav-button:hover{
    color: #FFC000;
  }

}

@media (max-width:768px){

  .mat-toolbar.mat-primary{
    padding: 14px;
  }

  .sidenav-button{
    position: relative;
    left: 2rem;
  }

  #sidenavButton{
    align-items: center;
    position: relative;
    left: -2rem;
  }

  .imgContent1{
    width: 70%;
  }

  .svgContent{
    width: 30%;
  }

  .imgSvg{
    padding: 25px 0px 10px 10px;   
  }
 .imgCont{
  padding: 15px 0 10px 0;
 }
}

@media (min-width:1200px) {
  .imgContent{
    max-width: 1170px;
  }
  
}

@media (max-width:468px) {
  .header-img img{
    width: 100%;
    min-width: 50px !important;
  }
  
}

@media (max-width:280px) {
  .toggleIcon{
    font-size: 20px !important;
  }
  .svgCont{
    padding: 0 !important;
  }
  #sidenavButton{
    left: -1rem;
  }
  
}