import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Meta, Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { documentToHtmlString } from '@contentful/rich-text-html-renderer';
import { BLOCKS, INLINES } from '@contentful/rich-text-types';
import * as htmlToPdfmake from 'html-to-pdfmake';
import { ClipboardService } from 'ngx-clipboard';
import { ToastrService } from 'ngx-toastr';
import * as pdfMake from 'pdfmake/build/pdfmake';
import * as pdfFonts from 'pdfmake/build/vfs_fonts';
import { from, Observable, of, throwError, timer, zip } from 'rxjs';
import { concatMap, delayWhen, mergeMap, retryWhen, take } from 'rxjs/operators';
import { ContentfulService } from 'src/app/services/contentful.service';
import { MergePdfService } from 'src/app/services/merge-pdf.service';

@Component({
    selector: 'app-resources-page-preview',
    templateUrl: './resources-page-preview.component.html',
    styleUrls: ['./resources-page-preview.component.scss']
})
export class ResourcesPagePreviewComponent implements OnInit {


    @ViewChild('closebutton', {
        static: false
    }) closebutton;


    hideToggle = false;
    heading: any;
    headinglist: any;
    subhead: Observable<[]>;
    copyRights: any;
    landFootImg: String;
    logo: String;
    dynamicMsg: any;
    paraContent: any;
    checked: boolean = false;
    pdfValues: any = [];
    myemailForm!: FormGroup
    provideForm!: FormGroup
    submitted = false;
    firstList: any;
    firstListUrl: any;
    firstListValue: any;
    firstListUrlValue: any;
    secondListValue: any;
    secondList: any;
    secondListUrl: any;
    secondListValue2: any;
    secondListValue2Url: any;
    secondListValue2Sl: any;
    secondListValue2Tl: any;
    secondListValue2Fl: any;
    secondListValue2TlUrl: any;
    thirdList: any;
    resourceErrorMessage: "Please select any resource"
    reqId: any
    getfile: any
    routeListener
    userId
    topheadingAlignment: any;
    panelOpenState = false;
    indication: any;
    isi: any;
    resid: any;
    isi_id: any;
    dynamicmssg_id: any;
    branddiseaseResources_id: any;
    pageTitle_id: any;
    drugdata: any;
    versionone_med: any;
    versionone_medname: any;
    result2: any;
    resid2: any;
    seotitle: any;
    seodes: any;
    hidePagesFromSearchEnginesNoindex: any;
    excluedLinksFromSearchRankingsNofollow: any;
    pagekeywords: any;
    prescribe_id: any;
    presinfo_id: any;
    copyRights_id: any;
    paraContent_id: any;
    dymssg_id: any;
    dynamicMsg2_id: any;
    icon_id: any;
    icon1_id: any;
    icon2_id: any;
    heading_id: any;
    list_id: any;
    list1_id: any;
    list2_id: any;
    list3_id: any;
    brandheader_id: any;
    qsaUrl: any;
    drug: any;
    resourceres: any;
    brandresourceres: any;
    dynamicmssgres: any;
    footerres: any;
    logoimgid: any;
    shortstickyisi: any;
    gradient1: any;
    gradient2: any;
    gradient3: any;
    gradientrotation: void;
    gradientrotation2: any;
    resourceprimarycolor: any;
    resourcesecondarycolor: any;
    resourcetertiaryColor: any;
    footgradient1: any;
    footgradient2: any;
    footgradientrotation2: any;
    resourceh1color: any;
    resourceh2color: any;
    resourceh3color: any;
    resourcehorizontalRule: any;
    resourcebuttonBackgroundColor: any;
    resourcebuttonFontColor: any;
    resourcebuttonBackgroundRollOverColor: any;
    resourcebuttonRolloverFontColor: any;
    resourcebuttonActiveFontColor: any;
    resourcebuttonBackgroundActiveColor: any;
    resourcefontColor: any;
    resourcefontLinkColor: any;
    resourcefontLinkRolloverColor: any;
    resourcefontLink: any;
    resourcefontFamily: any;
    realtedbuttonids: any;
    button1name: any;
    button1link: any;
    button1textcol: any;
    button1backcol: any;
    button2name: any;
    button2link: any;
    button2textcol: any;
    button2backcol: any;
    relatedbuttonarray: any[];
    presinternalname: any;
    p_file: any;
    resourcebrandFooterBackgroundColor: any;
    resourceHeaderColor: any;
    headerIndicationHeaderFontSize: any;
    headerIndicationCopyFontSize: any;
    headerIndicationFontColor: any;
    isiHeadersFontSize: any;
    isiHeadersColors: any;
    ginacontent: any;
    pdfUrls: any[] = [];
    convertedUrls: any[] = [];
    mergedPdfUrl: Promise<any>;
    viewpdfurl: any;
    provideemailbody: any;
    indication_text: any;
    emailcustomLink: any;
    provideemailsubject: any;
    alertmssg: boolean;
    allcontents: boolean;
    isiTextFontSize: any;
    bodyTextFontSize: any;
    bodyTextFontWeight: any;
    isiTextFontWeight: any;
    bodyTextLineHeight: any;
    isiTextLineHeight: any;
    Textbanneronly: any;
    Textbanneronlyb: any;
    Textbannerref: any;
    Textbannerrefs: any;
    Textmobilebanneronly: any;
    Textmobilebanneronlyb: any;
    Textmobilebannerref: any;
    Textmobilebannerrefs: any;
    headingFontSize: any;
    referencesFontColor: any;
    referencesFontSize: any;
    bannerbg: any;
    headingFontColor: any;
    headingFontSizeMobile: any;
    dyanamicMessagingWidth: any;
    headingAlignment: any;
    referencesFontSizeMobile: any;
    imageText: any;
    imageMobile: any;
    imageBanner: any;
    ImageBannerheight: any;
    ImageBannerwidth: any;
    imageBannerMobile: any;
    imgtextFontColor: any;
    txtbannerwidth: any;
    textbanneronlyb: any;
    textmobilebanneronlyb: any;
    headingAlignmentMobile: any;
    referencesAlignment: any;
    referencesAlignmentMobile: any;
    dynamicMessageLayoutType: any;
    imagePlacement: any;
    imageMobilePlacement: any;
    textFontSize: any;
    textFontSizeMobile: any;
    imagewidth: any;
    imageheight: any;
    imageMobilewidth: any;
    imageMobileheight: any;
    textimageMobileTextPadding: any;
    textimageTextPadding: any;
    textBannerPadding: any;
    textBannerPaddingMobile: any;
    topheadingbg: any;
    moreResourcesPanelState = false;
    columns1: any;
    columns2: any;
    brandheadertextContent: any;
    leftcontentimage: any;
    brandheadercolumnContent2: any;
    brandheadertextContent2: any;
    brandheaderimageurl2: any;
    brandheaderdropmenu2: any;
    brandnavigationLink: any;
    branddropdownLinks: any;
    branddropdowntext: any;
    indicationtextContent: any;
    textBoxBelowHeaderIndication: any;
    brandbgcolor: any;
    brandcontentwidth: any;
    LeftcolumnWidth: any;
    LeftcolumnWidth1: any;
    rightcolumnWidth: any;
    DynamicMessage: any;
    DynamicMessagecontent: any;
    DynamicMessage1: any;
    DynamicMessagecontent1: any;
    DynamicMessage2: any;
    DynamicMessagecontent2: any;
    DynamicMessage3: any;
    DynamicMessagecontent3: any;
    openIcon: any;
    closeIcon: any;
    textBoxAboveResourcecontent: any;
    textAndLinkResources: any;
    buttonsSectionBlockcecontent: any;
    emailSubjectForSendingPersonalOgeqsa: any;
    Emailimage: any;
    buttonemailtext: any;
    buttonsSectionBlocksId2: any;
    buttonsPrintBlocksId: any;
    buttonPrinttext: any;
    Printimage: any;
    buttonsSectionBlocksId3: any;
    buttonsCopyLinkBlocksId: any;
    buttonCopyLinktext: any;
    CopyLinkimage: any;
    buttonsSectionBlock0: any;
    brandheaderfontsize: any;
    brandheadertextAlignment: any;
    brandheaderfontsize2: any;
    brandheadertextAlignment2: any;
    dropdownBackgroundColor: any;
    textBoxBelowHeaderalignment: any;
    logoimg: any;
    textBoxBelowHeaderfontSize: any;
    textBoxBelowHeaderlineHeight: any;
    textBoxBelowHeaderfontSizemobile: any;
    textBoxBelowHeaderlineHeightmobile: any;
    textBoxBelowHeaderfontColor: any;
    textBoxBelowHeaderlinkColor: any;
    headerIsialignment: any;
    headerIsifontSize: any;
    headerIsifontColor: any;
    headerIsiwidth: any;
    headerIsibackgroundColor: any;
    dropdownLinkRolloverBackgroundColor: any;
    dropdownFontColor: any;
    heading_backgroundColor: any;
    heading_openIcon: any;
    arrowdown: any;
    heading_closeIcon: any;
    arrowup: any;
    heading_iconBackgroundColor: any;
    heading_iconRadius: any;
    brandcolumnBlockPadding: any;
    headingMobile: any;
    textMobile: any;
    textAlignment: any;
    topheadingAlignmentMobile: any;
    textAlignmentMobile: any;
    textFontSize1: any;
    textFontSizeMobile1: any;
    textFontColor: any;
    textFontColor2: any;
    textBlockPadding: any;
    dynamicMsgbg: any;
    borderRadiusRoundedCorners: any;
    iconBackgroundColor: any;
    iconBackgroundBorderRadius: any;
    copyFont: any;
    copyFontColor: any;
    copyFontSize: any;
    copyFontSizeMobile: any;
    copyFont1: any;
    copyFontColor1: any;
    copyFontSize1: any;
    copyFontSizeMobile1: any;
    copyFont2: any;
    copyFontColor2: any;
    copyFontSize2: any;
    copyFontSizeMobile2: any;
    copyFont3: any;
    copyFontColor3: any;
    copyFontSize3: any;
    copyFontSizeMobile3: any;
    textBoxAboveRestextAlignment: any;
    textBoxAboveResfontSize: any;
    textBoxAboveResfontSizeMobile: any;
    textBoxAboveResfontColor: any;
    textBoxResourceListsbg: any;
    textBoxResourceLsborderRadius: any;
    ResiconBackgroundColor: any;
    ResiconRolloverBackgroundColor: any;
    ResLsiconWidth: any;
    ResLsiconHeight: any;
    expandIcon: any;
    previewIcon: any;
    customButtonBackgroundColor: any;
    customButtonBackgroundRolloverColor: any;
    buttonBorderRadius: any;
    buttonPadding: any;
    buttonWidth: any;
    buttonHeight: any;
    customButtonBackgroundColor1: any;
    customButtonBackgroundRolloverColor1: any;
    buttonBorderRadius1: any;
    buttonPadding1: any;
    buttonWidth1: any;
    buttonHeight1: any;
    customButtonBackgroundColor2: any;
    customButtonBackgroundRolloverColor2: any;
    buttonBorderRadius2: any;
    buttonPadding2: any;
    buttonWidth2: any;
    buttonHeight2: any;
    buttonsSectionBlock2: any;
    rescopyFont: any;
    rescopyFontColor: any;
    rescopyFontSize: any;
    rescopyFontSizeMobile: any;
    rescopyFont1: any;
    rescopyFontColor1: any;
    rescopyFontSize1: any;
    rescopyFontSizeMobile1: any;
    rescopyFont2: any;
    rescopyFontColor2: any;
    rescopyFontSize2: any;
    rescopyFontSizeMobile2: any;
    btnsec0textAlignment: any;
    btnsec0fontSize: any;
    btnsec0fontSizeMobilet: any;
    btnsec0fontColor: any;
    stickyIsi_id: any;
    stickyIsi: any;
    shortStickyIsi: any;
    massecpadding: any;
    textBlockMargin: any;
    healthToolsTitleFontSize: any;
    healthToolsTitleFontSizeMobile: any;
    healthToolsTitleFontWeight: any;
    healthToolsTitleFontColor: any;
    healthToolsDescriptionFontSize: any;
    healthToolsDescriptionFontSizeMobile: any;
    healthToolsDescriptionFontColor: any;
    copyTextAlign: any;
    copyTextAlign1: any;
    copyTextAlign2: any;
    copyTextAlign3: any;
    copypadding: any;
    linkFontColor: any;
    DynamicMessage4: any;
    DynamicMessagecontent4: string;
    copyFontColor4: any;
    copyFontSize4: any;
    copyFontSizeMobile4: any;
    copyTextAlign4: any;
    copyFont4: string;
    textBoxAboveBlockMargin: any;
    expandIconPopUpContent: any;
    expandIconbackgroundColor: any;
    expandIconborderRadius: any;
    expandIcontextBlockPadding: any;
    expandIconfontColor: any;
    expandIconwidth: any;
    expandIcontextAlignment: any;
    expandIconfontSize: any;
    expandIconfontSizeMobile: any;
    previewIconPopUpContent: any;
    previewIconPopUpContentbackgroundColor: any;
    previewIconPopUpContentborderRadius: any;
    previewIconPopUpContenttextBlockPadding: any;
    previewIconPopUpContentfontColor: any;
    previewIconPopUpContentwidth: any;
    previewIconPopUpContenttextAlignment: any;
    previewIconPopUpContentfontSize: any;
    previewIconPopUpContentfontSizeMobile: any;
    checkboxSelectPopUpContent: any;
    checkboxSelectPopUpContentbackgroundColor: any;
    checkboxSelectPopUpContentborderRadius: any;
    checkboxSelectPopUpContenttextBlockPadding: any;
    checkboxSelectPopUpContentfontColor: any;
    checkboxSelectPopUpContentwidth: any;
    checkboxSelectPopUpContenttextAlignment: any;
    checkboxSelectPopUpContentfontSize: any;
    checkboxSelectPopUpContentfontSizeMobile: any;
    footerrestextAlignment: any;
    footerrestextAlignmentMobile: any;
    indicationtextContenttext: string;
    indicationtextContenttext1: string;
    copyrightfooterContentAlignment: any;
    textLineHeight: any;
    textLineHeightMobile: any;
    copyLineHeight: any;
    copyLineHeight1: any;
    copyLineHeight2: any;
    copyLineHeight3: any;
    copyLineHeight4: any;
    mainIsiHeader: any;
    mainIsiContentheading: string;
    mainIsiContenttextAlignment: any;
    mainIsiContentfontSize: any;
    mainIsiContentlineHeight: any;
    mainIsiContentfontSizeMobile: any;
    mainIsiContentlineHeightMobile: any;
    mainIsiContentfontColor: any;
    mainIsiContenttextBlockPadding: any;
    mainIsiContenttextBlockMargin: any;
    mainIsiContentwidth: any;
    mainIsiContenttext: string;
    mainIsiContenttextAlignment1: any;
    mainIsiContentfontSize1: any;
    mainIsiContentlineHeight1: any;
    mainIsiContentfontSizeMobile1: any;
    mainIsiContentlineHeightMobile1: any;
    mainIsiContentfontColor1: any;
    mainIsiContenttextBlockPadding1: any;
    mainIsiContenttextBlockMargin1: any;
    mainIsiContentwidth1: any;
    mainIsiContentheading1: string;
    mainIsiContenttextAlignment2: any;
    mainIsiContentfontSize2: any;
    mainIsiContentlineHeight2: any;
    mainIsiContentfontSizeMobile2: any;
    mainIsiContentlineHeightMobile2: any;
    mainIsiContentfontColor2: any;
    mainIsiContenttextBlockPadding2: any;
    mainIsiContenttextBlockMargin2: any;
    mainIsiContentwidth2: any;
    mainIsiContenttext1: string;
    mainIsiContenttextAlignment3: any;
    mainIsiContentfontSize3: any;
    mainIsiContentlineHeight3: any;
    mainIsiContentfontSizeMobile3: any;
    mainIsiContentlineHeightMobile3: any;
    mainIsiContentfontColor3: any;
    mainIsiContenttextBlockPadding3: any;
    mainIsiContenttextBlockMargin3: any;
    mainIsiContentwidth3: any;
    mainIsiMargin: any;
    mainIsiPadding: any;
    mainIsiHeaderFontSize: any;
    mainIsiHeaderFontSizeMobile: any;
    mainIsiHeaderFontWeight: any;
    mainIsiHeaderFontColor: any;
    mainIsiContentheading2: string;
    mainIsiContenttextAlignment4: any;
    mainIsiContentfontSize4: any;
    mainIsiContentlineHeight4: any;
    mainIsiContentfontSizeMobile4: any;
    mainIsiContentlineHeightMobile4: any;
    mainIsiContentfontColor4: any;
    mainIsiContenttextBlockPadding4: any;
    mainIsiContenttextBlockMargin4: any;
    mainIsiContentwidth4: any;
    mainIsiContenttext2: string;
    mainIsiContenttextAlignment5: any;
    mainIsiContentfontSize5: any;
    mainIsiContentlineHeight5: any;
    mainIsiContentfontSizeMobile5: any;
    mainIsiContentlineHeightMobile5: any;
    mainIsiContentfontColor5: any;
    mainIsiContenttextBlockPadding5: any;
    mainIsiContenttextBlockMargin5: any;
    mainIsiContentwidth5: any;
    logoimage: any;
    iconWidth: any;
    iconHeight: any;
    iconWidthMobile: any;
    iconHeightMobile: any;
    branddiseaseiconWidth: any;
    branddiseaseiconHeight: any;
    branddiseaseiconWidthMobile: any;
    branddiseaseiconHeightMobile: any;
    branddiseaselogoWidth: any;
    branddiseaselogoHeight: any;
    branddiseaselogoWidthMobile: any;
    branddiseaselogoHeightMobile: any;
    branddiseaseresWidth: any;
    branddiseaseresHeight: any;
    branddiseaseresWidthMobile: any;
    branddiseaseresHeightMobile: any;
    customButtonIconWidth: any;
    customButtonIconHeight: any;
    customButtonIconWidthMobile: any;
    customButtonIconHeightMobile: any;
    customButtonIconWidth1: any;
    customButtonIconHeight1: any;
    customButtonIconWidthMobile1: any;
    customButtonIconHeightMobile1: any;
    customButtonIconWidth2: any;
    customButtonIconHeight2: any;
    customButtonIconWidthMobile2: any;
    customButtonIconHeightMobile2: any;
    brandheaderimageurl2Width: any;
    brandheaderimageurl2Height: any;
    brandheaderimageurl2WidthMobile: any;
    brandheaderimageurl2HeightMobile: any;
    brandheaderimageurlWidth: any;
    brandheaderimageurlHeight: any;
    brandheaderimageurlWidthMobile: any;
    brandheaderimageurlHeightMobile: any;
    copyPadding: any;
    copyMargin: any;
    copyPadding1: any;
    copyMargin1: any;
    copyPadding2: any;
    copyMargin2: any;
    copyPadding3: any;
    copyMargin3: any;
    copyPadding4: any;
    copyMargin4: any;
    columnBlockPaddingMobile: any;
    fontSizeMobile: any;
    iconPadding: any;
    iconMargin: any;
    imagePadding: any;
    imageMargin: any;
    imagePadding2: any;
    imageMargin2: any;
    branddiseaseiconPadding: any;
    branddiseaseiconMargin: any;
    branddiseaseresiconPadding: any;
    branddiseaseresiconMargin: any;
    brandheaderfontsizeMobile: any;
    brandheaderfontsizeMobile2: any;
    copyTextAlignMobile1: any;
    copyLineHeightMobile1: any;
    copyPaddingMobile1: any;
    copyMarginMobile1: any;
    copyTextAlignMobile2: any;
    copyLineHeightMobile2: any;
    copyPaddingMobile2: any;
    copyMarginMobile2: any;
    copyTextAlignMobile3: any;
    copyLineHeightMobile3: any;
    copyPaddingMobile3: any;
    copyMarginMobile3: any;
    copyTextAlignMobile4: any;
    copyLineHeightMobile4: any;
    copyPaddingMobile4: any;
    copyMarginMobile4: any;
    buttonWidthMobile: any;
    buttonHeightMobile: any;
    buttonWidthMobile1: any;
    buttonHeightMobile1: any;
    buttonWidthMobile2: any;
    buttonHeightMobile2: any;
    collapsibleDefaultPosition: any;
    textBlockMarginMobile: any;
    textBlockHeaderPadding: any;
    textBlockHeaderPaddingMobile: any;
    collapsibleDefaultPosition1: any;
    branddiseasepadding: any;
    branddiseasemargin: any;
    copypaddingMobile: any;
    copymargin: any;
    copymarginMobile: any;
    healthToolsIconsVerticalAlignment: any;
    healthToolsTitleMargin: any;
    healthToolsTitleMarginMobile: any;
    indicationtextContenttexttextMobile: string;
    indicationtextContentMobile: string;
    indicationtextContenttext1textMobile: string;
    headerIsilineHeight: any;
    headerIsimargin: any;
    headerIsialignmentMobile: any;
    headerIsifontSizeMobile: any;
    headerIsilineHeightMobile: any;
    headerIsimarginMobile: any;
    textBlockPaddingMobile: any;
    textBoxBelowHeaderIndicationMobile: string;
    textBoxBelowHeaderalignmentMobile: any;
    textBoxAboveResourcecontentMobile: string;
    textBoxAboveRestextAlignmentMobile: any;
    textBoxAboveBlockMarginMobile: any;
    textBoxAboveBlockPaddingMobile: any;
    textBoxAboveBlockPadding: any;
    textBoxAboveReslineHeight: any;
    textBoxAboveReslineHeightMobile: any;
    buttonsSectionBlockcecontentMobile: string;
    btnsec0textAlignmentMobile: any;
    btnsec0textBlockMargin: any;
    btnsec0textBlockMarginMobile: any;
    btnsec0textBlockPadding: any;
    btnsec0lineHeight: any;
    btnsec0lineHeightMobile: any;
    mainIsiContentheadingMobile: string;
    mainIsiContenttextBlockPaddingMobile: any;
    mainIsiContenttextBlockMarginMobile: any;
    p_file1: any;
    p_file1Mobile: any;
    constructor(private contentfulservice: ContentfulService, private router: Router,
        private clipboardService: ClipboardService, private http: HttpClient,
        private dialogRef: MatDialog, private toast: ToastrService, private route: ActivatedRoute,
        private fb: FormBuilder, private titleService: Title, private metaservice: Meta, private mergeService: MergePdfService) {

        pdfMake.vfs = pdfFonts.pdfMake.vfs;

        this.getallpageapicalls()

        this.alertmssg = false;
        this.allcontents = true;



    }

    getallpageapicalls() {
        let str = window.location.href;
        this.qsaUrl = str;
        let wordinurl = (this.qsaUrl.substring(this.qsaUrl.lastIndexOf('/') + 1));
        console.log(wordinurl)
        this.resid = wordinurl
        console.log(this.resid)

        this.contentfulservice.getAlldatapreview().subscribe(res => {
            let masterContent = res.items;
            let id = res.items.map(item => {
                return item.sys.id;
            });
            let filteredid = (filterItems(id, this.resid))
            console.log(filteredid.length)
            if (filteredid.length == 0) {
                this.alertmssg = true;
                this.allcontents = false;
            }
        });

        function filterItems(arr, query) {
            return arr.filter(element => element.toLowerCase() === query.toLowerCase());
        }

        this.contentfulservice.getdatapreview(this.resid)
            .pipe(this.retryWithDelay())
            .subscribe(
                (data) => {
                    console.log(data);
                    this.resourceres = data;
                    this.getSeoData();
                    this.getAllContents();
                    this.getInformation();
                    this.getPrescribe();
                    this.getAllDynamicMssg();
                    this.getbrandresource();
                    this.getprovideemailbody();
                },
                (error) => {
                    console.error(error);
                }
            );

    }

    retryWithDelay(maxRetry: number = 3, delay: number = 2000): (src: Observable<any>) => Observable<any> {
        console.log("retried")
        return (src: Observable<any>) =>
            src.pipe(
                retryWhen((errors: Observable<any>) =>
                    errors.pipe(
                        mergeMap((error, i) =>
                            i < maxRetry ? timer(delay) : throwError(error)
                        )
                    )
                )
            );
    }



    scrollToElement(element: HTMLElement) {
        element.scrollIntoView({
            behavior: "smooth"
        });
    }

    private __getElementById(id: any): HTMLElement {
        const element = document.getElementById(id);
        return element;
    }

    scrollToElementById(id: any) {
        const element = this.__getElementById(id);
        this.scrollToElement(element);
    }

    ngOnInit() {



        document.getElementById("head-content").scrollIntoView();



        this.myemailForm = this.fb.group({
            fromEmail: ['<EMAIL>'],
            toEmail: ['', [Validators.required]],
            subject: [''],
            body: ['']
        })


        this.routeListener = this.route.params.subscribe(params => {
            this.userId = params['id'];
        });


        this.provideForm = new FormGroup({
            email: new FormControl('', [Validators.required]),
            qsaUrl: new FormControl('Resource Url'),
            drug: new FormControl('Drug Name'),
            sessionId: new FormControl(`${this.userId}`),
            body: new FormControl('Mail Content'),
            fromEmail: new FormControl('<EMAIL>'),
            subject: new FormControl('')
        });

        this.getAllDynamicMssg();
    }



    getprovideemailbody() {
        if (this.resourceres.fields.hasOwnProperty("emailContentForSendingPersonalOgeqsa")) {
            let body = this.resourceres.fields.emailContentForSendingPersonalOgeqsa;
            this.provideemailbody = this._returnHtmlFromRichText(body)
            let url = this.qsaUrl
            const newUrls = url.replace('http://localhost:4200/', 'https://qsasupport.com/')

            this.emailcustomLink = newUrls;
            const updatedHtmlContent = this.provideemailbody.replace(/\[URL Link\]/g, `<a href="${this.emailcustomLink}" target="_blank">${this.emailcustomLink}</a>`);
            this.provideemailbody = updatedHtmlContent;
        }
        else {
            console.log("emailContentForSendingPersonalOgeqsa not exsits")
        }
        if (this.resourceres.fields.hasOwnProperty("emailSubjectForSendingPersonalOgeqsa")) {
            let body = this.resourceres.fields.emailSubjectForSendingPersonalOgeqsa;
            this.provideemailsubject = body
        }
    }



    getSeoData() {
        this.pageTitle_id = this.resourceres.sys.id;
        this.contentfulservice.getdatapreview(this.pageTitle_id).subscribe(res => {

            if (res.fields.hasOwnProperty("seoMetadata")) {
                let seoid = res.fields.seoMetadata.sys.id
                this.titleService.setTitle(res.fields.pageTitle)
                this.contentfulservice.getdatapreview(seoid).subscribe(res => {
                    this.seotitle = res.fields.seoTitle;
                    this.seodes = res.fields.seoDescription;
                    this.hidePagesFromSearchEnginesNoindex = res.fields.hidePagesFromSearchEnginesNoindex;
                    this.excluedLinksFromSearchRankingsNofollow = res.fields.excluedLinksFromSearchRankingsNofollow;
                    this.pagekeywords = res.fields.keywords;
                    this.metaservice.addTag({ name: 'description', content: this.seodes });
                    this.metaservice.addTag({ name: 'application-name', content: this.seotitle });
                    this.metaservice.addTag({ name: 'keywords', content: this.pagekeywords });
                    this.metaservice.addTag({ name: 'noindex', content: this.hidePagesFromSearchEnginesNoindex });
                    this.metaservice.addTag({ name: 'nofollow', content: this.excluedLinksFromSearchRankingsNofollow });
                })
            }
        })
    }


    get f() {
        return this.provideForm.controls;
    }


    provideEmail() {
        let url = this.qsaUrl
        const newUrls = url.replace('http://localhost:4200/', 'https://qsasupport.com/')

        let resurl = "<a href='" + newUrls + "'>Resource URL</a>"
        this.submitted = true
        if (!this.provideForm.invalid) {
            console.log(this.provideemailbody)
            if (typeof this.provideemailbody === 'undefined') {
                this.provideemailbody = resurl;
            }
            this.provideForm.get('body').setValue(this.provideemailbody);
            this.provideForm.get('qsaUrl').setValue(this.qsaUrl);
            this.provideForm.get('drug').setValue(this.drug);
            this.provideForm.get('subject').setValue(this.provideemailsubject);
            this.provideForm.value.body = encodeURI(this.provideemailbody);
            console.log(this.provideForm.value)
            let url = 'https://api.qsasupport.com/v1/resources/request';
            this.http.post(url, this.provideForm.value).subscribe(res => {
                this.toast.success('Mail Submitted')
                console.log(res);
            })
        }
    }

    getbrandresource() {

        if (this.resourceres.fields.hasOwnProperty("branddiseaseResources")) {
            this.branddiseaseResources_id = this.resourceres.fields.branddiseaseResources.sys.id;
            this.contentfulservice.getdatapreview(this.branddiseaseResources_id)
                .pipe(this.retryWithDelay())
                .subscribe(
                    (res) => {
                        console.log("res res", res);
                        this.brandresourceres = res;
                        this.getTopHeadingstyle();
                        this.getTopHeadingcontent();
                        this.getstickyIsi();
                        this.DynamicMessageList();
                        this.textBoxAboveResourceLists();
                        this.textBoxResourceLists();
                        this.textAndLinkToSpanishResources();
                        this.getPrescribeFile();
                        this.getCopyright();
                        this.getContent();
                        this.getDynamicmsg();
                        this.getprescribingInformation();
                        this.getLogoimage();
                        this.getTextbanner();
                        this.getTextImgbanner();
                        this.getImagebanner();
                        this.getColor();
                        this.getdrugrelatedbutton()
                    },
                    (error) => {
                        console.error(error);
                    }
                );

        }
        else {
            console.log("branddiseaseResources not exsits")
        }
    }

    masterContent: any;
    getAllContents() {
        this.contentfulservice.getAlldatapreview().subscribe(res => {
            this.masterContent = res.items;
            this.getResourceContent();
        });
    }



    resourceContents = [];
    getResourceContent() {
        if (this.resourceres.fields.hasOwnProperty("branddiseaseResources")) {
            this.branddiseaseResources_id = this.resourceres.fields.branddiseaseResources.sys.id;
            console.log("branddiseaseResources_id", this.resourceres.fields.branddiseaseResources.sys.id)
            this.contentfulservice.getdatapreview(this.branddiseaseResources_id).subscribe(res => {
                res.fields.healthToolsOnOgeqsa.forEach((element) => {
                    let index = this.masterContent.filter(data => data.sys.id == element.sys.id);
                    if (index) {
                        this.resourceContents.push(index[0]);
                        if (res.fields.healthToolsOnOgeqsa.length == this.resourceContents.length) {
                            this.groupResources();
                        }
                    }
                });
            })
        } else { console.log("branddiseaseResources not exsits") }
    }

    resourceData: any;
    groupResources() {
        var filtered = this.resourceContents.filter(function (x) {
            return x !== undefined;
        });

        var groups = new Set(filtered.map(item => item.fields.audiences.sys.id));
        this.resourceData = [];
        groups.forEach(g =>
            this.resourceData.push({
                name: g,
                values: filtered.filter(i => i.fields.audiences.sys.id === g)
            }))
        if (groups.size == this.resourceData.length) {
            this.resourceData.forEach(resource => {
                this.getResourceAudianceTitle(resource.name).then((groupName) => {
                    resource.name = groupName;
                });
            });
        }
        console.log("resourceData", this.resourceData)
    }

    getResourceAudianceTitle(id) {
        let that = this;
        return new Promise(function (resolve, reject) {
            that.contentfulservice.getdatapreview(id).subscribe(res => {
                resolve(res.fields.audience);
            }, err => {
                reject(console.log(err));

            });
        });
    }
    selectedResources: any = [];
    onResourceSelect(event) {
        if (event.checked) {
            this.selectedResources.push(event);
        } else {
            this.selectedResources.pop(event);
        }
    }
    emailContent: any;

    generateEmailContent() {
        if (this.resourceres.fields.hasOwnProperty("emailContentForSendingPersonalOgeqsa")) {
            this.emailSubjectForSendingPersonalOgeqsa = this.resourceres.fields.emailSubjectForSendingPersonalOgeqsa;
            let body = this.resourceres.fields.emailContentForSendingPersonalOgeqsa;
            this.emailContent = this._returnHtmlFromRichText(body);
            let resourceContent = '';
            if (this.selectedResources.length > 0) {
                this.selectedResources.forEach(resource => {
                    resourceContent = resourceContent + "<br><b>" + resource.fields.healthResourceTitle + "</b>" + this._returnHtmlFromRichText(resource.fields.teaser) + " " + "<a href='" + resource.fields.healthResourceUrl + "'>" + resource.fields.healthResourceTitle + ".Pdf</a><br>"
                });
                this.openModal();
                this.myemailForm.get('subject').setValue(this.emailSubjectForSendingPersonalOgeqsa);
            } else {
                this.toast.error("Please select any resource")
            }
            this.emailContent = this.emailContent.replace(/\[URL Link\]/g, resourceContent);
        }
    }

    copySelectedResourceLinks() {
        this.pdfValues = [];
        localStorage.clear();
        this.pdfValues = [];
        if (this.selectedResources.length > 0) {
            this.selectedResources.forEach(resource => {
                this.pdfValues.push(resource.fields.healthResourceUrl);

                this.clipboardService.copyFromContent(this.pdfValues);
                this.toast.success('Copied to clipboard')
            });
        } else {
            this.toast.error("Please select any resource")
        }
    }

    viewPrintSelectedResourcs() {
        localStorage.clear();
        this.pdfValues = [];
        let prescribeInfo = [this.getfile]
        if (this.selectedResources.length > 0) {
            this.selectedResources.forEach((resource, index) => {
                this.pdfValues.push(resource.fields.healthResourcePrintUrl);
                localStorage.setItem('pdfData', JSON.stringify(this.pdfValues));
                localStorage.setItem('prescribe', JSON.stringify(prescribeInfo))


                if (this.selectedResources.length == index + 1) {
                    window.open('/pdfViewer')
                }
            });
        } else {
            this.toast.error("Please select any resource")
        }
    }


    mailPdf(event) {
        let index = this.pdfValues.indexOf(event);
        if (index === -1) {
            return this.pdfValues.push(event);
        } else {
            this.pdfValues.splice(index, 1);
        }
    }

    get result() {
        return this.resourceContents.filter(item => item.fields);
    }


    onSubmit() {
        this.myemailForm.value.body = encodeURI(this.emailContent);
        this.contentfulservice.getMailInfo(this.myemailForm.value).subscribe(res => {
            this.toast.success('Mail Sent')
            this.closeModal();
        })

    }

    openModal() {
        document.getElementById("openbutton").click();
    }

    closeModal() {
        this.closebutton.nativeElement.click();
    }

    public safetyInformation: any;
    public prescribePdf: any;
    getInformation() {
        if (this.resourceres.fields.hasOwnProperty("isi")) {
            this.isi_id = this.resourceres.fields.isi.sys.id;
        } else { console.log("isi not exsits") }
        this.contentfulservice.getdatapreview(this.isi_id).subscribe(res => {
            if (res.fields.hasOwnProperty('shortStickyIsiIndicationsText')) { this.indication_text = res.fields.shortStickyIsiIndicationsText; }
            if (res.fields.hasOwnProperty("safetyInformation")) { this.isi = res.fields.safetyInformation } else { console.log("safetyInformation not exsits") }
            if (res.fields.hasOwnProperty("shortStickyIsi")) { this.shortstickyisi = res.fields.shortStickyIsi } else { console.log("shortStickyIsi not exsits") }
            if (res.fields.hasOwnProperty("prescribingInformationLink")) { this.prescribePdf = res.fields.prescribingInformationLink; } else { console.log("prescribingInformationLink not exsits") }
        })
    }

    public prescribe: any

    getPrescribe() {
        if (this.resourceres.fields.hasOwnProperty("isi")) { this.prescribe_id = this.resourceres.fields.isi.sys.id; } else { console.log("isi not exsits") }
        this.contentfulservice.getdatapreview(this.prescribe_id).subscribe(res => {
            if (res.fields.hasOwnProperty("safetyInformation")) { this.prescribe = res.fields.safetyInformation; } else { console.log("safetyInformation not exsits") }
        })
    }




    getPrescribeFile() {
        if (this.brandresourceres.fields.hasOwnProperty("prescribingInformation")) {
            this.presinfo_id = this.brandresourceres.fields.prescribingInformation.sys.id;
            return this.contentfulservice.getdatapreview(this.presinfo_id).pipe(
                mergeMap(res => {
                    if (res.fields.hasOwnProperty("prescribingInformation")) {
                        let presinfo2 = res.fields.prescribingInformation.sys.id;
                        return this.contentfulservice.getdatapreview(presinfo2).pipe(
                            mergeMap(res => {
                                if (res.fields.hasOwnProperty("prescribingInformationPdf")) {
                                    let assetsid = res.fields.prescribingInformationPdf.sys.id;
                                    return this.contentfulservice.getAssetspreview('/' + assetsid + '/').pipe(
                                        mergeMap(presInfoData => {
                                            if (presInfoData.fields.hasOwnProperty("file")) {
                                                this.getfile = presInfoData.fields.file.url;
                                                return of(true);
                                            } else {
                                                console.log("prescribingInformationPdf not exists");
                                                return of(false);
                                            }
                                        }),
                                        retryWhen(errors =>
                                            errors.pipe(
                                                delayWhen(() => timer(1000)),
                                                mergeMap((error, index) => {
                                                    if (index === 4) {
                                                        return throwError(error);
                                                    }
                                                    return of(error);
                                                }),
                                                take(5)
                                            )
                                        )
                                    );
                                } else {
                                    if (res.fields.hasOwnProperty('prescribingInformationLink')) {
                                        this.getfile = res.fields.prescribingInformationLink;
                                    }
                                    console.log("prescribingInformationPdf not exists");
                                    return of(false);
                                }
                            }),
                            retryWhen(errors =>
                                errors.pipe(
                                    delayWhen(() => timer(1000)),
                                    mergeMap((error, index) => {
                                        if (index === 4) {
                                            return throwError(error);
                                        }
                                        return of(error);
                                    }),
                                    take(5)
                                )
                            )
                        );
                    } else {
                        console.log("prescribingInformation not exists");
                        return of(false);
                    }
                }),
                retryWhen(errors =>
                    errors.pipe(
                        delayWhen(() => timer(1000)),
                        mergeMap((error, index) => {
                            if (index === 4) {
                                return throwError(error);
                            }
                            return of(error);
                        }),
                        take(5)
                    )
                )
            );
        } else {
            console.log("prescribingInformation not exists");
            return of(false);
        }
    }

    getAllDynamicMssg() {
        if (this.resourceres.fields.hasOwnProperty("dynamicSystemMessages")) {
            this.dymssg_id = this.resourceres.fields.dynamicSystemMessages.sys.id;
            this.contentfulservice.getdatapreview(this.dymssg_id).subscribe(res => {
                this.dynamicmssgres = res;
                console.log("dynamicmssgres", this.dynamicmssgres)
                this.getTopHeading();
                this.getTopHeadinglist();
                this.getIcon();
                this.getIcon1();
                this.getIcon2();
                this.getTopDropdown();
                this.getTopListed();
                this.getTopListedDemo();
                this.getTopListed2();
            })
        } else {
            console.log("dynamicSystemMessages not exists")
        }
    }

    linkIcon
    getIcon() {
        this.icon_id = this.dynamicmssgres.fields.messages[2].sys.id;
        this.contentfulservice.getdatapreview(this.icon_id).subscribe(res => {
            let iconid2 = res.fields.image.sys.id;
            this.contentfulservice.getAssetspreview('/' + iconid2 + '/').subscribe(res => {
                this.linkIcon = res.fields.file.url
            })
        })
    }
    copyIcon
    getIcon1() {
        this.icon1_id = this.dynamicmssgres.fields.messages[1].sys.id;
        this.contentfulservice.getdatapreview(this.icon1_id).subscribe(res => {
            let iconid2 = res.fields.image.sys.id;
            this.contentfulservice.getAssetspreview('/' + iconid2 + '/').subscribe(res => {
                this.copyIcon = res.fields.file.url
            })
        })
    }
    searchIcon
    getIcon2() {
        this.icon2_id = this.dynamicmssgres.fields.messages[3].sys.id;
        this.contentfulservice.getdatapreview(this.icon2_id).subscribe(res => {
            let iconid2 = res.fields.image.sys.id;
            this.contentfulservice.getAssetspreview('/' + iconid2 + '/').subscribe(res => {
                this.searchIcon = res.fields.file.url
            })
        })

    }

    getCopyright() {
        if (this.brandresourceres.fields.hasOwnProperty("footer")) {
            this.copyRights_id = this.brandresourceres.fields.footer.sys.id;
            this.contentfulservice.getdatapreview(this.copyRights_id).subscribe(res => {
                this.footerres = res;
                if (res.fields.hasOwnProperty("logoimage")) {
                    let logoimage = res.fields.logoimage[0].sys.id
                    this.contentfulservice.getdatapreview(logoimage).subscribe(res => {
                        let imageWrapper = res.fields.imageWrapper.sys.id
                        this.contentfulservice.getdatapreview(imageWrapper).subscribe(res => {
                            let logoimageurl = res.fields.image.sys.id
                            this.contentfulservice.getassets(logoimageurl).subscribe(res => {
                                this.logoimage = res.fields.file.url;
                            });
                        });
                    });
                } else { console.log("logoimage not exists") }
                if (res.fields.hasOwnProperty("copyright")) {
                    this.copyRights = res.fields.copyright
                } else { console.log("copyrights not exists") }
                this.getCslimage()
            })
        } else { console.log("footer not exists") }

    }

    getCslimage() {
        this.logoimgid = this.footerres.fields.logoimage[0].sys.id;
        this.contentfulservice.getdatapreview(this.logoimgid).subscribe(res => {
            let bid3 = res.fields.imageWrapper.sys.id;
            this.contentfulservice.getdatapreview(bid3).subscribe(res => {
                let bid4 = res.fields.image.sys.id;
                this.contentfulservice.getAssetspreview('/' + bid4 + '/').subscribe(imgData =>
                    this.landFootImg = imgData.fields.file.url
                );
            })
        });
        this.copyrightfooterContentAlignment = this.footerres.fields.footerContentAlignment;
        document.documentElement.style.setProperty('--copyrightfooterContentAlignment', this.copyrightfooterContentAlignment);
        this.footerrestextAlignment = this.footerres.fields.textAlignment;
        document.documentElement.style.setProperty('--footerrestextAlignment', this.footerrestextAlignment);
        this.footerrestextAlignmentMobile = this.footerres.fields.textAlignmentMobile;
        document.documentElement.style.setProperty('--footerrestextAlignmentMobile', this.footerrestextAlignmentMobile);
    }

    public indicationTitle = null;
    getLogoimage() {
        if (this.brandresourceres.fields.hasOwnProperty("branddiseaseHeader")) {
            this.brandheader_id = this.brandresourceres.fields.branddiseaseHeader.sys.id;
            this.contentfulservice.getdatapreview(this.brandheader_id).pipe(
                mergeMap(res => {
                    if (res.fields.hasOwnProperty("logoimage")) {
                        let a = res.fields.logoimage.sys.id;
                        return this.contentfulservice.getdatapreview(a).pipe(
                            mergeMap(res => {
                                if (res.fields.hasOwnProperty("imageWrapper")) {
                                    let b = res.fields.imageWrapper.sys.id;
                                    return this.contentfulservice.getdatapreview(b).pipe(
                                        mergeMap(res => {


                                            this.branddiseaselogoWidth = res.fields.imageWidth;
                                            document.documentElement.style.setProperty('--branddiseaselogoWidth', this.branddiseaselogoWidth);
                                            this.branddiseaselogoHeight = res.fields.imageHeight;
                                            document.documentElement.style.setProperty('--branddiseaselogoHeight', this.branddiseaselogoHeight);
                                            this.branddiseaselogoWidthMobile = res.fields.imageWidthMobile;
                                            document.documentElement.style.setProperty('--branddiseaselogoWidthMobile', this.branddiseaselogoWidthMobile);
                                            this.branddiseaselogoHeightMobile = res.fields.imageHeightMobile;
                                            document.documentElement.style.setProperty('--branddiseaselogoHeightMobile', this.branddiseaselogoHeightMobile);
                                            this.branddiseasepadding = res.fields.padding;
                                            document.documentElement.style.setProperty('--branddiseasepadding', this.branddiseasepadding);
                                            this.branddiseasemargin = res.fields.margin;
                                            document.documentElement.style.setProperty('--branddiseasemargin', this.branddiseasemargin);

                                            if (res.fields.hasOwnProperty("image")) {
                                                let c = res.fields.image.sys.id;
                                                return this.contentfulservice.getAssetspreview('/' + c + '/').pipe(
                                                    mergeMap(imgData => {
                                                        if (imgData.fields.hasOwnProperty("file")) {
                                                            this.logo = imgData.fields.file.url;
                                                            return of(true);
                                                        } else {
                                                            console.log("logo image not exists");
                                                            return of(false);
                                                        }
                                                    }),
                                                    retryWhen(errors =>
                                                        errors.pipe(
                                                            delayWhen(() => timer(1000)),
                                                            mergeMap((error, index) => {
                                                                if (index === 4) {
                                                                    return throwError(error);
                                                                }
                                                                return of(error);
                                                            }),
                                                            take(5)
                                                        )
                                                    )
                                                );
                                            } else {
                                                console.log("image not exists");
                                                return of(false);
                                            }
                                        }),
                                        retryWhen(errors =>
                                            errors.pipe(
                                                delayWhen(() => timer(1000)),
                                                mergeMap((error, index) => {
                                                    if (index === 4) {
                                                        return throwError(error);
                                                    }
                                                    return of(error);
                                                }),
                                                take(5)
                                            )
                                        )
                                    );
                                } else {
                                    console.log("imageWrapper not exists");
                                    return of(false);
                                }
                            }),
                            retryWhen(errors =>
                                errors.pipe(
                                    delayWhen(() => timer(1000)),
                                    mergeMap((error, index) => {
                                        if (index === 4) {
                                            return throwError(error);
                                        }
                                        return of(error);
                                    }),
                                    take(5)
                                )
                            )
                        );
                    } else {
                        console.log("logoimage not exists");
                        return of(false);
                    }
                }),
                retryWhen(errors =>
                    errors.pipe(
                        delayWhen(() => timer(1000)),
                        mergeMap((error, index) => {
                            if (index === 4) {
                                return throwError(error);
                            }
                            return of(error);
                        }),
                        take(5)
                    )
                )
            ).subscribe(res => {
                console.log(res)
                if (res) {
                    if (this.brandresourceres.fields.hasOwnProperty("branddiseaseHeader")) {
                        let a = this.brandresourceres.fields.branddiseaseHeader.sys.id;
                        this.contentfulservice.getdatapreview(a).subscribe(res => {
                            let b = res.fields.headerIsi.sys.id;
                            this.contentfulservice.getdatapreview(b).subscribe(res => {

                                this.p_file1 = this._returnHtmlFromRichText(res.fields.text);
                                this.p_file1Mobile = this._returnHtmlFromRichText(res.fields.textMobile);
                                this.indicationtextContent = this.extractPrescribingInformation(res.fields.text);
                                this.indicationtextContentMobile = this.extractPrescribingInformation(res.fields.textMobile);
                                this.indicationtextContenttext = this.extractPrescribingInformationtext(res.fields.text);
                                this.indicationtextContenttexttextMobile = this.extractPrescribingInformationtext(res.fields.textMobile);
                                this.indicationtextContenttext1 = this.extractPrescribingInformationtext1(res.fields.text);
                                this.indicationtextContenttext1textMobile = this.extractPrescribingInformationtext1(res.fields.textMobile);
                                this.headerIsialignment = res.fields.textAlignment;
                                document.documentElement.style.setProperty('--headerIsialignment', this.headerIsialignment);
                                this.headerIsifontSize = res.fields.fontSize;
                                document.documentElement.style.setProperty('--headerIsifontSize', this.headerIsifontSize);
                                this.fontSizeMobile = res.fields.fontSizeMobile;
                                document.documentElement.style.setProperty('--fontSizeMobile', this.fontSizeMobile);
                                this.headerIsifontColor = res.fields.fontColor.value;
                                document.documentElement.style.setProperty('--headerIsifontColor', this.headerIsifontColor);
                                this.headerIsiwidth = res.fields.width;
                                document.documentElement.style.setProperty('--headerIsiwidth', this.headerIsiwidth);
                                this.headerIsibackgroundColor = res.fields.backgroundColor.value;
                                document.documentElement.style.setProperty('--headerIsibackgroundColor', this.headerIsibackgroundColor);
                                this.textBlockPadding = res.fields.textBlockPadding;
                                document.documentElement.style.setProperty('--textBlockPadding', this.textBlockPadding);
                                this.headerIsilineHeight = res.fields.lineHeight;
                                document.documentElement.style.setProperty('--headerIsilineHeight', this.headerIsilineHeight);
                                this.headerIsimargin = res.fields.textBlockMargin;
                                document.documentElement.style.setProperty('--headerIsimargin', this.headerIsimargin);
                                this.headerIsialignmentMobile = res.fields.textAlignmentMobile;
                                document.documentElement.style.setProperty('--headerIsialignmentMobile', this.headerIsialignmentMobile);
                                this.headerIsifontSizeMobile = res.fields.fontSizeMobile;
                                document.documentElement.style.setProperty('--headerIsifontSizeMobile', this.headerIsifontSizeMobile);
                                this.headerIsilineHeightMobile = res.fields.lineHeightMobile;
                                document.documentElement.style.setProperty('--headerIsilineHeightMobile', this.headerIsilineHeightMobile);
                                this.headerIsimarginMobile = res.fields.textBlockMarginMobile;
                                document.documentElement.style.setProperty('--headerIsimarginMobile', this.headerIsimarginMobile);
                                this.textBlockPaddingMobile = res.fields.textBlockPaddingMobile;
                                document.documentElement.style.setProperty('--textBlockPaddingMobile', this.textBlockPaddingMobile);
                                this.textBlockPadding = res.fields.textBlockPadding;
                                document.documentElement.style.setProperty('--textBlockPadding', this.textBlockPadding);
                                this.textBlockPaddingMobile = res.fields.textBlockPaddingMobile;
                                document.documentElement.style.setProperty('--textBlockPaddingMobile', this.textBlockPaddingMobile);
                            });
                        });
                        this.contentfulservice.getdatapreview(a).subscribe(res => {
                            this.textBoxBelowHeaderIndication = res.fields.textBoxBelowHeaderIndication[0].sys.id;
                            this.contentfulservice.getdatapreview(this.textBoxBelowHeaderIndication).subscribe(res => {
                                console.log("textBoxBelowHeaderIndication11", res);
                                this.textBoxBelowHeaderIndication = documentToHtmlString(res.fields.text);
                                this.textBoxBelowHeaderIndicationMobile = documentToHtmlString(res.fields.textMobile);

                                if (res.fields.hasOwnProperty("textAlignment")) {
                                    this.textBoxBelowHeaderalignment = res.fields.textAlignment;
                                    document.documentElement.style.setProperty('--textBoxBelowHeaderalignment', this.textBoxBelowHeaderalignment);
                                } else { console.log("textAlignment not exists") }

                                if (res.fields.hasOwnProperty("textAlignmentMobile")) {
                                    this.textBoxBelowHeaderalignmentMobile = res.fields.textAlignmentMobile;
                                    document.documentElement.style.setProperty('--textBoxBelowHeaderalignmentMobile', this.textBoxBelowHeaderalignmentMobile);
                                } else { console.log("textAlignmentMobile not exists") }
                                if (res.fields.hasOwnProperty("fontSize")) {
                                    this.textBoxBelowHeaderfontSize = res.fields.fontSize;
                                    document.documentElement.style.setProperty('--textBoxBelowHeaderfontSize', this.textBoxBelowHeaderfontSize);
                                } else { console.log("fontSize not exists") }

                                if (res.fields.hasOwnProperty("lineHeight")) {
                                    this.textBoxBelowHeaderlineHeight = res.fields.lineHeight;
                                    document.documentElement.style.setProperty('--textBoxBelowHeaderlineHeight', this.textBoxBelowHeaderlineHeight);
                                } else { console.log("lineHeight not exists") }

                                if (res.fields.hasOwnProperty("fontSizeMobile")) {
                                    this.textBoxBelowHeaderfontSizemobile = res.fields.fontSizeMobile;
                                    document.documentElement.style.setProperty('--textBoxBelowHeaderfontSizemobile', this.textBoxBelowHeaderfontSizemobile);
                                } else { console.log("fontSizeMobile not exists") }

                                if (res.fields.hasOwnProperty("lineHeightMobile")) {
                                    this.textBoxBelowHeaderlineHeightmobile = res.fields.lineHeightMobile;
                                    document.documentElement.style.setProperty('--textBoxBelowHeaderlineHeightmobile', this.textBoxBelowHeaderlineHeightmobile);
                                } else { console.log("lineHeightMobile not exists") }

                                if (res.fields.hasOwnProperty("fontColor")) {
                                    this.textBoxBelowHeaderfontColor = res.fields.fontColor.value;
                                    document.documentElement.style.setProperty('--textBoxBelowHeaderfontColor', this.textBoxBelowHeaderfontColor);
                                } else { console.log("fontColor not exists") }

                                if (res.fields.hasOwnProperty("linkColor")) {
                                    this.textBoxBelowHeaderlinkColor = res.fields.linkColor.value;
                                    document.documentElement.style.setProperty('--textBoxBelowHeaderlinkColor', this.textBoxBelowHeaderlinkColor);
                                } else { console.log("linkColor not exists") }

                                if (res.fields.hasOwnProperty("textBlockMargin")) {
                                    this.textBlockMargin = res.fields.textBlockMargin;
                                    document.documentElement.style.setProperty('--textBlockMargin', this.textBlockMargin);
                                } else { console.log("textBlockMargin not exists") }

                                if (res.fields.hasOwnProperty("textBlockMarginMobile")) {
                                    this.textBlockMarginMobile = res.fields.textBlockMarginMobile;
                                    document.documentElement.style.setProperty('--textBlockMarginMobile', this.textBlockMarginMobile);
                                } else { console.log("textBlockMarginMobile not exists") }

                                if (res.fields.hasOwnProperty("textBlockPadding")) {
                                    this.textBlockHeaderPadding = res.fields.textBlockPadding;
                                    document.documentElement.style.setProperty('--textBlockHeaderPadding', this.textBlockHeaderPadding);
                                } else { console.log("textBlockPadding not exists") }

                                if (res.fields.hasOwnProperty("textBlockPaddingMobile")) {
                                    this.textBlockHeaderPaddingMobile = res.fields.textBlockPaddingMobile;
                                    document.documentElement.style.setProperty('--textBlockHeaderPaddingMobile', this.textBlockHeaderPaddingMobile);
                                } else { console.log("textBlockPaddingMobile not exists") }
                            });
                            this.logoimg = res.fields.alignment;
                            document.documentElement.style.setProperty('--logoimg', this.logoimg);
                        });
                    } else {
                        console.log("headerIsi not exists");

                    }
                }
            });
        } else {
            console.log("branddiseaseHeader not exists");
        }
    }

    getDynamicmsg() {
        if (this.brandresourceres.fields.hasOwnProperty("dynamicSystemMessages")) {
            this.dynamicMsg2_id = this.brandresourceres.fields.dynamicSystemMessages.sys.id;
            this.contentfulservice.getdatapreview(this.dynamicMsg2_id).subscribe(res => {
                let dynamicMsg2 = res.fields.messages[0].sys.id
                this.contentfulservice.getdatapreview(dynamicMsg2).subscribe(res => {
                    this.dynamicMsg = res.fields.heading;
                })
            })
        } else { console.log("dynamicSystemMessages not exits") }
    }
    getTopHeadingstyle() {
        console.log("this.brandresourceres.fields", this.brandresourceres.fields)
        if (this.brandresourceres.fields.hasOwnProperty("dynamicSystemMessages")) {
            this.dynamicMsg2_id = this.brandresourceres.fields.dynamicSystemMessages.sys.id;
            this.contentfulservice.getdatapreview(this.dynamicMsg2_id).subscribe(res => {

                if (res.fields.hasOwnProperty("backgroundColor")) {
                    this.topheadingbg = res.fields.backgroundColor;
                    document.documentElement.style.setProperty('--topheadingbackgroundColor', this.topheadingbg);
                } else { console.log("backgroundColor not exists") }
            })
        } else { console.log("dynamicSystemMessages not exits") }
    }
    getTopHeadingcontent() {
        console.log("this.brandresourceres.fields", this.brandresourceres.fields.branddiseaseHeader)
        if (this.brandresourceres.fields.hasOwnProperty("branddiseaseHeader")) {
            this.dynamicMsg2_id = this.brandresourceres.fields.branddiseaseHeader.sys.id;
            this.contentfulservice.getdatapreview(this.dynamicMsg2_id).subscribe(res => {
                this.topheadingbg = res.fields.aboveBrandLogo[0].sys.id;
                this.contentfulservice.getdatapreview(this.topheadingbg).subscribe(res => {
                    console.log("this.topheadingbg", res)

                    if (res.fields.hasOwnProperty("backgroundColor")) {
                        this.brandbgcolor = res.fields.backgroundColor.value;
                        document.documentElement.style.setProperty('--brandbgcolor', this.brandbgcolor);
                    } else { console.log("backgroundColor not exists") }

                    if (res.fields.hasOwnProperty("width")) {
                        this.brandcontentwidth = res.fields.width;
                        document.documentElement.style.setProperty('--brandcontentwidth', this.brandcontentwidth);
                    } else { console.log("width not exists") }

                    if (res.fields.hasOwnProperty("columnBlockPadding")) {
                        this.brandcolumnBlockPadding = res.fields.columnBlockPadding;
                        document.documentElement.style.setProperty('--columnBlockPadding', this.brandcolumnBlockPadding);
                    } else { console.log("columnBlockPadding not exists") }

                    if (res.fields.hasOwnProperty("columnBlockPaddingMobile")) {
                        this.columnBlockPaddingMobile = res.fields.columnBlockPaddingMobile;
                        document.documentElement.style.setProperty('--columnBlockPaddingMobile', this.columnBlockPaddingMobile);
                    } else { console.log("columnBlockPaddingMobile not exists") }

                    this.columns1 = res.fields.columns[0].sys.id;
                    this.columns2 = res.fields.columns[1].sys.id;
                    this.contentfulservice.getdatapreview(this.columns1).subscribe(res => {
                        console.log("res colums1", res)

                        if (res.fields.hasOwnProperty("columnWidth")) {
                            this.LeftcolumnWidth = res.fields.columnWidth;
                            document.documentElement.style.setProperty('--LeftcolumnWidth', this.LeftcolumnWidth)
                        } else { console.log("columnWidth not exists") }

                        this.brandheadercolumnContent = res.fields.columnContent[0].sys.id;
                        this.contentfulservice.getdatapreview(this.brandheadercolumnContent).subscribe(res => {
                            this.brandheadertextContent = documentToHtmlString(res.fields.text);

                            let brandheaderimage = res.fields.image.sys.id;
                            this.contentfulservice.getassets(brandheaderimage).subscribe(res => {
                                this.brandheaderimageurl = res.fields.file.url;
                            });

                            if (res.fields.hasOwnProperty("imagePadding")) {
                                this.imagePadding = res.fields.imagePadding;
                                document.documentElement.style.setProperty('--imagePadding', this.imagePadding);
                            } else { console.log("imagePadding not exists") }

                            if (res.fields.hasOwnProperty("imageMargin")) {
                                this.imageMargin = res.fields.imageMargin;
                                document.documentElement.style.setProperty('--imageMargin', this.imageMargin);
                            } else { console.log("imageMargin not exists") }

                            if (res.fields.hasOwnProperty("imageWidth")) {
                                this.brandheaderimageurlWidth = res.fields.imageWidth;
                                document.documentElement.style.setProperty('--brandheaderimageurlWidth', this.brandheaderimageurlWidth);
                            } else { console.log("imageWidth not exists") }

                            if (res.fields.hasOwnProperty("imageHeight")) {
                                this.brandheaderimageurlHeight = res.fields.imageHeight;
                                document.documentElement.style.setProperty('--brandheaderimageurlHeight', this.brandheaderimageurlHeight);
                            } else { console.log("imageHeight not exists") }

                            if (res.fields.hasOwnProperty("imageWidthMobile")) {
                                this.brandheaderimageurlWidthMobile = res.fields.imageWidthMobile;
                                document.documentElement.style.setProperty('--brandheaderimageurlWidthMobile', this.brandheaderimageurlWidthMobile);
                            } else { console.log("imageWidthMobile not exists") }

                            if (res.fields.hasOwnProperty("imageHeightMobile")) {
                                this.brandheaderimageurlHeightMobile = res.fields.imageHeightMobile;
                                document.documentElement.style.setProperty('--brandheaderimageurlHeightMobile', this.brandheaderimageurlHeightMobile);
                            } else { console.log("imageHeightMobile not exists") }

                            if (res.fields.hasOwnProperty("textFontSize")) {
                                this.brandheaderfontsize = res.fields.textFontSize;
                                document.documentElement.style.setProperty('--brandheaderfontsize', this.brandheaderfontsize);
                            } else { console.log("textFontSize not exists") }

                            if (res.fields.hasOwnProperty("textAlignment")) {
                                this.brandheadertextAlignment = res.fields.textAlignment;
                                document.documentElement.style.setProperty('--brandheadertextAlignment', this.brandheadertextAlignment);
                            } else { console.log("textAlignment not exists") }

                            if (res.fields.hasOwnProperty("textFontColor")) {
                                this.textFontColor = res.fields.textFontColor.value;
                                document.documentElement.style.setProperty('--textFontColor', this.textFontColor);
                            } else { console.log("textFontColor not exists") }

                            if (res.fields.hasOwnProperty("linkFontColor")) {
                                this.linkFontColor = res.fields.linkFontColor.value;
                                document.documentElement.style.setProperty('--linkFontColor', this.linkFontColor);
                            } else { console.log("linkFontColor not exists") }

                            if (res.fields.hasOwnProperty("textFontSizeMobile")) {
                                this.brandheaderfontsizeMobile = res.fields.textFontSizeMobile;
                                document.documentElement.style.setProperty('--brandheaderfontsizeMobile', this.brandheaderfontsizeMobile);
                            } else { console.log("textFontSizeMobile not exists") }
                        });
                    });
                    this.contentfulservice.getdatapreview(this.columns2).subscribe(res => {
                        this.brandheadercolumnContent2 = res.fields.columnContent[0].sys.id;

                        if (res.fields.hasOwnProperty("columnWidth")) {
                            this.rightcolumnWidth = res.fields.columnWidth;
                            document.documentElement.style.setProperty('--rightcolumnWidth', this.rightcolumnWidth)
                        } else { console.log("columnWidth not exists") }

                        this.contentfulservice.getdatapreview(this.brandheadercolumnContent2).subscribe(res => {
                            this.brandheadertextContent2 = documentToHtmlString(res.fields.text);
                            let brandheaderimage2 = res.fields.image.sys.id
                            console.log("brandeader", brandheaderimage2)
                            this.contentfulservice.getassets(brandheaderimage2).subscribe(res => {
                                console.log('res1', res)
                                this.brandheaderimageurl2 = res.fields.file.url;
                            });

                            if (res.fields.hasOwnProperty("imagePadding")) {
                                this.imagePadding2 = res.fields.imagePadding;
                                document.documentElement.style.setProperty('--imagePadding2', this.imagePadding2);
                            } else { console.log("imagePadding not exists") }

                            if (res.fields.hasOwnProperty("imageMargin")) {
                                this.imageMargin2 = res.fields.imageMargin;
                                document.documentElement.style.setProperty('--imageMargin2', this.imageMargin2);
                            } else { console.log("imageMargin not exists") }

                            if (res.fields.hasOwnProperty("imageWidth")) {
                                this.brandheaderimageurl2Width = res.fields.imageWidth;
                                document.documentElement.style.setProperty('--brandheaderimageurl2Width', this.brandheaderimageurl2Width);
                            } else { console.log("imageWidth not exists") }

                            if (res.fields.hasOwnProperty("imageHeight")) {
                                this.brandheaderimageurl2Height = res.fields.imageHeight;
                                document.documentElement.style.setProperty('--brandheaderimageurl2Height', this.brandheaderimageurl2Height);
                            } else { console.log("imageHeight not exists") }

                            if (res.fields.hasOwnProperty("imageWidthMobile")) {
                                this.brandheaderimageurl2WidthMobile = res.fields.imageWidthMobile;
                                document.documentElement.style.setProperty('--brandheaderimageurl2WidthMobile', this.brandheaderimageurl2WidthMobile);
                            } else { console.log("imageWidthMobile not exists") }

                            if (res.fields.hasOwnProperty("imageHeightMobile")) {
                                this.brandheaderimageurl2HeightMobile = res.fields.imageHeightMobile;
                                document.documentElement.style.setProperty('--brandheaderimageurl2HeightMobile', this.brandheaderimageurl2HeightMobile);
                            } else { console.log("imageHeightMobile not exists") }

                            if (res.fields.hasOwnProperty("textFontSize")) {
                                this.brandheaderfontsize2 = res.fields.textFontSize;
                                document.documentElement.style.setProperty('--brandheaderfontsize2', this.brandheaderfontsize2);
                            } else { console.log("textFontSize not exists") }

                            if (res.fields.hasOwnProperty("textAlignment")) {
                                this.brandheadertextAlignment2 = res.fields.textAlignment;
                                document.documentElement.style.setProperty('--brandheadertextAlignment2', this.brandheadertextAlignment2);
                            } else { console.log("textAlignment not exists") }

                            if (res.fields.hasOwnProperty("textFontColor")) {
                                this.textFontColor2 = res.fields.textFontColor.value;
                                document.documentElement.style.setProperty('--textFontColor2', this.textFontColor2);
                            } else { console.log("textFontColor not exists") }

                            if (res.fields.hasOwnProperty("textFontSizeMobile")) {
                                this.brandheaderfontsizeMobile2 = res.fields.textFontSizeMobile;
                                document.documentElement.style.setProperty('--brandheaderfontsizeMobile2', this.brandheaderfontsizeMobile2);
                            } else { console.log("textFontSizeMobile not exists") }
                        });

                        if (res.fields.hasOwnProperty("dropdownMenu")) {
                            this.brandheaderdropmenu2 = res.fields.dropdownMenu.sys.id;
                            this.contentfulservice.getdatapreview(this.brandheaderdropmenu2).subscribe(res => {
                                this.brandnavigationLink = res.fields.dropdownLinks[0].sys.id;
                                this.contentfulservice.getdatapreview(this.brandnavigationLink).subscribe(res => {
                                    this.branddropdownLinks = res.fields.navigationLink;
                                    this.branddropdowntext = res.fields.navigationText;
                                });

                                if (res.fields.hasOwnProperty("dropdownBackgroundColor")) {
                                    this.dropdownBackgroundColor = res.fields.dropdownBackgroundColor.value;
                                    document.documentElement.style.setProperty('--dropdownBackgroundColor', this.dropdownBackgroundColor);
                                } else { console.log("dropdownBackgroundColor not exists") }

                                if (res.fields.hasOwnProperty("dropdownLinkRolloverBackgroundColor")) {
                                    this.dropdownLinkRolloverBackgroundColor = res.fields.dropdownLinkRolloverBackgroundColor.value;
                                    document.documentElement.style.setProperty('--dropdownLinkRolloverBackgroundColor', this.dropdownLinkRolloverBackgroundColor);
                                } else { console.log("dropdownLinkRolloverBackgroundColor not exists") }

                                if (res.fields.hasOwnProperty("dropdownFontColor")) {
                                    this.dropdownFontColor = res.fields.dropdownFontColor.value;
                                    document.documentElement.style.setProperty('--dropdownFontColor', this.dropdownFontColor);
                                } else { console.log("dropdownFontColor not exists") }
                            });
                        } else { console.log("dropdownMenu not exists") }
                    });
                })
            })
        } else { console.log("branddiseaseHeader not exits") }
    }

    getstickyIsi() {
        if (this.brandresourceres.fields.hasOwnProperty("stickyIsi")) {

            let b = this.brandresourceres.fields.stickyIsi.sys.id
            this.contentfulservice.getdatapreview(b).subscribe(res => {
                console.log("stickyIsi2", res);
                if (res.fields.hasOwnProperty("mainIsiHeader")) {
                    this.mainIsiHeader = res.fields.mainIsiHeader;
                } else { console.log("mainIsiHeader not exists") }
                if (res.fields.hasOwnProperty("mainIsiContent")) {
                    res.fields.mainIsiContent.forEach((block, index) => {

                        if (index === 0) {
                            console.log("mainIsiContent", res.fields.mainIsiContent);
                            let mainIsiContent = res.fields.mainIsiContent[0].sys.id
                            this.contentfulservice.getdatapreview(mainIsiContent).subscribe(res => {
                                this.mainIsiContentheading = this._returnHtmlFromRichText(res.fields.text);
                                this.mainIsiContentheadingMobile = this._returnHtmlFromRichText(res.fields.textMobile);

                                if (res.fields.hasOwnProperty("textAlignment")) {
                                    this.mainIsiContenttextAlignment = res.fields.textAlignment
                                    document.documentElement.style.setProperty('--mainIsiContenttextAlignment', this.mainIsiContenttextAlignment);
                                } else { console.log("textAlignment not exists") }

                                if (res.fields.hasOwnProperty("fontSize")) {
                                    this.mainIsiContentfontSize = res.fields.fontSize
                                    document.documentElement.style.setProperty('--mainIsiContentfontSize', this.mainIsiContentfontSize);
                                } else { console.log("fontSize not exists") }

                                if (res.fields.hasOwnProperty("lineHeight")) {
                                    this.mainIsiContentlineHeight = res.fields.lineHeight
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeight', this.mainIsiContentlineHeight);
                                } else { console.log("lineHeight not exists") }

                                if (res.fields.hasOwnProperty("fontSizeMobile")) {
                                    this.mainIsiContentfontSizeMobile = res.fields.fontSizeMobile
                                    document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile', this.mainIsiContentfontSizeMobile);
                                } else { console.log("fontSizeMobile not exists") }

                                if (res.fields.hasOwnProperty("lineHeightMobile")) {
                                    this.mainIsiContentlineHeightMobile = res.fields.lineHeightMobile
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile', this.mainIsiContentlineHeightMobile);
                                } else { console.log("lineHeightMobile not exists") }

                                if (res.fields.hasOwnProperty("fontColor")) {
                                    this.mainIsiContentfontColor = res.fields.fontColor.value
                                    document.documentElement.style.setProperty('--mainIsiContentfontColor', this.mainIsiContentfontColor);
                                } else { console.log("fontColor not exists") }

                                if (res.fields.hasOwnProperty("textBlockPadding")) {
                                    this.mainIsiContenttextBlockPadding = res.fields.textBlockPadding
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding', this.mainIsiContenttextBlockPadding);
                                } else { console.log("textBlockPadding not exists") }

                                if (res.fields.hasOwnProperty("textBlockPaddingMobile")) {
                                    this.mainIsiContenttextBlockPaddingMobile = res.fields.textBlockPaddingMobile
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockPaddingMobile', this.mainIsiContenttextBlockPaddingMobile);
                                } else { console.log("textBlockPaddingMobile not exists") }

                                if (res.fields.hasOwnProperty("textBlockMargin")) {
                                    this.mainIsiContenttextBlockMargin = res.fields.textBlockMargin
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin', this.mainIsiContenttextBlockMargin);
                                } else { console.log("textBlockMargin not exists") }

                                if (res.fields.hasOwnProperty("textBlockMarginMobile")) {
                                    this.mainIsiContenttextBlockMarginMobile = res.fields.textBlockMarginMobile
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockMarginMobile', this.mainIsiContenttextBlockMarginMobile);
                                } else { console.log("textBlockMarginMobile not exists") }

                                if (res.fields.hasOwnProperty("width")) {
                                    this.mainIsiContentwidth = res.fields.width
                                    document.documentElement.style.setProperty('--mainIsiContentwidth', this.mainIsiContentwidth);
                                } else { console.log("width not exists") }
                            });
                        } else if (index === 1) {
                            let mainIsiContent1 = res.fields.mainIsiContent[1].sys.id
                            this.contentfulservice.getdatapreview(mainIsiContent1).subscribe(res => {
                                this.mainIsiContenttext = this._returnHtmlFromRichText(res.fields.text);

                                if (res.fields.hasOwnProperty("textAlignment")) {
                                    this.mainIsiContenttextAlignment1 = res.fields.textAlignment
                                    document.documentElement.style.setProperty('--mainIsiContenttextAlignment1', this.mainIsiContenttextAlignment1);
                                } else { console.log("textAlignment not exists") }

                                if (res.fields.hasOwnProperty("fontSize")) {
                                    this.mainIsiContentfontSize1 = res.fields.fontSize
                                    document.documentElement.style.setProperty('--mainIsiContentfontSize1', this.mainIsiContentfontSize1);
                                } else { console.log("fontSize not exists") }

                                if (res.fields.hasOwnProperty("lineHeight")) {
                                    this.mainIsiContentlineHeight1 = res.fields.lineHeight
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeight1', this.mainIsiContentlineHeight1);
                                } else { console.log("lineHeight not exists") }

                                if (res.fields.hasOwnProperty("fontSizeMobile")) {
                                    this.mainIsiContentfontSizeMobile1 = res.fields.fontSizeMobile
                                    document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile1', this.mainIsiContentfontSizeMobile1);
                                } else { console.log("fontSizeMobile not exists") }

                                if (res.fields.hasOwnProperty("lineHeightMobile")) {
                                    this.mainIsiContentlineHeightMobile1 = res.fields.lineHeightMobile
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile1', this.mainIsiContentlineHeightMobile1);
                                } else { console.log("lineHeightMobile not exists") }

                                if (res.fields.hasOwnProperty("fontColor")) {
                                    this.mainIsiContentfontColor1 = res.fields.fontColor.value
                                    document.documentElement.style.setProperty('--mainIsiContentfontColor1', this.mainIsiContentfontColor1);
                                } else { console.log("fontColor not exists") }

                                if (res.fields.hasOwnProperty("textBlockPadding")) {
                                    this.mainIsiContenttextBlockPadding1 = res.fields.textBlockPadding
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding1', this.mainIsiContenttextBlockPadding1);
                                } else { console.log("textBlockPadding not exists") }

                                if (res.fields.hasOwnProperty("textBlockMargin")) {
                                    this.mainIsiContenttextBlockMargin1 = res.fields.textBlockMargin
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin1', this.mainIsiContenttextBlockMargin1);
                                } else { console.log("textBlockMargin not exists") }

                                if (res.fields.hasOwnProperty("width")) {
                                    this.mainIsiContentwidth1 = res.fields.width
                                    document.documentElement.style.setProperty('--mainIsiContentwidth1', this.mainIsiContentwidth1);
                                } else { console.log("width not exists") }
                            });
                        } else if (index === 2) {
                            let mainIsiContent2 = res.fields.mainIsiContent[2].sys.id
                            this.contentfulservice.getdatapreview(mainIsiContent2).subscribe(res => {
                                this.mainIsiContentheading1 = this._returnHtmlFromRichText(res.fields.text);

                                if (res.fields.hasOwnProperty("textAlignment")) {
                                    this.mainIsiContenttextAlignment2 = res.fields.textAlignment
                                    document.documentElement.style.setProperty('--mainIsiContenttextAlignment2', this.mainIsiContenttextAlignment2);
                                } else { console.log("textAlignment not exists") }

                                if (res.fields.hasOwnProperty("fontSize")) {
                                    this.mainIsiContentfontSize2 = res.fields.fontSize
                                    document.documentElement.style.setProperty('--mainIsiContentfontSize2', this.mainIsiContentfontSize2);
                                } else { console.log("fontSize not exists") }

                                if (res.fields.hasOwnProperty("lineHeight")) {
                                    this.mainIsiContentlineHeight2 = res.fields.lineHeight
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeight2', this.mainIsiContentlineHeight2);
                                } else { console.log("lineHeight not exists") }

                                if (res.fields.hasOwnProperty("fontSizeMobile")) {
                                    this.mainIsiContentfontSizeMobile2 = res.fields.fontSizeMobile
                                    document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile2', this.mainIsiContentfontSizeMobile2);
                                } else { console.log("fontSizeMobile not exists") }

                                if (res.fields.hasOwnProperty("lineHeightMobile")) {
                                    this.mainIsiContentlineHeightMobile2 = res.fields.lineHeightMobile
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile2', this.mainIsiContentlineHeightMobile2);
                                } else { console.log("lineHeightMobile not exists") }

                                if (res.fields.hasOwnProperty("fontColor")) {
                                    this.mainIsiContentfontColor2 = res.fields.fontColor.value
                                    document.documentElement.style.setProperty('--mainIsiContentfontColor2', this.mainIsiContentfontColor2);
                                } else { console.log("fontColor not exists") }

                                if (res.fields.hasOwnProperty("textBlockPadding")) {
                                    this.mainIsiContenttextBlockPadding2 = res.fields.textBlockPadding
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding2', this.mainIsiContenttextBlockPadding2);
                                } else { console.log("textBlockPadding not exists") }

                                if (res.fields.hasOwnProperty("textBlockMargin")) {
                                    this.mainIsiContenttextBlockMargin2 = res.fields.textBlockMargin
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin2', this.mainIsiContenttextBlockMargin2);
                                } else { console.log("textBlockMargin not exists") }

                                if (res.fields.hasOwnProperty("width")) {
                                    this.mainIsiContentwidth2 = res.fields.width
                                    document.documentElement.style.setProperty('--mainIsiContentwidth2', this.mainIsiContentwidth2);
                                } else { console.log("width not exists") }
                            });
                        } else if (index === 3) {
                            let mainIsiContent3 = res.fields.mainIsiContent[3].sys.id
                            this.contentfulservice.getdatapreview(mainIsiContent3).subscribe(res => {
                                this.mainIsiContenttext1 = this._returnHtmlFromRichText(res.fields.text);

                                if (res.fields.hasOwnProperty("textAlignment")) {
                                    this.mainIsiContenttextAlignment3 = res.fields.textAlignment
                                    document.documentElement.style.setProperty('--mainIsiContenttextAlignment3', this.mainIsiContenttextAlignment3);
                                } else { console.log("textAlignment not exists") }

                                if (res.fields.hasOwnProperty("fontSize")) {
                                    this.mainIsiContentfontSize3 = res.fields.fontSize
                                    document.documentElement.style.setProperty('--mainIsiContentfontSize3', this.mainIsiContentfontSize3);
                                } else { console.log("fontSize not exists") }

                                if (res.fields.hasOwnProperty("lineHeight")) {
                                    this.mainIsiContentlineHeight3 = res.fields.lineHeight
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeight3', this.mainIsiContentlineHeight3);
                                } else { console.log("lineHeight not exists") }

                                if (res.fields.hasOwnProperty("fontSizeMobile")) {
                                    this.mainIsiContentfontSizeMobile3 = res.fields.fontSizeMobile
                                    document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile3', this.mainIsiContentfontSizeMobile3);
                                } else { console.log("fontSizeMobile not exists") }

                                if (res.fields.hasOwnProperty("lineHeightMobile")) {
                                    this.mainIsiContentlineHeightMobile3 = res.fields.lineHeightMobile
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile3', this.mainIsiContentlineHeightMobile3);
                                } else { console.log("lineHeightMobile not exists") }

                                if (res.fields.hasOwnProperty("fontColor")) {
                                    this.mainIsiContentfontColor3 = res.fields.fontColor.value
                                    document.documentElement.style.setProperty('--mainIsiContentfontColor3', this.mainIsiContentfontColor3);
                                } else { console.log("fontColor not exists") }

                                if (res.fields.hasOwnProperty("textBlockPadding")) {
                                    this.mainIsiContenttextBlockPadding3 = res.fields.textBlockPadding
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding3', this.mainIsiContenttextBlockPadding3);
                                } else { console.log("textBlockPadding not exists") }

                                if (res.fields.hasOwnProperty("textBlockMargin")) {
                                    this.mainIsiContenttextBlockMargin3 = res.fields.textBlockMargin
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin3', this.mainIsiContenttextBlockMargin3);
                                } else { console.log("textBlockMargin not exists") }

                                if (res.fields.hasOwnProperty("width")) {
                                    this.mainIsiContentwidth3 = res.fields.width
                                    document.documentElement.style.setProperty('--mainIsiContentwidth3', this.mainIsiContentwidth3);
                                } else { console.log("width not exists") }
                            });
                        } else if (index === 4) {
                            let mainIsiContent4 = res.fields.mainIsiContent[4].sys.id
                            this.contentfulservice.getdatapreview(mainIsiContent4).subscribe(res => {
                                this.mainIsiContentheading2 = this._returnHtmlFromRichText(res.fields.text);

                                if (res.fields.hasOwnProperty("textAlignment")) {
                                    this.mainIsiContenttextAlignment4 = res.fields.textAlignment
                                    document.documentElement.style.setProperty('--mainIsiContenttextAlignment4', this.mainIsiContenttextAlignment4);
                                } else { console.log("textAlignment not exists") }

                                if (res.fields.hasOwnProperty("fontSize")) {
                                    this.mainIsiContentfontSize4 = res.fields.fontSize
                                    document.documentElement.style.setProperty('--mainIsiContentfontSize4', this.mainIsiContentfontSize4);
                                } else { console.log("fontSize not exists") }

                                if (res.fields.hasOwnProperty("lineHeight")) {
                                    this.mainIsiContentlineHeight4 = res.fields.lineHeight
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeight4', this.mainIsiContentlineHeight4);
                                } else { console.log("lineHeight not exists") }

                                if (res.fields.hasOwnProperty("fontSizeMobile")) {
                                    this.mainIsiContentfontSizeMobile4 = res.fields.fontSizeMobile
                                    document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile4', this.mainIsiContentfontSizeMobile4);
                                } else { console.log("fontSizeMobile not exists") }

                                if (res.fields.hasOwnProperty("lineHeightMobile")) {
                                    this.mainIsiContentlineHeightMobile4 = res.fields.lineHeightMobile
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile4', this.mainIsiContentlineHeightMobile4);
                                } else { console.log("lineHeightMobile not exists") }

                                if (res.fields.hasOwnProperty("fontColor")) {
                                    this.mainIsiContentfontColor4 = res.fields.fontColor.value
                                    document.documentElement.style.setProperty('--mainIsiContentfontColor4', this.mainIsiContentfontColor4);
                                } else { console.log("fontColor not exists") }

                                if (res.fields.hasOwnProperty("textBlockPadding")) {
                                    this.mainIsiContenttextBlockPadding4 = res.fields.textBlockPadding
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding4', this.mainIsiContenttextBlockPadding4);
                                } else { console.log("textBlockPadding not exists") }

                                if (res.fields.hasOwnProperty("textBlockMargin")) {
                                    this.mainIsiContenttextBlockMargin4 = res.fields.textBlockMargin
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin4', this.mainIsiContenttextBlockMargin4);
                                } else { console.log("textBlockMargin not exists") }

                                if (res.fields.hasOwnProperty("width")) {
                                    this.mainIsiContentwidth4 = res.fields.width
                                    document.documentElement.style.setProperty('--mainIsiContentwidth4', this.mainIsiContentwidth4);
                                } else { console.log("width not exists") }
                            });
                        } else if (index === 5) {
                            let mainIsiContent5 = res.fields.mainIsiContent[5].sys.id
                            this.contentfulservice.getdatapreview(mainIsiContent5).subscribe(res => {
                                this.mainIsiContenttext2 = this._returnHtmlFromRichText(res.fields.text);

                                if (res.fields.hasOwnProperty("textAlignment")) {
                                    this.mainIsiContenttextAlignment5 = res.fields.textAlignment
                                    document.documentElement.style.setProperty('--mainIsiContenttextAlignment5', this.mainIsiContenttextAlignment5);
                                } else { console.log("textAlignment not exists") }

                                if (res.fields.hasOwnProperty("fontSize")) {
                                    this.mainIsiContentfontSize5 = res.fields.fontSize
                                    document.documentElement.style.setProperty('--mainIsiContentfontSize5', this.mainIsiContentfontSize5);
                                } else { console.log("fontSize not exists") }

                                if (res.fields.hasOwnProperty("lineHeight")) {
                                    this.mainIsiContentlineHeight5 = res.fields.lineHeight
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeight5', this.mainIsiContentlineHeight5);
                                } else { console.log("lineHeight not exists") }

                                if (res.fields.hasOwnProperty("fontSizeMobile")) {
                                    this.mainIsiContentfontSizeMobile5 = res.fields.fontSizeMobile
                                    document.documentElement.style.setProperty('--mainIsiContentfontSizeMobile5', this.mainIsiContentfontSizeMobile5);
                                } else { console.log("fontSizeMobile not exists") }

                                if (res.fields.hasOwnProperty("lineHeightMobile")) {
                                    this.mainIsiContentlineHeightMobile5 = res.fields.lineHeightMobile
                                    document.documentElement.style.setProperty('--mainIsiContentlineHeightMobile5', this.mainIsiContentlineHeightMobile5);
                                } else { console.log("lineHeightMobile not exists") }

                                if (res.fields.hasOwnProperty("fontColor")) {
                                    this.mainIsiContentfontColor5 = res.fields.fontColor.value
                                    document.documentElement.style.setProperty('--mainIsiContentfontColor5', this.mainIsiContentfontColor5);
                                } else { console.log("fontColor not exists") }

                                if (res.fields.hasOwnProperty("textBlockPadding")) {
                                    this.mainIsiContenttextBlockPadding5 = res.fields.textBlockPadding
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockPadding5', this.mainIsiContenttextBlockPadding5);
                                } else { console.log("textBlockPadding not exists") }

                                if (res.fields.hasOwnProperty("textBlockMargin")) {
                                    this.mainIsiContenttextBlockMargin5 = res.fields.textBlockMargin
                                    document.documentElement.style.setProperty('--mainIsiContenttextBlockMargin5', this.mainIsiContenttextBlockMargin5);
                                } else { console.log("textBlockMargin not exists") }

                                if (res.fields.hasOwnProperty("width")) {
                                    this.mainIsiContentwidth5 = res.fields.width
                                    document.documentElement.style.setProperty('--mainIsiContentwidth5', this.mainIsiContentwidth5);
                                } else { console.log("width not exists") }
                            });
                        }
                    });
                } else { console.log("shortStickyIsiImportantSafetyInformationText not exists") }

                if (res.fields.hasOwnProperty("mainIsiMargin")) {
                    this.mainIsiMargin = res.fields.mainIsiMargin
                    document.documentElement.style.setProperty('--mainIsiMargin', this.mainIsiMargin);
                } else { console.log("mainIsiMargin not exists") }

                if (res.fields.hasOwnProperty("mainIsiPadding")) {
                    this.mainIsiPadding = res.fields.mainIsiPadding
                    document.documentElement.style.setProperty('--mainIsiPadding', this.mainIsiPadding);
                } else { console.log("mainIsiPadding not exists") }

                if (res.fields.hasOwnProperty("mainIsiHeaderFontSize")) {
                    this.mainIsiHeaderFontSize = res.fields.mainIsiHeaderFontSize
                    document.documentElement.style.setProperty('--mainIsiHeaderFontSize', this.mainIsiHeaderFontSize);
                } else { console.log("mainIsiHeaderFontSize not exists") }

                if (res.fields.hasOwnProperty("mainIsiHeaderFontSizeMobile")) {
                    this.mainIsiHeaderFontSizeMobile = res.fields.mainIsiHeaderFontSizeMobile
                    document.documentElement.style.setProperty('--mainIsiHeaderFontSizeMobile', this.mainIsiHeaderFontSizeMobile);
                } else { console.log("mainIsiHeaderFontSizeMobile not exists") }

                if (res.fields.hasOwnProperty("mainIsiHeaderFontWeight")) {
                    this.mainIsiHeaderFontWeight = res.fields.mainIsiHeaderFontWeight
                    document.documentElement.style.setProperty('--mainIsiHeaderFontWeight', this.mainIsiHeaderFontWeight);
                } else { console.log("mainIsiHeaderFontWeight not exists") }

                if (res.fields.hasOwnProperty("mainIsiHeaderFontColor")) {
                    this.mainIsiHeaderFontColor = res.fields.mainIsiHeaderFontColor.value
                    document.documentElement.style.setProperty('--mainIsiHeaderFontColor', this.mainIsiHeaderFontColor);
                } else { console.log("mainIsiHeaderFontColor not exists") }
            });

        }
    }

    DynamicMessageList() {
        if (this.brandresourceres.fields.hasOwnProperty("dynamicSystemMessages")) {
            this.dynamicMsg2_id = this.brandresourceres.fields.dynamicSystemMessages.sys.id;
            this.contentfulservice.getdatapreview(this.dynamicMsg2_id).subscribe(res => {
                // massages
                this.DynamicMessage = res.fields.messages[0].sys.id;
                this.contentfulservice.getdatapreview(this.DynamicMessage).subscribe(res => {
                    this.DynamicMessagecontent = documentToHtmlString(res.fields.copy);

                    if (res.fields.hasOwnProperty("copyFont")) {
                        this.copyFont = res.fields.copyFont;
                        document.documentElement.style.setProperty('--copyFont', this.copyFont);
                    } else { console.log("copyFont not exists") }

                    if (res.fields.hasOwnProperty("copyFontColor")) {
                        this.copyFontColor = res.fields.copyFontColor.value;
                        document.documentElement.style.setProperty('--copyFontColor', this.copyFontColor);
                    } else { console.log("copyFontColor not exists") }

                    if (res.fields.hasOwnProperty("copyFontSize")) {
                        this.copyFontSize = res.fields.copyFontSize;
                        document.documentElement.style.setProperty('--copyFontSize', this.copyFontSize);
                    } else { console.log("copyFontSize not exists") }

                    if (res.fields.hasOwnProperty("copyFontSizeMobile")) {
                        this.copyFontSizeMobile = res.fields.copyFontSizeMobile;
                        document.documentElement.style.setProperty('--copyFontSizeMobile', this.copyFontSizeMobile);
                    } else { console.log("copyFontSizeMobile not exists") }

                    if (res.fields.hasOwnProperty("copyTextAlign")) {
                        this.copyTextAlign = res.fields.copyTextAlign;
                        document.documentElement.style.setProperty('--copyTextAlign', this.copyTextAlign);
                    } else { console.log("copyTextAlign not exists") }

                    if (res.fields.hasOwnProperty("copyLineHeight")) {
                        this.copyLineHeight = res.fields.copyLineHeight;
                        document.documentElement.style.setProperty('--copyLineHeight', this.copyLineHeight);
                    } else { console.log("copyLineHeight not exists") }

                    if (res.fields.hasOwnProperty("copyPadding")) {
                        this.copyPadding = res.fields.copyPadding;
                        document.documentElement.style.setProperty('--copyPadding', this.copyPadding);
                    } else { console.log("copyPadding not exists") }

                    if (res.fields.hasOwnProperty("copyMargin")) {
                        this.copyMargin = res.fields.copyMargin;
                        document.documentElement.style.setProperty('--copyMargin', this.copyMargin);
                    } else { console.log("copyMargin not exists") }

                    console.log("copyPadding,copyMargin ", this.copyPadding, this.copyMargin)
                });
                this.DynamicMessage1 = res.fields.messages[1].sys.id;
                this.contentfulservice.getdatapreview(this.DynamicMessage1).subscribe(res => {
                    console.log("DynamicMessageList[1]", res);
                    this.DynamicMessagecontent1 = documentToHtmlString(res.fields.copy);

                    if (res.fields.hasOwnProperty("copyFont")) {
                        this.copyFont1 = res.fields.copyFont;
                        document.documentElement.style.setProperty('--copyFont1', this.copyFont1);
                    } else { console.log("copyFont not exists") }

                    if (res.fields.hasOwnProperty("copyFontColor")) {
                        this.copyFontColor1 = res.fields.copyFontColor.value;
                        document.documentElement.style.setProperty('--copyFontColor1', this.copyFontColor1);
                    } else { console.log("copyFontColor not exists") }

                    if (res.fields.hasOwnProperty("copyFontSize")) {
                        this.copyFontSize1 = res.fields.copyFontSize;
                        document.documentElement.style.setProperty('--copyFontSize1', this.copyFontSize1);
                    } else { console.log("copyFontSize not exists") }

                    if (res.fields.hasOwnProperty("copyFontSizeMobile")) {
                        this.copyFontSizeMobile1 = res.fields.copyFontSizeMobile;
                        document.documentElement.style.setProperty('--copyFontSizeMobile1', this.copyFontSizeMobile1);
                    } else { console.log("copyFontSizeMobile not exists") }

                    if (res.fields.hasOwnProperty("copyTextAlign")) {
                        this.copyTextAlign1 = res.fields.copyTextAlign;
                        document.documentElement.style.setProperty('--copyTextAlign1', this.copyTextAlign1);
                    } else { console.log("copyTextAlign not exists") }

                    if (res.fields.hasOwnProperty("copyLineHeight")) {
                        this.copyLineHeight1 = res.fields.copyLineHeight;
                        document.documentElement.style.setProperty('--copyLineHeight1', this.copyLineHeight1);
                    } else { console.log("copyLineHeight not exists") }

                    if (res.fields.hasOwnProperty("copyPadding")) {
                        this.copyPadding1 = res.fields.copyPadding;
                        document.documentElement.style.setProperty('--copyPadding1', this.copyPadding1);
                    } else { console.log("copyPadding not exists") }

                    if (res.fields.hasOwnProperty("copyMargin")) {
                        this.copyMargin1 = res.fields.copyMargin;
                        document.documentElement.style.setProperty('--copyMargin1', this.copyMargin1);
                    } else { console.log("copyMargin not exists") }

                    if (res.fields.hasOwnProperty("copyTextAlignMobile")) {
                        this.copyTextAlignMobile1 = res.fields.copyTextAlignMobile;
                        document.documentElement.style.setProperty('--copyTextAlignMobile1', this.copyTextAlignMobile1);
                    } else { console.log("copyTextAlignMobile not exists") }

                    if (res.fields.hasOwnProperty("copyLineHeightMobile")) {
                        this.copyLineHeightMobile1 = res.fields.copyLineHeightMobile;
                        document.documentElement.style.setProperty('--copyLineHeightMobile1', this.copyLineHeightMobile1);
                    } else { console.log("copyLineHeightMobile not exists") }

                    if (res.fields.hasOwnProperty("copyPaddingMobile")) {
                        this.copyPaddingMobile1 = res.fields.copyPaddingMobile;
                        document.documentElement.style.setProperty('--copyPaddingMobile1', this.copyPaddingMobile1);
                    } else { console.log("copyPaddingMobile not exists") }

                    if (res.fields.hasOwnProperty("copyMarginMobile")) {
                        this.copyMarginMobile1 = res.fields.copyMarginMobile;
                        document.documentElement.style.setProperty('--copyMarginMobile1', this.copyMarginMobile1);
                    } else { console.log("copyMarginMobile not exists") }
                });
                this.DynamicMessage2 = res.fields.messages[2].sys.id;
                this.contentfulservice.getdatapreview(this.DynamicMessage2).subscribe(res => {
                    this.DynamicMessagecontent2 = documentToHtmlString(res.fields.copy);

                    if (res.fields.hasOwnProperty("copyFont")) {
                        this.copyFont2 = res.fields.copyFont;
                        document.documentElement.style.setProperty('--copyFont2', this.copyFont2);
                    } else { console.log("copyFont not exists") }

                    if (res.fields.hasOwnProperty("copyFontColor")) {
                        this.copyFontColor2 = res.fields.copyFontColor.value;
                        document.documentElement.style.setProperty('--copyFontColor2', this.copyFontColor2);
                    } else { console.log("copyFontColor not exists") }

                    if (res.fields.hasOwnProperty("copyFontSize")) {
                        this.copyFontSize2 = res.fields.copyFontSize;
                        document.documentElement.style.setProperty('--copyFontSize2', this.copyFontSize2);
                    } else { console.log("copyFontSize not exists") }

                    if (res.fields.hasOwnProperty("copyFontSizeMobile")) {
                        this.copyFontSizeMobile2 = res.fields.copyFontSizeMobile;
                        document.documentElement.style.setProperty('--copyFontSizeMobile2', this.copyFontSizeMobile2);
                    } else { console.log("copyFontSizeMobile not exists") }

                    if (res.fields.hasOwnProperty("copyTextAlign")) {
                        this.copyTextAlign2 = res.fields.copyTextAlign;
                        document.documentElement.style.setProperty('--copyTextAlign2', this.copyTextAlign2);
                    } else { console.log("copyTextAlign not exists") }

                    if (res.fields.hasOwnProperty("copyLineHeight")) {
                        this.copyLineHeight2 = res.fields.copyLineHeight;
                        document.documentElement.style.setProperty('--copyLineHeight2', this.copyLineHeight2);
                    } else { console.log("copyLineHeight not exists") }

                    if (res.fields.hasOwnProperty("copyPadding")) {
                        this.copyPadding2 = res.fields.copyPadding;
                        document.documentElement.style.setProperty('--copyPadding2', this.copyPadding2);
                    } else { console.log("copyPadding not exists") }

                    if (res.fields.hasOwnProperty("copyMargin")) {
                        this.copyMargin2 = res.fields.copyMargin;
                        document.documentElement.style.setProperty('--copyMargin2', this.copyMargin2);
                    } else { console.log("copyMargin not exists") }

                    if (res.fields.hasOwnProperty("copyTextAlignMobile")) {
                        this.copyTextAlignMobile2 = res.fields.copyTextAlignMobile;
                        document.documentElement.style.setProperty('--copyTextAlignMobile2', this.copyTextAlignMobile2);
                    } else { console.log("copyTextAlignMobile not exists") }

                    if (res.fields.hasOwnProperty("copyLineHeightMobile")) {
                        this.copyLineHeightMobile2 = res.fields.copyLineHeightMobile;
                        document.documentElement.style.setProperty('--copyLineHeightMobile2', this.copyLineHeightMobile2);
                    } else { console.log("copyLineHeightMobile not exists") }

                    if (res.fields.hasOwnProperty("copyPaddingMobile")) {
                        this.copyPaddingMobile2 = res.fields.copyPaddingMobile;
                        document.documentElement.style.setProperty('--copyPaddingMobile2', this.copyPaddingMobile2);
                    } else { console.log("copyPaddingMobile not exists") }

                    if (res.fields.hasOwnProperty("copyMarginMobile")) {
                        this.copyMarginMobile2 = res.fields.copyMarginMobile;
                        document.documentElement.style.setProperty('--copyMarginMobile2', this.copyMarginMobile2);
                    } else { console.log("copyMarginMobile not exists") }
                });
                this.DynamicMessage3 = res.fields.messages[3].sys.id;
                this.contentfulservice.getdatapreview(this.DynamicMessage3).subscribe(res => {
                    console.log("DynamicMessage3", res)
                    this.DynamicMessagecontent3 = documentToHtmlString(res.fields.copy);

                    if (res.fields.hasOwnProperty("copyFont")) {
                        this.copyFont3 = res.fields.copyFont;
                        document.documentElement.style.setProperty('--copyFont3', this.copyFont3);
                    } else { console.log("copyFont not exists") }

                    if (res.fields.hasOwnProperty("copyFontColor")) {
                        this.copyFontColor3 = res.fields.copyFontColor.value;
                        document.documentElement.style.setProperty('--copyFontColor3', this.copyFontColor3);
                    } else { console.log("copyFontColor not exists") }

                    if (res.fields.hasOwnProperty("copyFontSize")) {
                        this.copyFontSize3 = res.fields.copyFontSize;
                        document.documentElement.style.setProperty('--copyFontSize3', this.copyFontSize3);
                    } else { console.log("copyFontSize not exists") }

                    if (res.fields.hasOwnProperty("copyFontSizeMobile")) {
                        this.copyFontSizeMobile3 = res.fields.copyFontSizeMobile;
                        document.documentElement.style.setProperty('--copyFontSizeMobile3', this.copyFontSizeMobile3);
                    } else { console.log("copyFontSizeMobile not exists") }

                    if (res.fields.hasOwnProperty("copyTextAlign")) {
                        this.copyTextAlign3 = res.fields.copyTextAlign;
                        document.documentElement.style.setProperty('--copyTextAlign3', this.copyTextAlign3);
                    } else { console.log("copyTextAlign not exists") }

                    if (res.fields.hasOwnProperty("copyLineHeight")) {
                        this.copyLineHeight3 = res.fields.copyLineHeight;
                        document.documentElement.style.setProperty('--copyLineHeight3', this.copyLineHeight3);
                    } else { console.log("copyLineHeight not exists") }

                    if (res.fields.hasOwnProperty("copyPadding")) {
                        this.copyPadding3 = res.fields.copyPadding;
                        document.documentElement.style.setProperty('--copyPadding3', this.copyPadding3);
                    } else { console.log("copyPadding not exists") }

                    if (res.fields.hasOwnProperty("copyMargin")) {
                        this.copyMargin3 = res.fields.copyMargin;
                        document.documentElement.style.setProperty('--copyMargin3', this.copyMargin3);
                    } else { console.log("copyMargin not exists") }

                    if (res.fields.hasOwnProperty("copyTextAlignMobile")) {
                        this.copyTextAlignMobile3 = res.fields.copyTextAlignMobile;
                        document.documentElement.style.setProperty('--copyTextAlignMobile3', this.copyTextAlignMobile3);
                    } else { console.log("copyTextAlignMobile not exists") }

                    if (res.fields.hasOwnProperty("copyLineHeightMobile")) {
                        this.copyLineHeightMobile3 = res.fields.copyLineHeightMobile;
                        document.documentElement.style.setProperty('--copyLineHeightMobile3', this.copyLineHeightMobile3);
                    } else { console.log("copyLineHeightMobile not exists") }

                    if (res.fields.hasOwnProperty("copyPaddingMobile")) {
                        this.copyPaddingMobile3 = res.fields.copyPaddingMobile;
                        document.documentElement.style.setProperty('--copyPaddingMobile3', this.copyPaddingMobile3);
                    } else { console.log("copyPaddingMobile not exists") }

                    if (res.fields.hasOwnProperty("copyMarginMobile")) {
                        this.copyMarginMobile3 = res.fields.copyMarginMobile;
                        document.documentElement.style.setProperty('--copyMarginMobile3', this.copyMarginMobile3);
                    } else { console.log("copyMarginMobile not exists") }
                });
                this.DynamicMessage4 = res.fields.messages[4].sys.id;
                this.contentfulservice.getdatapreview(this.DynamicMessage4).subscribe(res => {
                    console.log("DynamicMessage3", res)
                    this.DynamicMessagecontent4 = documentToHtmlString(res.fields.copy);

                    if (res.fields.hasOwnProperty("copyFont")) {
                        this.copyFont4 = res.fields.copyFont;
                        document.documentElement.style.setProperty('--copyFont4', this.copyFont4);
                    } else { console.log("copyFont not exists") }

                    if (res.fields.hasOwnProperty("copyFontColor")) {
                        this.copyFontColor4 = res.fields.copyFontColor.value;
                        document.documentElement.style.setProperty('--copyFontColor4', this.copyFontColor4);
                    } else { console.log("copyFontColor not exists") }

                    if (res.fields.hasOwnProperty("copyFontSize")) {
                        this.copyFontSize4 = res.fields.copyFontSize;
                        document.documentElement.style.setProperty('--copyFontSize4', this.copyFontSize4);
                    } else { console.log("copyFontSize not exists") }

                    if (res.fields.hasOwnProperty("copyFontSizeMobile")) {
                        this.copyFontSizeMobile4 = res.fields.copyFontSizeMobile;
                        document.documentElement.style.setProperty('--copyFontSizeMobile4', this.copyFontSizeMobile4);
                    } else { console.log("copyFontSizeMobile not exists") }

                    if (res.fields.hasOwnProperty("copyTextAlign")) {
                        this.copyTextAlign4 = res.fields.copyTextAlign;
                        document.documentElement.style.setProperty('--copyTextAlign4', this.copyTextAlign4);
                    } else { console.log("copyTextAlign not exists") }

                    if (res.fields.hasOwnProperty("copyLineHeight")) {
                        this.copyLineHeight4 = res.fields.copyLineHeight;
                        document.documentElement.style.setProperty('--copyLineHeight4', this.copyLineHeight4);
                    } else { console.log("copyLineHeight not exists") }

                    if (res.fields.hasOwnProperty("copyPadding")) {
                        this.copyPadding4 = res.fields.copyPadding;
                        document.documentElement.style.setProperty('--copyPadding4', this.copyPadding4);
                    } else { console.log("copyPadding not exists") }

                    if (res.fields.hasOwnProperty("copyMargin")) {
                        this.copyMargin4 = res.fields.copyMargin;
                        document.documentElement.style.setProperty('--copyMargin4', this.copyMargin4);
                    } else { console.log("copyMargin not exists") }

                    if (res.fields.hasOwnProperty("copyTextAlignMobile")) {
                        this.copyTextAlignMobile4 = res.fields.copyTextAlignMobile;
                        document.documentElement.style.setProperty('--copyTextAlignMobile4', this.copyTextAlignMobile4);
                    } else { console.log("copyTextAlignMobile not exists") }

                    if (res.fields.hasOwnProperty("copyLineHeightMobile")) {
                        this.copyLineHeightMobile4 = res.fields.copyLineHeightMobile;
                        document.documentElement.style.setProperty('--copyLineHeightMobile4', this.copyLineHeightMobile4);
                    } else { console.log("copyLineHeightMobile not exists") }

                    if (res.fields.hasOwnProperty("copyPaddingMobile")) {
                        this.copyPaddingMobile4 = res.fields.copyPaddingMobile;
                        document.documentElement.style.setProperty('--copyPaddingMobile4', this.copyPaddingMobile4);
                    } else { console.log("copyPaddingMobile not exists") }

                    if (res.fields.hasOwnProperty("copyMarginMobile")) {
                        this.copyMarginMobile4 = res.fields.copyMarginMobile;
                        document.documentElement.style.setProperty('--copyMarginMobile4', this.copyMarginMobile4);
                    } else { console.log("copyMarginMobile not exists") }
                });
                // icons
                this.openIcon = res.fields.openIcon.sys.id;
                this.contentfulservice.getassets(this.openIcon).subscribe(res => {
                    this.openIcon = res.fields.file.url;
                });
                this.closeIcon = res.fields.closeIcon.sys.id;
                this.contentfulservice.getassets(this.closeIcon).subscribe(res => {
                    this.closeIcon = res.fields.file.url;
                });
                // collapsible 
                if (res.fields.hasOwnProperty("collapsibleDefaultPosition")) {
                    this.collapsibleDefaultPosition1 = res.fields.collapsibleDefaultPosition;
                    this.isDropdownOpen1 = this.collapsibleDefaultPosition1 === 'Closed' ? false : true;
                } else { console.log("collapsibleDefaultPosition not exists") }

                // styles
                if (res.fields.hasOwnProperty("backgroundColor")) {
                    this.dynamicMsgbg = res.fields.backgroundColor.value;
                    document.documentElement.style.setProperty('--dynamicMsgbg', this.dynamicMsgbg);
                } else { console.log("backgroundColor not exists") }

                if (res.fields.hasOwnProperty("iconBackground") && res.fields.iconBackground == true) {
                    if (res.fields.hasOwnProperty("iconBackgroundColor")) {
                        this.iconBackgroundColor = res.fields.iconBackgroundColor.value;
                        document.documentElement.style.setProperty('--iconBackgroundColor', this.iconBackgroundColor);
                    } else { console.log("iconBackgroundColor not exists") }
                } else { console.log("iconBackground not exists or false") }

                if (res.fields.hasOwnProperty("iconPadding")) {
                    this.branddiseaseiconPadding = res.fields.iconPadding;
                    document.documentElement.style.setProperty('--branddiseaseiconPadding', this.branddiseaseiconPadding);
                } else { console.log("iconPadding not exists") }

                if (res.fields.hasOwnProperty("iconMargin")) {
                    this.branddiseaseiconMargin = res.fields.iconMargin;
                    document.documentElement.style.setProperty('--branddiseaseiconMargin', this.branddiseaseiconMargin);
                } else { console.log("iconMargin not exists") }

                if (res.fields.hasOwnProperty("iconWidth")) {
                    this.branddiseaseiconWidth = res.fields.iconWidth;
                    document.documentElement.style.setProperty('--branddiseaseiconWidth', this.branddiseaseiconWidth);
                } else { console.log("iconWidth not exists") }

                if (res.fields.hasOwnProperty("iconHeight")) {
                    this.branddiseaseiconHeight = res.fields.iconHeight;
                    document.documentElement.style.setProperty('--branddiseaseiconHeight', this.branddiseaseiconHeight);
                } else { console.log("iconHeight not exists") }

                if (res.fields.hasOwnProperty("iconWidthMobile")) {
                    this.branddiseaseiconWidthMobile = res.fields.iconWidthMobile;
                    document.documentElement.style.setProperty('--branddiseaseiconWidthMobile', this.branddiseaseiconWidthMobile);
                } else { console.log("iconWidthMobile not exists") }

                if (res.fields.hasOwnProperty("iconHeightMobile")) {
                    this.branddiseaseiconHeightMobile = res.fields.iconHeightMobile;
                    document.documentElement.style.setProperty('--branddiseaseiconHeightMobile', this.branddiseaseiconHeightMobile);
                } else { console.log("iconHeightMobile not exists") }

                if (res.fields.hasOwnProperty("iconBackgroundBorderRadius")) {
                    this.iconBackgroundBorderRadius = res.fields.iconBackgroundBorderRadius;
                    document.documentElement.style.setProperty('--iconBackgroundBorderRadius', this.iconBackgroundBorderRadius);
                } else { console.log("iconBackgroundBorderRadius not exists") }

                if (res.fields.hasOwnProperty("padding")) {
                    this.massecpadding = res.fields.padding;
                    document.documentElement.style.setProperty('--massecpadding', this.massecpadding);
                } else { console.log("padding not exists") }

                if (res.fields.hasOwnProperty("roundedCorners") && res.fields.roundedCorners == true) {
                    if (res.fields.hasOwnProperty("borderRadiusRoundedCorners")) {
                        this.borderRadiusRoundedCorners = res.fields.borderRadiusRoundedCorners;
                        document.documentElement.style.setProperty('--borderRadiusRoundedCorners', this.borderRadiusRoundedCorners);
                    } else { console.log("borderRadiusRoundedCorners not exists") }
                } else { console.log("roundedCorners not exists or false") }
            });
        } else {
            console.log("dynamicSystemMessages not exits")
        }
    }



    textBoxResourceLists() {
        if (this.brandresourceres.fields.hasOwnProperty("backgroundColor")) {
            this.textBoxResourceListsbg = this.brandresourceres.fields.backgroundColor.value;
            document.documentElement.style.setProperty('--textBoxResourceListsbg', this.textBoxResourceListsbg);
        } else { console.log("backgroundColor not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("borderRadius")) {
            this.textBoxResourceLsborderRadius = this.brandresourceres.fields.borderRadius;
            document.documentElement.style.setProperty('--textBoxResourceLsborderRadius', this.textBoxResourceLsborderRadius);
        } else { console.log("borderRadius not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("iconBackgroundColor")) {
            this.ResiconBackgroundColor = this.brandresourceres.fields.iconBackgroundColor.value;
            document.documentElement.style.setProperty('--ResiconBackgroundColor', this.ResiconBackgroundColor);
        } else { console.log("iconBackgroundColor not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("iconRolloverBackgroundColor")) {
            this.ResiconRolloverBackgroundColor = this.brandresourceres.fields.iconRolloverBackgroundColor.value;
            document.documentElement.style.setProperty('--ResiconRolloverBackgroundColor', this.ResiconRolloverBackgroundColor);
        } else { console.log("iconRolloverBackgroundColor not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("healthToolsIconsVerticalAlignment")) {
            this.healthToolsIconsVerticalAlignment = this.brandresourceres.fields.healthToolsIconsVerticalAlignment;
            let alignItems: string;
            if (this.healthToolsIconsVerticalAlignment === 'middle') {
                alignItems = 'center';
            } else if (this.healthToolsIconsVerticalAlignment === 'top') {
                alignItems = 'flex-start';
            } else {
                alignItems = 'flex-end';
            }
            document.documentElement.style.setProperty('--healthToolsIconsVerticalAlignment', alignItems);
        } else { console.log("healthToolsIconsVerticalAlignment not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("healthToolsTitleMargin")) {
            this.healthToolsTitleMargin = this.brandresourceres.fields.healthToolsTitleMargin;
            document.documentElement.style.setProperty('--healthToolsTitleMargin', this.healthToolsTitleMargin);
        } else { console.log("healthToolsTitleMargin not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("healthToolsTitleMarginMobile")) {
            this.healthToolsTitleMarginMobile = this.brandresourceres.fields.healthToolsTitleMarginMobile;
            document.documentElement.style.setProperty('--healthToolsTitleMarginMobile', this.healthToolsTitleMarginMobile);
        } else { console.log("healthToolsTitleMarginMobile not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("iconWidth")) {
            this.ResLsiconWidth = this.brandresourceres.fields.iconWidth;
            document.documentElement.style.setProperty('--ResLsiconWidth', this.ResLsiconWidth);
        } else { console.log("iconWidth not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("iconHeight")) {
            this.ResLsiconHeight = this.brandresourceres.fields.iconHeight;
            document.documentElement.style.setProperty('--ResLsiconHeight', this.ResLsiconHeight);
        } else { console.log("iconHeight not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("healthToolsTitleFontSize")) {
            this.healthToolsTitleFontSize = this.brandresourceres.fields.healthToolsTitleFontSize;
            document.documentElement.style.setProperty('--healthToolsTitleFontSize', this.healthToolsTitleFontSize);
        } else { console.log("healthToolsTitleFontSize not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("healthToolsTitleFontSizeMobile")) {
            this.healthToolsTitleFontSizeMobile = this.brandresourceres.fields.healthToolsTitleFontSizeMobile;
            document.documentElement.style.setProperty('--healthToolsTitleFontSizeMobile', this.healthToolsTitleFontSizeMobile);
        } else { console.log("healthToolsTitleFontSizeMobile not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("healthToolsTitleFontWeight")) {
            this.healthToolsTitleFontWeight = this.brandresourceres.fields.healthToolsTitleFontWeight;
            document.documentElement.style.setProperty('--healthToolsTitleFontWeight', this.healthToolsTitleFontWeight);
        } else { console.log("healthToolsTitleFontWeight not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("healthToolsTitleFontColor")) {
            this.healthToolsTitleFontColor = this.brandresourceres.fields.healthToolsTitleFontColor.value;
            document.documentElement.style.setProperty('--healthToolsTitleFontColor', this.healthToolsTitleFontColor);
        } else { console.log("healthToolsTitleFontColor not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("healthToolsDescriptionFontSize")) {
            this.healthToolsDescriptionFontSize = this.brandresourceres.fields.healthToolsDescriptionFontSize;
            document.documentElement.style.setProperty('--healthToolsDescriptionFontSize', this.healthToolsDescriptionFontSize);
        } else { console.log("healthToolsDescriptionFontSize not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("healthToolsDescriptionFontSizeMobile")) {
            this.healthToolsDescriptionFontSizeMobile = this.brandresourceres.fields.healthToolsDescriptionFontSizeMobile;
            document.documentElement.style.setProperty('--healthToolsDescriptionFontSizeMobile', this.healthToolsDescriptionFontSizeMobile);
        } else { console.log("healthToolsDescriptionFontSizeMobile not exists") }

        if (this.brandresourceres.fields.hasOwnProperty("healthToolsDescriptionFontColor")) {
            this.healthToolsDescriptionFontColor = this.brandresourceres.fields.healthToolsDescriptionFontColor.value;
            document.documentElement.style.setProperty('--healthToolsDescriptionFontColor', this.healthToolsDescriptionFontColor);
        } else { console.log("healthToolsDescriptionFontColor not exists") }

        this.expandIcon = this.brandresourceres.fields.expandIcon.sys.id;
        this.contentfulservice.getassets(this.expandIcon).subscribe(res => {
            this.expandIcon = res.fields.file.url;
        });

        this.expandIconPopUpContent = this.brandresourceres.fields.expandIconPopUpContent.sys.id;
        this.contentfulservice.getdatapreview(this.expandIconPopUpContent).subscribe(res => {
            this.expandIconPopUpContent = res.fields.text.content[0].content[0].value;

            if (res.fields.hasOwnProperty("backgroundColor")) {
                this.expandIconbackgroundColor = res.fields.backgroundColor.value;
                document.documentElement.style.setProperty('--expandIconbackgroundColor', this.expandIconbackgroundColor);
            } else { console.log("backgroundColor not exists") }

            if (res.fields.hasOwnProperty("borderRadius")) {
                this.expandIconborderRadius = res.fields.borderRadius;
                document.documentElement.style.setProperty('--expandIconborderRadius', this.expandIconborderRadius);
            } else { console.log("borderRadius not exists") }

            if (res.fields.hasOwnProperty("textBlockPadding")) {
                this.expandIcontextBlockPadding = res.fields.textBlockPadding;
                document.documentElement.style.setProperty('--expandIcontextBlockPadding', this.expandIcontextBlockPadding);
            } else { console.log("textBlockPadding not exists") }

            if (res.fields.hasOwnProperty("fontColor")) {
                this.expandIconfontColor = res.fields.fontColor.value;
                document.documentElement.style.setProperty('--expandIconfontColor', this.expandIconfontColor);
            } else { console.log("fontColor not exists") }

            if (res.fields.hasOwnProperty("width")) {
                this.expandIconwidth = res.fields.width;
                document.documentElement.style.setProperty('--expandIconwidth', this.expandIconwidth);
            } else { console.log("width not exists") }

            if (res.fields.hasOwnProperty("textAlignment")) {
                this.expandIcontextAlignment = res.fields.textAlignment;
                document.documentElement.style.setProperty('--expandIcontextAlignment', this.expandIcontextAlignment);
            } else { console.log("textAlignment not exists") }

            if (res.fields.hasOwnProperty("fontSize")) {
                this.expandIconfontSize = res.fields.fontSize;
                document.documentElement.style.setProperty('--expandIconfontSize', this.expandIconfontSize);
            } else { console.log("fontSize not exists") }

            if (res.fields.hasOwnProperty("fontSizeMobile")) {
                this.expandIconfontSizeMobile = res.fields.fontSizeMobile;
                document.documentElement.style.setProperty('--expandIconfontSizeMobile', this.expandIconfontSizeMobile);
            } else { console.log("fontSizeMobile not exists") }
        });

        this.previewIconPopUpContent = this.brandresourceres.fields.previewIconPopUpContent.sys.id;
        this.contentfulservice.getdatapreview(this.previewIconPopUpContent).subscribe(res => {
            this.previewIconPopUpContent = res.fields.text.content[0].content[0].value;

            if (res.fields.hasOwnProperty("backgroundColor")) {
                this.previewIconPopUpContentbackgroundColor = res.fields.backgroundColor.value;
                document.documentElement.style.setProperty('--previewIconPopUpContentbackgroundColor', this.previewIconPopUpContentbackgroundColor);
            } else { console.log("backgroundColor not exists") }

            if (res.fields.hasOwnProperty("borderRadius")) {
                this.previewIconPopUpContentborderRadius = res.fields.borderRadius;
                document.documentElement.style.setProperty('--previewIconPopUpContentborderRadius', this.previewIconPopUpContentborderRadius);
            } else { console.log("borderRadius not exists") }

            if (res.fields.hasOwnProperty("textBlockPadding")) {
                this.previewIconPopUpContenttextBlockPadding = res.fields.textBlockPadding;
                document.documentElement.style.setProperty('--previewIconPopUpContenttextBlockPadding', this.previewIconPopUpContenttextBlockPadding);
            } else { console.log("textBlockPadding not exists") }

            if (res.fields.hasOwnProperty("fontColor")) {
                this.previewIconPopUpContentfontColor = res.fields.fontColor.value;
                document.documentElement.style.setProperty('--previewIconPopUpContentfontColor', this.previewIconPopUpContentfontColor);
            } else { console.log("fontColor not exists") }

            if (res.fields.hasOwnProperty("width")) {
                this.previewIconPopUpContentwidth = res.fields.width;
                document.documentElement.style.setProperty('--previewIconPopUpContentwidth', this.previewIconPopUpContentwidth);
            } else { console.log("width not exists") }

            if (res.fields.hasOwnProperty("textAlignment")) {
                this.previewIconPopUpContenttextAlignment = res.fields.textAlignment;
                document.documentElement.style.setProperty('--previewIconPopUpContenttextAlignment', this.previewIconPopUpContenttextAlignment);
            } else { console.log("textAlignment not exists") }

            if (res.fields.hasOwnProperty("fontSize")) {
                this.previewIconPopUpContentfontSize = res.fields.fontSize;
                document.documentElement.style.setProperty('--previewIconPopUpContentfontSize', this.previewIconPopUpContentfontSize);
            } else { console.log("fontSize not exists") }

            if (res.fields.hasOwnProperty("fontSizeMobile")) {
                this.previewIconPopUpContentfontSizeMobile = res.fields.fontSizeMobile;
                document.documentElement.style.setProperty('--previewIconPopUpContentfontSizeMobile', this.previewIconPopUpContentfontSizeMobile);
            } else { console.log("fontSizeMobile not exists") }
        });



        this.checkboxSelectPopUpContent = this.brandresourceres.fields.checkboxSelectPopUpContent.sys.id;
        this.contentfulservice.getdatapreview(this.checkboxSelectPopUpContent).subscribe(res => {
            this.checkboxSelectPopUpContent = res.fields.text.content[0].content[0].value;

            if (res.fields.hasOwnProperty("backgroundColor")) {
                this.checkboxSelectPopUpContentbackgroundColor = res.fields.backgroundColor.value;
                document.documentElement.style.setProperty('--checkboxSelectPopUpContentbackgroundColor', this.checkboxSelectPopUpContentbackgroundColor);
            } else { console.log("backgroundColor not exists") }

            if (res.fields.hasOwnProperty("borderRadius")) {
                this.checkboxSelectPopUpContentborderRadius = res.fields.borderRadius;
                document.documentElement.style.setProperty('--checkboxSelectPopUpContentborderRadius', this.checkboxSelectPopUpContentborderRadius);
            } else { console.log("borderRadius not exists") }

            if (res.fields.hasOwnProperty("textBlockPadding")) {
                this.checkboxSelectPopUpContenttextBlockPadding = res.fields.textBlockPadding;
                document.documentElement.style.setProperty('--checkboxSelectPopUpContenttextBlockPadding', this.checkboxSelectPopUpContenttextBlockPadding);
            } else { console.log("textBlockPadding not exists") }

            if (res.fields.hasOwnProperty("fontColor")) {
                this.checkboxSelectPopUpContentfontColor = res.fields.fontColor.value;
                document.documentElement.style.setProperty('--checkboxSelectPopUpContentfontColor', this.checkboxSelectPopUpContentfontColor);
            } else { console.log("fontColor not exists") }

            if (res.fields.hasOwnProperty("width")) {
                this.checkboxSelectPopUpContentwidth = res.fields.width;
                document.documentElement.style.setProperty('--checkboxSelectPopUpContentwidth', this.checkboxSelectPopUpContentwidth);
            } else { console.log("width not exists") }

            if (res.fields.hasOwnProperty("textAlignment")) {
                this.checkboxSelectPopUpContenttextAlignment = res.fields.textAlignment;
                document.documentElement.style.setProperty('--checkboxSelectPopUpContenttextAlignment', this.checkboxSelectPopUpContenttextAlignment);
            } else { console.log("textAlignment not exists") }

            if (res.fields.hasOwnProperty("fontSize")) {
                this.checkboxSelectPopUpContentfontSize = res.fields.fontSize;
                document.documentElement.style.setProperty('--checkboxSelectPopUpContentfontSize', this.checkboxSelectPopUpContentfontSize);
            } else { console.log("fontSize not exists") }

            if (res.fields.hasOwnProperty("fontSizeMobile")) {
                this.checkboxSelectPopUpContentfontSizeMobile = res.fields.fontSizeMobile;
                document.documentElement.style.setProperty('--checkboxSelectPopUpContentfontSizeMobile', this.checkboxSelectPopUpContentfontSizeMobile);
            } else { console.log("fontSizeMobile not exists") }
        });

        if (this.brandresourceres.fields.hasOwnProperty("iconPadding")) {
            this.branddiseaseresiconPadding = this.brandresourceres.fields.iconPadding;
            document.documentElement.style.setProperty('--branddiseaseresiconPadding', this.branddiseaseresiconPadding);
        } else { console.log("iconPadding not exists") }
        if (this.brandresourceres.fields.hasOwnProperty("iconMargin")) {
            this.branddiseaseresiconMargin = this.brandresourceres.fields.iconMargin;
            document.documentElement.style.setProperty('--branddiseaseresiconMargin', this.branddiseaseresiconMargin);
        } else { console.log("iconMargin not exists") }
        if (this.brandresourceres.fields.hasOwnProperty("iconWidth")) {
            this.branddiseaseresWidth = this.brandresourceres.fields.iconWidth;
            document.documentElement.style.setProperty('--branddiseaseresWidth', this.branddiseaseresWidth);
        } else { console.log("iconWidth not exists") }
        if (this.brandresourceres.fields.hasOwnProperty("iconHeight")) {
            this.branddiseaseresHeight = this.brandresourceres.fields.iconHeight;
            document.documentElement.style.setProperty('--branddiseaseresHeight', this.branddiseaseresHeight);
        } else { console.log("iconHeight not exists") }
        if (this.brandresourceres.fields.hasOwnProperty("iconWidthMobile")) {
            this.branddiseaseresWidthMobile = this.brandresourceres.fields.iconWidthMobile;
            document.documentElement.style.setProperty('--branddiseaseresWidthMobile', this.branddiseaseresWidthMobile);
        } else { console.log("iconWidthMobile not exists") }
        if (this.brandresourceres.fields.hasOwnProperty("iconHeightMobile")) {
            this.branddiseaseresHeightMobile = this.brandresourceres.fields.iconHeightMobile;
            document.documentElement.style.setProperty('--branddiseaseresHeightMobile', this.branddiseaseresHeightMobile);
        } else { console.log("iconHeightMobile not exists") }

        this.previewIcon = this.brandresourceres.fields.previewIcon.sys.id;
        this.contentfulservice.getassets(this.previewIcon).subscribe(res => {
            this.previewIcon = res.fields.file.url;
        });
    }

    textBoxAboveResourceLists() {
        if (this.brandresourceres.fields.hasOwnProperty("textBoxAboveResourceLists")) {
            this.dynamicMsg2_id = this.brandresourceres.fields.textBoxAboveResourceLists.sys.id;
            this.contentfulservice.getdatapreview(this.dynamicMsg2_id).subscribe(res => {
                console.log("textBoxAboveResourceLists", res);
                this.textBoxAboveResourcecontent = documentToHtmlString(res.fields.text);
                this.textBoxAboveResourcecontentMobile = documentToHtmlString(res.fields.textMobile);

                if (res.fields.hasOwnProperty("textAlignment")) {
                    this.textBoxAboveRestextAlignment = res.fields.textAlignment;
                    document.documentElement.style.setProperty('--textBoxAboveRestextAlignment', this.textBoxAboveRestextAlignment);
                } else { console.log("textAlignment not exists") }
                if (res.fields.hasOwnProperty("textAlignmentMobile")) {
                    this.textBoxAboveRestextAlignmentMobile = res.fields.textAlignmentMobile;
                    document.documentElement.style.setProperty('--textBoxAboveRestextAlignmentMobile', this.textBoxAboveRestextAlignmentMobile);
                } else { console.log("textAlignmentMobile not exists") }

                if (res.fields.hasOwnProperty("fontSize")) {
                    this.textBoxAboveResfontSize = res.fields.fontSize;
                    document.documentElement.style.setProperty('--textBoxAboveResfontSize', this.textBoxAboveResfontSize);
                } else { console.log("fontSize not exists") }

                if (res.fields.hasOwnProperty("fontSizeMobile")) {
                    this.textBoxAboveResfontSizeMobile = res.fields.fontSizeMobile;
                    document.documentElement.style.setProperty('--textBoxAboveResfontSizeMobile', this.textBoxAboveResfontSizeMobile);
                } else { console.log("fontSizeMobile not exists") }

                if (res.fields.hasOwnProperty("fontColor")) {
                    this.textBoxAboveResfontColor = res.fields.fontColor.value;
                    document.documentElement.style.setProperty('--textBoxAboveResfontColor', this.textBoxAboveResfontColor);
                } else { console.log("fontColor not exists") }

                if (res.fields.hasOwnProperty("textBlockMargin")) {
                    this.textBoxAboveBlockMargin = res.fields.textBlockMargin;
                    document.documentElement.style.setProperty('--textBoxAboveBlockMargin', this.textBoxAboveBlockMargin);
                } else { console.log("textBlockMargin not exists") }

                if (res.fields.hasOwnProperty("textBlockMarginMobile")) {
                    this.textBoxAboveBlockMarginMobile = res.fields.textBlockMarginMobile;
                    document.documentElement.style.setProperty('--textBoxAboveBlockMarginMobile', this.textBoxAboveBlockMarginMobile);
                } else { console.log("textBlockMarginMobile not exists") }

                if (res.fields.hasOwnProperty("textBlockPaddingMobile")) {
                    this.textBoxAboveBlockPaddingMobile = res.fields.textBlockPaddingMobile;
                    document.documentElement.style.setProperty('--textBoxAboveBlockPaddingMobile', this.textBoxAboveBlockPaddingMobile);
                } else { console.log("textBlockPaddingMobile not exists") }

                if (res.fields.hasOwnProperty("textBlockPadding")) {
                    this.textBoxAboveBlockPadding = res.fields.textBlockPadding;
                    document.documentElement.style.setProperty('--textBoxAboveBlockPadding', this.textBoxAboveBlockPadding);
                } else { console.log("textBlockPadding not exists") }

                if (res.fields.hasOwnProperty("fontSizeMobile")) {
                    this.textBoxAboveResfontSizeMobile = res.fields.fontSizeMobile;
                    document.documentElement.style.setProperty('--textBoxAboveResfontSizeMobile', this.textBoxAboveResfontSizeMobile);
                } else { console.log("fontSizeMobile not exists") }

                if (res.fields.hasOwnProperty("lineHeight")) {
                    this.textBoxAboveReslineHeight = res.fields.lineHeight;
                    document.documentElement.style.setProperty('--textBoxAboveReslineHeight', this.textBoxAboveReslineHeight);
                } else { console.log("lineHeight not exists") }

                if (res.fields.hasOwnProperty("lineHeightMobile")) {
                    this.textBoxAboveReslineHeightMobile = res.fields.lineHeightMobile;
                    document.documentElement.style.setProperty('--textBoxAboveReslineHeightMobile', this.textBoxAboveReslineHeightMobile);
                } else { console.log("lineHeightMobile not exists") }


            });
        }
    }

    textAndLinkToSpanishResources() {
        const { fields } = this.brandresourceres;

        if (fields.hasOwnProperty("textAndLinkToSpanishResources")) {
            this.textAndLinkResources = documentToHtmlString(fields.textAndLinkToSpanishResources);
        } else { console.log("textAndLinkToSpanishResources not exists") }

        if (fields.hasOwnProperty("buttonsSectionBlock")) {
            if (fields.buttonsSectionBlock[0] && fields.buttonsSectionBlock[0].sys && fields.buttonsSectionBlock[0].sys.id) {
                this.buttonsSectionBlock0 = fields.buttonsSectionBlock[0].sys.id;
                this.contentfulservice.getdatapreview(this.buttonsSectionBlock0).subscribe(res => {
                    this.buttonsSectionBlockcecontent = documentToHtmlString(res.fields.text);
                    this.buttonsSectionBlockcecontentMobile = documentToHtmlString(res.fields.textMobile);

                    if (res.fields.hasOwnProperty("textAlignment")) {
                        this.btnsec0textAlignment = res.fields.textAlignment;
                        document.documentElement.style.setProperty('--btnsec0textAlignment', this.btnsec0textAlignment);
                    } else { console.log("textAlignment not exists") }

                    if (res.fields.hasOwnProperty("textAlignmentMobile")) {
                        this.btnsec0textAlignmentMobile = res.fields.textAlignmentMobile;
                        document.documentElement.style.setProperty('--btnsec0textAlignmentMobile', this.btnsec0textAlignmentMobile);
                    } else { console.log("textAlignmentMobile not exists") }

                    if (res.fields.hasOwnProperty("fontSize")) {
                        this.btnsec0fontSize = res.fields.fontSize;
                        document.documentElement.style.setProperty('--btnsec0fontSize', this.btnsec0fontSize);
                    } else { console.log("fontSize not exists") }
                    if (res.fields.hasOwnProperty("fontSizeMobile")) {
                        this.btnsec0fontSizeMobilet = res.fields.fontSizeMobile;
                        document.documentElement.style.setProperty('--btnsec0fontSizeMobilet', this.btnsec0fontSizeMobilet);
                    } else { console.log("fontSizeMobile not exists") }
                    if (res.fields.hasOwnProperty("fontColor")) {
                        this.btnsec0fontColor = res.fields.fontColor.value;
                        document.documentElement.style.setProperty('--btnsec0fontColor', this.btnsec0fontColor);
                    } else { console.log("fontColor not exists") }

                    if (res.fields.hasOwnProperty("textBlockMargin")) {
                        this.btnsec0textBlockMargin = res.fields.textBlockMargin;
                        document.documentElement.style.setProperty('--btnsec0textBlockMargin', this.btnsec0textBlockMargin);
                    } else { console.log("textBlockMargin not exists") }

                    if (res.fields.hasOwnProperty("textBlockMarginMobile")) {
                        this.btnsec0textBlockMarginMobile = res.fields.textBlockMarginMobile;
                        document.documentElement.style.setProperty('--btnsec0textBlockMarginMobile', this.btnsec0textBlockMarginMobile);
                    } else { console.log("textBlockMarginMobile not exists") }

                    if (res.fields.hasOwnProperty("textBlockPadding")) {
                        this.btnsec0textBlockPadding = res.fields.textBlockPadding;
                        document.documentElement.style.setProperty('--btnsec0textBlockPadding', this.btnsec0textBlockPadding);
                    } else { console.log("textBlockPadding not exists") }

                    if (res.fields.hasOwnProperty("lineHeight")) {
                        this.btnsec0lineHeight = res.fields.lineHeight;
                        document.documentElement.style.setProperty('--btnsec0lineHeight', this.btnsec0lineHeight);
                    } else { console.log("lineHeight not exists") }

                    if (res.fields.hasOwnProperty("lineHeightMobile")) {
                        this.btnsec0lineHeightMobile = res.fields.lineHeightMobile;
                        document.documentElement.style.setProperty('--btnsec0lineHeightMobile', this.btnsec0lineHeightMobile);
                    } else { console.log("lineHeightMobile not exists") }
                });
            } else { console.log("buttonsSectionBlock[0] not exists") }
        } else { console.log("buttonsSectionBlock not exists") }

        if (fields.hasOwnProperty("buttonsSectionBlock")) {
            if (fields.buttonsSectionBlock[1] && fields.buttonsSectionBlock[1].sys && fields.buttonsSectionBlock[1].sys.id) {
                this.buttonsSectionBlock = fields.buttonsSectionBlock[1].sys.id;
                this.contentfulservice.getdatapreview(this.buttonsSectionBlock).subscribe(res => {
                    console.log("buttonsSectionBlock", res);
                    if (res.fields.hasOwnProperty("columns")) {
                        res.fields.columns.forEach((block, index) => {
                            if (block && block.sys && block.sys.id) {
                                this.contentfulservice.getdatapreview(block.sys.id).subscribe(res => {
                                    if (res.fields.hasOwnProperty("columnContent")) {
                                        const columnContent = res.fields.columnContent[0].sys.id;
                                        this.contentfulservice.getdatapreview(columnContent).subscribe(res => {
                                            if (index === 0) {
                                                if (res.fields.hasOwnProperty("customButtonIcon")) {
                                                    this.Emailimage = res.fields.customButtonIcon.sys.id;
                                                    this.contentfulservice.getassets(this.Emailimage).subscribe(res => {
                                                        this.Emailimage = res.fields.file.url;
                                                    });
                                                } else { console.log("customButtonIcon not exists") }
                                                if (res.fields.hasOwnProperty("customButtonBackgroundColor")) {
                                                    this.customButtonBackgroundColor = res.fields.customButtonBackgroundColor.value;
                                                    document.documentElement.style.setProperty('--customButtonBackgroundColor', this.customButtonBackgroundColor);
                                                } else { console.log("customButtonBackgroundColor not exists") }
                                                if (res.fields.hasOwnProperty("customButtonBackgroundRolloverColor")) {
                                                    this.customButtonBackgroundRolloverColor = res.fields.customButtonBackgroundRolloverColor.value;
                                                    document.documentElement.style.setProperty('--customButtonBackgroundRolloverColor', this.customButtonBackgroundRolloverColor);
                                                } else { console.log("customButtonBackgroundRolloverColor not exists") }
                                                if (res.fields.hasOwnProperty("buttonBorderRadius")) {
                                                    this.buttonBorderRadius = res.fields.buttonBorderRadius;
                                                    document.documentElement.style.setProperty('--buttonBorderRadius', this.buttonBorderRadius);
                                                } else { console.log("buttonBorderRadius not exists") }
                                                if (res.fields.hasOwnProperty("buttonPadding")) {
                                                    this.buttonPadding = res.fields.buttonPadding;
                                                    document.documentElement.style.setProperty('--buttonPadding', this.buttonPadding);
                                                } else { console.log("buttonPadding not exists") }
                                                if (res.fields.hasOwnProperty("buttonWidth")) {
                                                    this.buttonWidth = res.fields.buttonWidth;
                                                    document.documentElement.style.setProperty('--buttonWidth', this.buttonWidth);
                                                } else { console.log("buttonWidth not exists") }
                                                if (res.fields.hasOwnProperty("buttonHeight")) {
                                                    this.buttonHeight = res.fields.buttonHeight;
                                                    document.documentElement.style.setProperty('--buttonHeight', this.buttonHeight);
                                                } else { console.log("buttonHeight not exists") }
                                                if (res.fields.hasOwnProperty("buttonWidthMobile")) {
                                                    this.buttonWidthMobile = res.fields.buttonWidthMobile;
                                                    document.documentElement.style.setProperty('--buttonWidthMobile', this.buttonWidthMobile);
                                                } else { console.log("buttonWidthMobile not exists") }
                                                if (res.fields.hasOwnProperty("buttonHeightMobile")) {
                                                    this.buttonHeightMobile = res.fields.buttonHeightMobile;
                                                    document.documentElement.style.setProperty('--buttonHeightMobile', this.buttonHeightMobile);
                                                } else { console.log("buttonHeightMobile not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconWidth")) {
                                                    this.customButtonIconWidth = res.fields.customButtonIconWidth;
                                                    document.documentElement.style.setProperty('--customButtonIconWidth', this.customButtonIconWidth);
                                                } else { console.log("customButtonIconWidth not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconHeight")) {
                                                    this.customButtonIconHeight = res.fields.customButtonIconHeight;
                                                    document.documentElement.style.setProperty('--customButtonIconHeight', this.customButtonIconHeight);
                                                } else { console.log("customButtonIconHeight not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconWidthMobile")) {
                                                    this.customButtonIconWidthMobile = res.fields.customButtonIconWidthMobile;
                                                    document.documentElement.style.setProperty('--customButtonIconWidthMobile', this.customButtonIconWidthMobile);
                                                } else { console.log("customButtonIconWidthMobile not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconHeightMobile")) {
                                                    this.customButtonIconHeightMobile = res.fields.customButtonIconHeightMobile;
                                                    document.documentElement.style.setProperty('--customButtonIconHeightMobile', this.customButtonIconHeightMobile);
                                                } else { console.log("customButtonIconHeightMobile not exists") }
                                            } else if (index === 1) {
                                                if (res.fields.hasOwnProperty("customButtonIcon")) {
                                                    this.Printimage = res.fields.customButtonIcon.sys.id;
                                                    this.contentfulservice.getassets(this.Printimage).subscribe(res => {
                                                        this.Printimage = res.fields.file.url;
                                                    });
                                                } else { console.log("customButtonIcon not exists") }
                                                if (res.fields.hasOwnProperty("customButtonBackgroundColor")) {
                                                    this.customButtonBackgroundColor1 = res.fields.customButtonBackgroundColor.value;
                                                    document.documentElement.style.setProperty('--customButtonBackgroundColor1', this.customButtonBackgroundColor1);
                                                } else { console.log("customButtonBackgroundColor not exists") }
                                                if (res.fields.hasOwnProperty("customButtonBackgroundRolloverColor")) {
                                                    this.customButtonBackgroundRolloverColor1 = res.fields.customButtonBackgroundRolloverColor.value;
                                                    document.documentElement.style.setProperty('--customButtonBackgroundRolloverColor1', this.customButtonBackgroundRolloverColor1);
                                                } else { console.log("customButtonBackgroundRolloverColor not exists") }
                                                if (res.fields.hasOwnProperty("buttonBorderRadius")) {
                                                    this.buttonBorderRadius1 = res.fields.buttonBorderRadius;
                                                    document.documentElement.style.setProperty('--buttonBorderRadius1', this.buttonBorderRadius1);
                                                } else { console.log("buttonBorderRadius not exists") }
                                                if (res.fields.hasOwnProperty("buttonPadding")) {
                                                    this.buttonPadding1 = res.fields.buttonPadding;
                                                    document.documentElement.style.setProperty('--buttonPadding1', this.buttonPadding1);
                                                } else { console.log("buttonPadding not exists") }
                                                if (res.fields.hasOwnProperty("buttonWidth")) {
                                                    this.buttonWidth1 = res.fields.buttonWidth;
                                                    document.documentElement.style.setProperty('--buttonWidth1', this.buttonWidth1);
                                                } else { console.log("buttonWidth not exists") }
                                                if (res.fields.hasOwnProperty("buttonHeight")) {
                                                    this.buttonHeight1 = res.fields.buttonHeight;
                                                    document.documentElement.style.setProperty('--buttonHeight1', this.buttonHeight1);
                                                } else { console.log("buttonHeight not exists") }
                                                if (res.fields.hasOwnProperty("buttonWidthMobile")) {
                                                    this.buttonWidthMobile1 = res.fields.buttonWidthMobile;
                                                    document.documentElement.style.setProperty('--buttonWidthMobile1', this.buttonWidthMobile1);
                                                } else { console.log("buttonWidthMobile not exists") }
                                                if (res.fields.hasOwnProperty("buttonHeightMobile")) {
                                                    this.buttonHeightMobile1 = res.fields.buttonHeightMobile;
                                                    document.documentElement.style.setProperty('--buttonHeightMobile1', this.buttonHeightMobile1);
                                                } else { console.log("buttonHeightMobile not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconWidth")) {
                                                    this.customButtonIconWidth1 = res.fields.customButtonIconWidth;
                                                    document.documentElement.style.setProperty('--customButtonIconWidth1', this.customButtonIconWidth1);
                                                } else { console.log("customButtonIconWidth not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconHeight")) {
                                                    this.customButtonIconHeight1 = res.fields.customButtonIconHeight;
                                                    document.documentElement.style.setProperty('--customButtonIconHeight1', this.customButtonIconHeight1);
                                                } else { console.log("customButtonIconHeight not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconWidthMobile")) {
                                                    this.customButtonIconWidthMobile1 = res.fields.customButtonIconWidthMobile;
                                                    document.documentElement.style.setProperty('--customButtonIconWidthMobile1', this.customButtonIconWidthMobile1);
                                                } else { console.log("customButtonIconWidthMobile not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconHeightMobile")) {
                                                    this.customButtonIconHeightMobile1 = res.fields.customButtonIconHeightMobile;
                                                    document.documentElement.style.setProperty('--customButtonIconHeightMobile1', this.customButtonIconHeightMobile1);
                                                } else { console.log("customButtonIconHeightMobile not exists") }
                                            } else if (index === 2) {
                                                if (res.fields.hasOwnProperty("customButtonIcon")) {
                                                    this.CopyLinkimage = res.fields.customButtonIcon.sys.id;
                                                    this.contentfulservice.getassets(this.CopyLinkimage).subscribe(res => {
                                                        this.CopyLinkimage = res.fields.file.url;
                                                    });
                                                } else { console.log("customButtonIcon not exists") }
                                                if (res.fields.hasOwnProperty("customButtonBackgroundColor")) {
                                                    this.customButtonBackgroundColor2 = res.fields.customButtonBackgroundColor.value;
                                                    document.documentElement.style.setProperty('--customButtonBackgroundColor2', this.customButtonBackgroundColor2);
                                                } else { console.log("customButtonBackgroundColor not exists") }
                                                if (res.fields.hasOwnProperty("customButtonBackgroundRolloverColor")) {
                                                    this.customButtonBackgroundRolloverColor2 = res.fields.customButtonBackgroundRolloverColor.value;
                                                    document.documentElement.style.setProperty('--customButtonBackgroundRolloverColor2', this.customButtonBackgroundRolloverColor2);
                                                } else { console.log("customButtonBackgroundRolloverColor not exists") }
                                                if (res.fields.hasOwnProperty("buttonBorderRadius")) {
                                                    this.buttonBorderRadius2 = res.fields.buttonBorderRadius;
                                                    document.documentElement.style.setProperty('--buttonBorderRadius2', this.buttonBorderRadius2);
                                                } else { console.log("buttonBorderRadius not exists") }
                                                if (res.fields.hasOwnProperty("buttonPadding")) {
                                                    this.buttonPadding2 = res.fields.buttonPadding;
                                                    document.documentElement.style.setProperty('--buttonPadding2', this.buttonPadding2);
                                                } else { console.log("buttonPadding not exists") }
                                                if (res.fields.hasOwnProperty("buttonWidth")) {
                                                    this.buttonWidth2 = res.fields.buttonWidth;
                                                    document.documentElement.style.setProperty('--buttonWidth2', this.buttonWidth2);
                                                } else { console.log("buttonWidth not exists") }
                                                if (res.fields.hasOwnProperty("buttonHeight")) {
                                                    this.buttonHeight2 = res.fields.buttonHeight;
                                                    document.documentElement.style.setProperty('--buttonHeight2', this.buttonHeight2);
                                                } else { console.log("buttonHeight not exists") }
                                                if (res.fields.hasOwnProperty("buttonWidthMobile")) {
                                                    this.buttonWidthMobile2 = res.fields.buttonWidthMobile;
                                                    document.documentElement.style.setProperty('--buttonWidthMobile2', this.buttonWidthMobile2);
                                                } else { console.log("buttonWidthMobile not exists") }
                                                if (res.fields.hasOwnProperty("buttonHeightMobile")) {
                                                    this.buttonHeightMobile2 = res.fields.buttonHeightMobile;
                                                    document.documentElement.style.setProperty('--buttonHeightMobile2', this.buttonHeightMobile2);
                                                } else { console.log("buttonHeightMobile not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconWidth")) {
                                                    this.customButtonIconWidth2 = res.fields.customButtonIconWidth;
                                                    document.documentElement.style.setProperty('--customButtonIconWidth2', this.customButtonIconWidth2);
                                                } else { console.log("customButtonIconWidth not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconHeight")) {
                                                    this.customButtonIconHeight2 = res.fields.customButtonIconHeight;
                                                    document.documentElement.style.setProperty('--customButtonIconHeight2', this.customButtonIconHeight2);
                                                } else { console.log("customButtonIconHeight not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconWidthMobile")) {
                                                    this.customButtonIconWidthMobile2 = res.fields.customButtonIconWidthMobile;
                                                    document.documentElement.style.setProperty('--customButtonIconWidthMobile2', this.customButtonIconWidthMobile2);
                                                } else { console.log("customButtonIconWidthMobile not exists") }
                                                if (res.fields.hasOwnProperty("customButtonIconHeightMobile")) {
                                                    this.customButtonIconHeightMobile2 = res.fields.customButtonIconHeightMobile;
                                                    document.documentElement.style.setProperty('--customButtonIconHeightMobile2', this.customButtonIconHeightMobile2);
                                                } else { console.log("customButtonIconHeightMobile not exists") }
                                            }
                                        });
                                    } else { console.log("columnContent not exists") }
                                });
                            } else { console.log("block.sys.id not exists") }
                        });
                    } else { console.log("columns not exists") }
                });
            } else { console.log("buttonsSectionBlock[1] not exists") }
        } else { console.log("buttonsSectionBlock not exists") }

        if (fields.hasOwnProperty("buttonsSectionBlock")) {
            if (fields.buttonsSectionBlock[2] && fields.buttonsSectionBlock[2].sys && fields.buttonsSectionBlock[2].sys.id) {
                this.buttonsSectionBlock2 = fields.buttonsSectionBlock[2].sys.id;
                this.contentfulservice.getdatapreview(this.buttonsSectionBlock2).subscribe(res => {
                    if (res.fields.hasOwnProperty("columns")) {
                        res.fields.columns.forEach((block, index) => {
                            if (block && block.sys && block.sys.id) {
                                this.contentfulservice.getdatapreview(block.sys.id).subscribe(res => {
                                    if (res.fields.hasOwnProperty("columnContent")) {
                                        const columnContent = res.fields.columnContent[0].sys.id;
                                        this.contentfulservice.getdatapreview(columnContent).subscribe(res => {
                                            if (index === 0) {
                                                if (res.fields.hasOwnProperty("copy")) {
                                                    this.buttonemailtext = documentToHtmlString(res.fields.copy);
                                                } else { console.log("copy not exists") }
                                                if (res.fields.hasOwnProperty("copyFont")) {
                                                    this.rescopyFont = res.fields.copyFont;
                                                    document.documentElement.style.setProperty('--rescopyFont', this.rescopyFont);
                                                } else { console.log("copyFont not exists") }
                                                if (res.fields.hasOwnProperty("copyFontColor")) {
                                                    this.rescopyFontColor = res.fields.copyFontColor.value;
                                                    document.documentElement.style.setProperty('--rescopyFontColor', this.rescopyFontColor);
                                                } else { console.log("copyFontColor not exists") }
                                                if (res.fields.hasOwnProperty("copyFontSize")) {
                                                    this.rescopyFontSize = res.fields.copyFontSize;
                                                    document.documentElement.style.setProperty('--rescopyFontSize', this.rescopyFontSize);
                                                } else { console.log("copyFontSize not exists") }
                                                if (res.fields.hasOwnProperty("copyFontSizeMobile")) {
                                                    this.rescopyFontSizeMobile = res.fields.copyFontSizeMobile;
                                                    document.documentElement.style.setProperty('--rescopyFontSizeMobile', this.rescopyFontSizeMobile);
                                                } else { console.log("copyFontSizeMobile not exists") }
                                            } else if (index === 1) {
                                                if (res.fields.hasOwnProperty("copy")) {
                                                    this.buttonPrinttext = documentToHtmlString(res.fields.copy);
                                                } else { console.log("copy not exists") }
                                                if (res.fields.hasOwnProperty("copyFont")) {
                                                    this.rescopyFont1 = res.fields.copyFont;
                                                    document.documentElement.style.setProperty('--rescopyFont1', this.rescopyFont1);
                                                } else { console.log("copyFont not exists") }
                                                if (res.fields.hasOwnProperty("copyFontColor")) {
                                                    this.rescopyFontColor1 = res.fields.copyFontColor.value;
                                                    document.documentElement.style.setProperty('--rescopyFontColor1', this.rescopyFontColor1);
                                                } else { console.log("copyFontColor not exists") }
                                                if (res.fields.hasOwnProperty("copyFontSize")) {
                                                    this.rescopyFontSize1 = res.fields.copyFontSize;
                                                    document.documentElement.style.setProperty('--rescopyFontSize1', this.rescopyFontSize1);
                                                } else { console.log("copyFontSize not exists") }
                                                if (res.fields.hasOwnProperty("copyFontSizeMobile")) {
                                                    this.rescopyFontSizeMobile1 = res.fields.copyFontSizeMobile;
                                                    document.documentElement.style.setProperty('--rescopyFontSizeMobile1', this.rescopyFontSizeMobile1);
                                                } else { console.log("copyFontSizeMobile not exists") }
                                            } else if (index === 2) {
                                                if (res.fields.hasOwnProperty("copy")) {
                                                    this.buttonCopyLinktext = documentToHtmlString(res.fields.copy);
                                                } else { console.log("copy not exists") }
                                                if (res.fields.hasOwnProperty("copyFont")) {
                                                    this.rescopyFont2 = res.fields.copyFont;
                                                    document.documentElement.style.setProperty('--rescopyFont2', this.rescopyFont2);
                                                } else { console.log("copyFont not exists") }
                                                if (res.fields.hasOwnProperty("copyFontColor")) {
                                                    this.rescopyFontColor2 = res.fields.copyFontColor.value;
                                                    document.documentElement.style.setProperty('--rescopyFontColor2', this.rescopyFontColor2);
                                                } else { console.log("copyFontColor not exists") }
                                                if (res.fields.hasOwnProperty("copyFontSize")) {
                                                    this.rescopyFontSize2 = res.fields.copyFontSize;
                                                    document.documentElement.style.setProperty('--rescopyFontSize2', this.rescopyFontSize2);
                                                } else { console.log("copyFontSize not exists") }
                                                if (res.fields.hasOwnProperty("copyFontSizeMobile")) {
                                                    this.rescopyFontSizeMobile2 = res.fields.copyFontSizeMobile;
                                                    document.documentElement.style.setProperty('--rescopyFontSizeMobile2', this.rescopyFontSizeMobile2);
                                                } else { console.log("copyFontSizeMobile not exists") }
                                            }
                                        });
                                    } else { console.log("columnContent not exists") }
                                });
                            } else { console.log("block.sys.id not exists") }
                        });
                    } else { console.log("columns not exists") }
                });
            } else { console.log("buttonsSectionBlock[2] not exists") }
        } else { console.log("buttonsSectionBlock not exists") }
    }


    buttonsemailBlocksId(buttonsemailBlocksId: any) {
        throw new Error('Method not implemented.');
    }
    buttonsSectionBlocksId1(buttonsSectionBlocksId1: any) {
        throw new Error('Method not implemented.');
    }
    buttonsSectionBlock(arg0: any, buttonsSectionBlock: any) {
        throw new Error('Method not implemented.');
    }

    brandheaderimageurl(arg0: any, brandheaderimageurl: any) {
        throw new Error('Method not implemented.');
    }
    brandheadercolumnContent(arg0: any, brandheadercolumnContent: any) {
        throw new Error('Method not implemented.');
    }

    getTextbanner() {
        if (this.brandresourceres.fields.hasOwnProperty("dynamicSystemMessages")) {
            this.dynamicMsg2_id = this.brandresourceres.fields.dynamicSystemMessages.sys.id;
            this.contentfulservice.getdata(this.dynamicMsg2_id).subscribe(res => {
                let dynamicMsg2 = res.fields.messages[0].sys.id
                this.contentfulservice.getdata(dynamicMsg2).subscribe(res => {
                    if (res.fields.hasOwnProperty("dynamicMessageLayoutType")) {
                        this.dynamicMessageLayoutType = res.fields.dynamicMessageLayoutType;
                    } else { console.log("dynamicMessageLayoutType not exists") }

                    if (res.fields.hasOwnProperty("heading")) {
                        this.Textbanneronly = documentToHtmlString(res.fields.heading);
                    } else { console.log("heading not exists") }

                    if (res.fields.hasOwnProperty("headingMobile")) {
                        this.Textmobilebanneronly = documentToHtmlString(res.fields.headingMobile);
                    } else { console.log("headingMobile not exists") }

                    if (res.fields.hasOwnProperty("referencesMobile")) {
                        this.Textmobilebannerref = documentToHtmlString(res.fields.referencesMobile);
                    } else { console.log("referencesMobile not exists") }

                    if (res.fields.hasOwnProperty("references")) {
                        this.Textbannerref = documentToHtmlString(res.fields.references);
                    } else { console.log("references not exists") }

                    if (res.fields.hasOwnProperty("text")) {
                        this.Textbanneronlyb = documentToHtmlString(res.fields.text);
                    } else { console.log("text not exists") }

                    if (res.fields.hasOwnProperty("textMobile")) {
                        this.Textmobilebanneronlyb = documentToHtmlString(res.fields.textMobile);
                    } else { console.log("textMobile not exists") }

                    if (res.fields.hasOwnProperty("imagePlacement")) {
                        this.imagePlacement = res.fields.imagePlacement;
                    } else { console.log("imagePlacement not exists") }

                    if (res.fields.hasOwnProperty("imageMobilePlacement")) {
                        this.imageMobilePlacement = res.fields.imageMobilePlacement;
                    } else { console.log("imageMobilePlacement not exists") }

                    if (res.fields.hasOwnProperty("headingFontSize")) {
                        this.headingFontSize = res.fields.headingFontSize;
                    } else { console.log("headingFontSize not exists") }

                    if (res.fields.hasOwnProperty("referencesFontColor")) {
                        this.referencesFontColor = res.fields.referencesFontColor.value;
                    } else { console.log("referencesFontColor not exists") }

                    if (res.fields.hasOwnProperty("referencesFontSize")) {
                        this.referencesFontSize = res.fields.referencesFontSize;
                    } else { console.log("referencesFontSize not exists") }

                    if (res.fields.hasOwnProperty("backgroundColor")) {
                        this.bannerbg = res.fields.backgroundColor.value;
                    } else { console.log("backgroundColor not exists") }

                    if (res.fields.hasOwnProperty("textFontColor")) {
                        this.imgtextFontColor = res.fields.textFontColor.value;
                    } else { console.log("textFontColor not exists") }

                    if (res.fields.hasOwnProperty("dyanamicMessagingWidth")) {
                        this.txtbannerwidth = res.fields.dyanamicMessagingWidth;
                    } else { console.log("dyanamicMessagingWidth not exists") }

                    if (res.fields.hasOwnProperty("textAlignment")) {
                        this.textbanneronlyb = res.fields.textAlignment;
                    } else { console.log("textAlignment not exists") }

                    if (res.fields.hasOwnProperty("headingAlignment")) {
                        this.headingAlignment = res.fields.headingAlignment;
                    } else { console.log("headingAlignment not exists") }

                    if (res.fields.hasOwnProperty("textAlignmentMobile")) {
                        this.textmobilebanneronlyb = res.fields.textAlignmentMobile;
                    } else { console.log("textAlignmentMobile not exists") }

                    if (res.fields.hasOwnProperty("headingAlignmentMobile")) {
                        this.headingAlignmentMobile = res.fields.headingAlignmentMobile;
                    } else { console.log("headingAlignmentMobile not exists") }

                    if (res.fields.hasOwnProperty("referencesAlignment")) {
                        this.referencesAlignment = res.fields.referencesAlignment;
                    } else { console.log("referencesAlignment not exists") }

                    if (res.fields.hasOwnProperty("referencesAlignmentMobile")) {
                        this.referencesAlignmentMobile = res.fields.referencesAlignmentMobile;
                    } else { console.log("referencesAlignmentMobile not exists") }

                    if (res.fields.hasOwnProperty("textFontSize")) {
                        this.textFontSize = res.fields.textFontSize;
                    } else { console.log("textFontSize not exists") }

                    if (res.fields.hasOwnProperty("textFontSizeMobile")) {
                        this.textFontSizeMobile = res.fields.textFontSizeMobile;
                    } else { console.log("textFontSizeMobile not exists") }

                    if (res.fields.hasOwnProperty("textimageMobileTextPadding")) {
                        this.textimageMobileTextPadding = res.fields.textimageMobileTextPadding;
                    } else { console.log("textimageMobileTextPadding not exists") }

                    if (res.fields.hasOwnProperty("textimageTextPadding")) {
                        this.textimageTextPadding = res.fields.textimageTextPadding;
                    } else { console.log("textimageTextPadding not exists") }

                    if (res.fields.hasOwnProperty("textBannerPadding")) {
                        this.textBannerPadding = res.fields.textBannerPadding;
                    } else { console.log("textBannerPadding not exists") }

                    if (res.fields.hasOwnProperty("textBannerPaddingMobile")) {
                        this.textBannerPaddingMobile = res.fields.textBannerPaddingMobile;
                    } else { console.log("textBannerPaddingMobile not exists") }

                    document.documentElement.style.setProperty('--textFontSizeMobile', this.textFontSizeMobile);
                    document.documentElement.style.setProperty('--textFontSize', this.textFontSize);
                    document.documentElement.style.setProperty('--referencesAlignmentMobile', this.referencesAlignmentMobile);
                    document.documentElement.style.setProperty('--referencesAlignment', this.referencesAlignment);
                    document.documentElement.style.setProperty('--headingAlignment', this.headingAlignment);
                    document.documentElement.style.setProperty('--textmobilebanneronlyb', this.textmobilebanneronlyb);
                    document.documentElement.style.setProperty('--headingAlignmentMobile', this.headingAlignmentMobile);
                    document.documentElement.style.setProperty('--textbanneronlyb', this.textbanneronlyb);
                    document.documentElement.style.setProperty('--txtbannerwidth', this.txtbannerwidth);
                    document.documentElement.style.setProperty('--bannerbg', this.bannerbg);
                    document.documentElement.style.setProperty('--textFontColor', this.imgtextFontColor);
                    document.documentElement.style.setProperty('--referencesFontColor', this.referencesFontColor);
                    document.documentElement.style.setProperty('--headingFontSize', this.headingFontSize);
                    document.documentElement.style.setProperty('--referencesFontSize', this.referencesFontSize);

                    if (res.fields.hasOwnProperty("headingFontSizeMobile")) {
                        this.headingFontSizeMobile = res.fields.headingFontSizeMobile;
                    } else { console.log("headingFontSizeMobile not exists") }

                    if (res.fields.hasOwnProperty("referencesFontSizeMobile")) {
                        this.referencesFontSizeMobile = res.fields.referencesFontSizeMobile;
                    } else { console.log("referencesFontSizeMobile not exists") }

                    document.documentElement.style.setProperty('--headingFontSizeMobile', this.headingFontSizeMobile);
                    document.documentElement.style.setProperty('--referencesFontSizeMobile', this.referencesFontSizeMobile);
                });
            });
        } else {
            console.log("dynamicSystemMessages not exists");
        }
    }

    getTextImgbanner() {
        if (this.brandresourceres.fields.hasOwnProperty("dynamicSystemMessages")) {
            this.dynamicMsg2_id = this.brandresourceres.fields.dynamicSystemMessages.sys.id;
            this.contentfulservice.getdata(this.dynamicMsg2_id).subscribe(res => {
                let dynamicMsg2 = res.fields.messages[0].sys.id;
                this.contentfulservice.getdata(dynamicMsg2).subscribe(res => {
                    if (res.fields.hasOwnProperty("image")) {
                        let dynamicMsg3 = res.fields.image.sys.id;
                        this.contentfulservice.getassets(dynamicMsg3).subscribe(res => {
                            if (res.fields.hasOwnProperty("file")) {
                                this.imageText = res.fields.file.url;
                                if (res.fields.file.hasOwnProperty("details") && res.fields.file.details.hasOwnProperty("image")) {
                                    this.imagewidth = res.fields.file.details.image.width;
                                    this.imageheight = res.fields.file.details.image.height;
                                    document.documentElement.style.setProperty('--imageheight', this.imageheight);
                                    document.documentElement.style.setProperty('--imagewidth', this.imagewidth);
                                    console.log('imageheight', this.imageheight);
                                } else { console.log("image details not exists") }
                            } else { console.log("file not exists") }
                        });
                    } else { console.log("image not exists") }

                    if (res.fields.hasOwnProperty("imageMobile")) {
                        let dynamicMsg4 = res.fields.imageMobile.sys.id;
                        this.contentfulservice.getassets(dynamicMsg4).subscribe(res => {
                            if (res.fields.hasOwnProperty("file")) {
                                this.imageMobile = res.fields.file.url;
                                if (res.fields.file.hasOwnProperty("details") && res.fields.file.details.hasOwnProperty("image")) {
                                    this.imageMobilewidth = res.fields.file.details.image.width;
                                    this.imageMobileheight = res.fields.file.details.image.height;
                                    document.documentElement.style.setProperty('--imageMobilewidth', this.imageMobilewidth);
                                    document.documentElement.style.setProperty('--imageMobileheight', this.imageMobileheight);
                                } else { console.log("imageMobile details not exists") }
                            } else { console.log("imageMobile file not exists") }
                        });
                    } else { console.log("imageMobile not exists") }
                });
            });
        } else {
            console.log("dynamicSystemMessages not exists");
        }
    }

    getImagebanner() {
        if (this.brandresourceres.fields.hasOwnProperty("dynamicSystemMessages")) {
            this.dynamicMsg2_id = this.brandresourceres.fields.dynamicSystemMessages.sys.id;
            console.log("this.dynamicMsg2_id", this.dynamicMsg2_id);
            this.contentfulservice.getdata(this.dynamicMsg2_id).subscribe(res => {
                let dynamicMsg2 = res.fields.messages[0].sys.id
                console.log(dynamicMsg2);
                this.contentfulservice.getdata(dynamicMsg2).subscribe(res => {
                    if (res.fields.hasOwnProperty("imageBanner")) {
                        let dynamicMsg3 = res.fields.imageBanner.sys.id
                        console.log('dynamicMsg3', dynamicMsg3)
                    } else { console.log("imageBanner not exists") }

                    if (res.fields.hasOwnProperty("imageBannerMobile")) {
                        let dynamicMsg4 = res.fields.imageBannerMobile.sys.id
                        console.log('dynamicMsg4', dynamicMsg4)
                    } else { console.log("imageBannerMobile not exists") }

                    if (res.fields.hasOwnProperty("dynamicMessageLayoutType")) {
                        this.dynamicMessageLayoutType = res.fields.dynamicMessageLayoutType
                        console.log('this.dynamicMessageLayoutType', this.dynamicMessageLayoutType)
                    } else { console.log("dynamicMessageLayoutType not exists") }

                    if (res.fields.hasOwnProperty("imageBanner")) {
                        let dynamicMsg3 = res.fields.imageBanner.sys.id
                        this.contentfulservice.getassets(dynamicMsg3).subscribe(res => {
                            console.log('res', res)
                            if (res.fields.hasOwnProperty("file")) {
                                this.imageBanner = res.fields.file.url;
                                if (res.fields.file.hasOwnProperty("details") && res.fields.file.details.hasOwnProperty("image")) {
                                    this.ImageBannerheight = res.fields.file.details.image.height;
                                    this.ImageBannerwidth = res.fields.file.details.image.width;

                                    document.documentElement.style.setProperty('--ImageBannerwidth', this.ImageBannerwidth);
                                    document.documentElement.style.setProperty('--ImageBannerheight', this.ImageBannerheight);
                                    console.log("imageBanner", this.imageBanner);
                                } else { console.log("imageBanner details not exists") }
                            } else { console.log("imageBanner file not exists") }
                        });
                    } else { console.log("imageBanner not exists") }

                    if (res.fields.hasOwnProperty("imageBannerMobile")) {
                        let dynamicMsg4 = res.fields.imageBannerMobile.sys.id
                        this.contentfulservice.getassets(dynamicMsg4).subscribe(res => {
                            console.log('res1', res)
                            if (res.fields.hasOwnProperty("file")) {
                                this.imageBannerMobile = res.fields.file.url;
                                console.log("imageBanner11", this.imageBannerMobile);
                            } else { console.log("imageBannerMobile file not exists") }
                        });
                    } else { console.log("imageBannerMobile not exists") }
                });
            });
        } else { console.log("dynamicSystemMessages img not exits") }
    }

    getTopHeading() {
        if (this.dynamicmssgres.fields.hasOwnProperty("backgroundColor")) {
            this.heading_backgroundColor = this.dynamicmssgres.fields.backgroundColor.value;
            document.documentElement.style.setProperty('--heading_backgroundColor', this.heading_backgroundColor);
        } else { console.log("backgroundColor not exists") }

        if (this.dynamicmssgres.fields.hasOwnProperty("padding")) {
            this.copypadding = this.dynamicmssgres.fields.padding;
            document.documentElement.style.setProperty('--copypadding', this.copypadding);
        } else { console.log("padding not exists") }

        if (this.dynamicmssgres.fields.hasOwnProperty("paddingMobile")) {
            this.copypaddingMobile = this.dynamicmssgres.fields.paddingMobile;
            document.documentElement.style.setProperty('--copypaddingMobile', this.copypaddingMobile);
        } else { console.log("paddingMobile not exists") }

        if (this.dynamicmssgres.fields.hasOwnProperty("margin")) {
            this.copymargin = this.dynamicmssgres.fields.margin;
            document.documentElement.style.setProperty('--copymargin', this.copymargin);
        } else { console.log("margin not exists") }

        if (this.dynamicmssgres.fields.hasOwnProperty("marginMobile")) {
            this.copymarginMobile = this.dynamicmssgres.fields.marginMobile;
            document.documentElement.style.setProperty('--copymarginMobile', this.copymarginMobile);
        } else { console.log("marginMobile not exists") }

        if (this.dynamicmssgres.fields.hasOwnProperty("iconBackgroundColor")) {
            this.heading_iconBackgroundColor = this.dynamicmssgres.fields.iconBackgroundColor.value;
            document.documentElement.style.setProperty('--heading_iconBackgroundColor', this.heading_iconBackgroundColor);
        } else { console.log("iconBackgroundColor not exists") }

        if (this.dynamicmssgres.fields.hasOwnProperty("iconBackgroundBorderRadius")) {
            this.heading_iconRadius = this.dynamicmssgres.fields.iconBackgroundBorderRadius;
            document.documentElement.style.setProperty('--heading_iconRadius', this.heading_iconRadius);
        } else { console.log("iconBackgroundBorderRadius not exists") }

        if (this.dynamicmssgres.fields.hasOwnProperty("collapsibleDefaultPosition")) {
            this.collapsibleDefaultPosition = this.dynamicmssgres.fields.collapsibleDefaultPosition;
            this.isDropdownOpen2 = this.collapsibleDefaultPosition === 'Closed' ? true : false;
        } else { console.log("collapsibleDefaultPosition not exists") }

        if (this.dynamicmssgres.fields.hasOwnProperty("openIcon")) {
            this.heading_openIcon = this.dynamicmssgres.fields.openIcon.sys.id;
            this.contentfulservice.getassets(this.heading_openIcon).subscribe(res => {
                this.arrowdown = res.fields.file.url;
            });

            if (this.dynamicmssgres.fields.hasOwnProperty("iconPadding")) {
                this.iconPadding = this.dynamicmssgres.fields.iconPadding;
                document.documentElement.style.setProperty('--iconPadding', this.iconPadding);
            } else { console.log("iconPadding not exists") }

            if (this.dynamicmssgres.fields.hasOwnProperty("iconMargin")) {
                this.iconMargin = this.dynamicmssgres.fields.iconMargin;
                document.documentElement.style.setProperty('--iconMargin', this.iconMargin);
            } else { console.log("iconMargin not exists") }

            if (this.dynamicmssgres.fields.hasOwnProperty("iconWidth")) {
                this.iconWidth = this.dynamicmssgres.fields.iconWidth;
                document.documentElement.style.setProperty('--iconWidth', this.iconWidth);
            } else { console.log("iconWidth not exists") }

            if (this.dynamicmssgres.fields.hasOwnProperty("iconHeight")) {
                this.iconHeight = this.dynamicmssgres.fields.iconHeight;
                document.documentElement.style.setProperty('--iconHeight', this.iconHeight);
            } else { console.log("iconHeight not exists") }

            if (this.dynamicmssgres.fields.hasOwnProperty("iconWidthMobile")) {
                this.iconWidthMobile = this.dynamicmssgres.fields.iconWidthMobile;
                document.documentElement.style.setProperty('--iconWidthMobile', this.iconWidthMobile);
            } else { console.log("iconWidthMobile not exists") }

            if (this.dynamicmssgres.fields.hasOwnProperty("iconHeightMobile")) {
                this.iconHeightMobile = this.dynamicmssgres.fields.iconHeightMobile;
                document.documentElement.style.setProperty('--iconHeightMobile', this.iconHeightMobile);
            } else { console.log("iconHeightMobile not exists") }

            if (this.dynamicmssgres.fields.hasOwnProperty("dyanamicMessagingWidth")) {
                this.dyanamicMessagingWidth = this.dynamicmssgres.fields.dyanamicMessagingWidth;
            } else { console.log("dyanamicMessagingWidth not exists") }
        } else { console.log("openIcon not exists") }

        if (this.dynamicmssgres.fields.hasOwnProperty("closeIcon")) {
            this.heading_closeIcon = this.dynamicmssgres.fields.closeIcon.sys.id;
            this.contentfulservice.getassets(this.heading_closeIcon).subscribe(res => {
                this.arrowup = res.fields.file.url;
            });
        } else { console.log("closeIcon not exists") }

        if (this.dynamicmssgres.fields.hasOwnProperty("messages") && this.dynamicmssgres.fields.messages[0] && this.dynamicmssgres.fields.messages[0].sys && this.dynamicmssgres.fields.messages[0].sys.id) {
            this.heading_id = this.dynamicmssgres.fields.messages[0].sys.id;
            this.contentfulservice.getdatapreview(this.heading_id).subscribe(res => {
                console.log("this.dynamicmssgres", res.fields);

                if (res.fields.hasOwnProperty("heading")) {
                    this.heading = documentToHtmlString(res.fields.heading);
                } else { console.log("heading not exists") }

                if (res.fields.hasOwnProperty("headingMobile")) {
                    this.headingMobile = documentToHtmlString(res.fields.headingMobile);
                } else { console.log("headingMobile not exists") }

                if (this.dyanamicMessagingWidth) {
                    document.documentElement.style.setProperty('--dyanamicMessagingWidth', this.dyanamicMessagingWidth);
                }

                if (res.fields.hasOwnProperty("headingAlignment")) {
                    this.topheadingAlignment = res.fields.headingAlignment;
                    document.documentElement.style.setProperty('--topheadingAlignment', this.topheadingAlignment);
                } else { console.log("headingAlignment not exists") }

                if (res.fields.hasOwnProperty("headingAlignmentMobile")) {
                    this.topheadingAlignmentMobile = res.fields.headingAlignmentMobile;
                    document.documentElement.style.setProperty('--topheadingAlignmentMobile', this.topheadingAlignmentMobile);
                } else { console.log("headingAlignmentMobile not exists") }

                if (res.fields.hasOwnProperty("headingFontSize")) {
                    this.headingFontSize = res.fields.headingFontSize;
                    document.documentElement.style.setProperty('--headingFontSize', this.headingFontSize);
                } else { console.log("headingFontSize not exists") }

                if (res.fields.hasOwnProperty("headingFontSizeMobile")) {
                    this.headingFontSizeMobile = res.fields.headingFontSizeMobile;
                    document.documentElement.style.setProperty('--headingFontSizeMobile', this.headingFontSizeMobile);
                } else { console.log("headingFontSizeMobile not exists") }

                if (res.fields.hasOwnProperty("headingFontColor")) {
                    this.headingFontColor = res.fields.headingFontColor.value;
                    document.documentElement.style.setProperty('--headingFontColor', this.headingFontColor);
                } else { console.log("headingFontColor not exists") }
            })
        } else { console.log("messages[0] not exists") }
    }

    getTopHeadinglist() {
        console.log("this.dynamicmssgres", this.dynamicmssgres)
        this.heading_id = this.dynamicmssgres.fields.messages[0].sys.id;
        this.contentfulservice.getdatapreview(this.heading_id).subscribe(res => {
            this.headinglist = documentToHtmlString(res.fields.text);
            this.textMobile = documentToHtmlString(res.fields.textMobile);

            if (res.fields.hasOwnProperty("textAlignment")) {
                this.textAlignment = res.fields.textAlignment
                document.documentElement.style.setProperty('--textAlignment', this.textAlignment);
            } else { console.log("textAlignment not exists") }
            if (res.fields.hasOwnProperty("textAlignmentMobile")) {
                this.textAlignmentMobile = res.fields.textAlignmentMobile
                document.documentElement.style.setProperty('--textAlignmentMobile', this.textAlignmentMobile);
            } else { console.log("textAlignmentMobile not exists") }
            if (res.fields.hasOwnProperty("textFontSize")) {
                this.textFontSize1 = res.fields.textFontSize
                document.documentElement.style.setProperty('--textFontSize1', this.textFontSize1);
            } else { console.log("textFontSize not exists") }
            if (res.fields.hasOwnProperty("textFontSizeMobile")) {
                this.textFontSizeMobile1 = res.fields.textFontSizeMobile
                document.documentElement.style.setProperty('--textFontSizeMobile1', this.textFontSizeMobile1);
            } else { console.log("textFontSizeMobile not exists") }
            if (res.fields.hasOwnProperty("textLineHeight")) {
                this.textLineHeight = res.fields.textLineHeight
                document.documentElement.style.setProperty('--textLineHeight', this.textLineHeight);
            } else { console.log("textLineHeight not exists") }
            if (res.fields.hasOwnProperty("textLineHeightMobile")) {
                this.textLineHeightMobile = res.fields.textLineHeightMobile
                document.documentElement.style.setProperty('--textLineHeightMobile', this.textLineHeightMobile);
            } else { console.log("textLineHeightMobile not exists") }
        });
    }

    getTopDropdown() {
        this.list_id = this.dynamicmssgres.fields.messages[1].sys.id;
        this.contentfulservice.getdatapreview(this.list_id).subscribe(res => {
            if (res.fields.messagecontent.content[0].content[0].value !== undefined) {
                this.firstList = res.fields.messagecontent.content[0].content[0].value
            }
            if (res.fields.messagecontent.content[0].content[1].content[0].value !== undefined) {
                this.firstListUrlValue = res.fields.messagecontent.content[0].content[1].content[0].value
            }
            if (res.fields.messagecontent.content[0].content[1].data.uri !== undefined) {
                this.firstListUrl = res.fields.messagecontent.content[0].content[1].data.uri
            }
            if (res.fields.messagecontent.content[0].content[2].value !== undefined) {
                this.firstListValue = res.fields.messagecontent.content[0].content[2].value
            }
        })
    }

    public getStartedContent: any;
    getTopListed() {
        this.list1_id = this.dynamicmssgres.fields.messages[0].sys.id;
        this.contentfulservice.getdatapreview(this.list1_id).subscribe(res => {
            this.getStartedContent = res.fields.copyBlock;
            this.ginacontent = this.getStartedContent.content[0].content[2].content[0].content[1].value
        })
    }

    getTopListedDemo() {
        this.list2_id = this.dynamicmssgres.fields.messages[2].sys.id;
        this.contentfulservice.getdatapreview(this.list2_id).subscribe(res => {
            if (res.fields.messagecontent.content[0].content[1].content[0].value !== undefined) {
                this.secondList = res.fields.messagecontent.content[0].content[1].content[0].value
            }
            if (res.fields.messagecontent.content[0].content[1].data.uri !== undefined) {
                this.secondListUrl = res.fields.messagecontent.content[0].content[1].data.uri
            }
            if (res.fields.messagecontent.content[0].content[2].value !== undefined) {
                this.secondListValue = res.fields.messagecontent.content[0].content[2].value
            }
            if (res.fields.messagecontent.content[0].content[3].content[0].value !== undefined) {
                this.secondListValue2 = res.fields.messagecontent.content[0].content[3].content[0].value
            }
            if (res.fields.messagecontent.content[0].content[3].data.uri !== undefined) {
                this.secondListValue2Url = res.fields.messagecontent.content[0].content[3].data.uri;
            }
            if (res.fields.messagecontent.content[0].content[4].value !== undefined) {
                this.secondListValue2Sl = res.fields.messagecontent.content[0].content[4].value
            }
            if (res.fields.messagecontent.content[0].content[5].content[0].value !== undefined) {
                this.secondListValue2Tl = res.fields.messagecontent.content[0].content[5].content[0].value
            }
            if (res.fields.messagecontent.content[0].content[5].data.uri !== undefined) {
                this.secondListValue2TlUrl = res.fields.messagecontent.content[0].content[5].data.uri
            }
            if (res.fields.messagecontent.content[0].content[6].value !== undefined) {
                this.secondListValue2Fl = res.fields.messagecontent.content[0].content[6].value
            }
        })
    }

    getTopListed2() {
        this.list3_id = this.dynamicmssgres.fields.messages[3].sys.id;
        this.contentfulservice.getdatapreview(this.list3_id).subscribe(res => {
            this.thirdList = res.fields.messagecontent.content[0].content[0].value
        })
    }

    getContent() {
        if (this.brandresourceres.fields.hasOwnProperty("healthToolTrigger")) {
            this.paraContent_id = this.brandresourceres.fields.healthToolTrigger.sys.id;
            this.contentfulservice.getdatapreview(this.paraContent_id).subscribe(res => {
                if (res.fields.hasOwnProperty("triggerCopy")) {
                    this.paraContent = res.fields.triggerCopy;
                } else { console.log("triggerCopy not exists") }
            })
        } else { console.log("triggerCopy not exists") }
    }

    getprescribingInformation() {
        if (this.brandresourceres.fields.hasOwnProperty('prescribingInformation')) {
            this.presinfo_id = this.brandresourceres.fields.prescribingInformation.sys.id;
            this.contentfulservice.getdatapreview(this.presinfo_id).subscribe(res => {
                if (res.fields.hasOwnProperty('prescribingInformation')) {
                    let p_id = res.fields.prescribingInformation.sys.id
                    this.contentfulservice.getdatapreview(p_id).subscribe(res => {
                        if (res.fields.hasOwnProperty('prescribingInformationPdf')) {
                            let ppdf_id = res.fields.prescribingInformationPdf.sys.id
                            this.presinternalname = res.fields.internalName;
                            this.contentfulservice.getAssetspreview('/' + ppdf_id + '/').subscribe(res => {
                                this.p_file = res.fields.file.url;
                            })
                        } else {
                            if (res.fields.hasOwnProperty('prescribingInformationLink')) {
                                this.p_file = res.fields.prescribingInformationLink
                            }
                            console.log("prescribingInformationPdf not exists")
                        }
                    })
                } else { console.log("prescribingInformation not exists") }
            })
        } else {
            console.log("prescribingInformation not exists")
        }
    }


    tabURl = ''
    getTabUrl() {
        this.clipboardService.copyFromContent(window.location.href)
        this.tabURl = window.location.href
        if (this.tabURl != '') {
            this.toast.success('URL Copied to ClipBoard')
        } else {
            this.toast.error('something went wrong')
        }
    }

    public options: any = {
        renderNode: {
            [INLINES.HYPERLINK]: (node, next) => {
                if (node.data.uri === "isi") {
                    // Use onclick instead of (click) for HTML string
                    return `<a href="javascript:void(0)" onclick="document.getElementById('contentbox').scrollIntoView({behavior: 'smooth'})">${next(node.content)}</a>`;
                }
                return `<a href="${node.data.uri}" target="_blank" rel="noopener noreferrer">${next(node.content)}</a>`;
            },
            [BLOCKS.PARAGRAPH]: (node, next) => `<p>${next(node.content).replace(/\n/g, '<br/>')}</p>`,
        }
    }


    _returnHtmlFromRichText(richText) {
        if (richText === undefined || richText === null || richText.nodeType !== 'document') {
            return '<p>Loading</p>';
        } else {
            return documentToHtmlString(richText, this.options);
        }
    }

    getColor() {

        if (this.brandresourceres.fields.hasOwnProperty("branding")) {
            let branding = this.brandresourceres.fields.branding.sys.id;
            this.contentfulservice.getdata(branding).subscribe(res => {
                if (res.fields.hasOwnProperty("primaryColor")) { this.resourceprimarycolor = res.fields.primaryColor.value; } else { console.log("resourceprimarycolor not exists") }
                if (res.fields.hasOwnProperty("secondaryColor")) { this.resourcesecondarycolor = res.fields.secondaryColor.value; } else { console.log("resourcesecondarycolor not exists") }
                if (res.fields.hasOwnProperty("brandHeaderBackgroundGradientColor1")) { this.gradient1 = res.fields.brandHeaderBackgroundGradientColor1.value } else { console.log("brandHeaderBackgroundGradientColor1 not exists") }
                if (res.fields.hasOwnProperty("brandHeaderBackgroundGradientColor2")) { this.gradient2 = res.fields.brandHeaderBackgroundGradientColor2.value } else { console.log("brandHeaderBackgroundGradientColor2 not exists") }
                if (res.fields.hasOwnProperty("brandHeaderBackgroundGradientColor3")) { this.gradient3 = res.fields.brandHeaderBackgroundGradientColor3.value } else { console.log("brandHeaderBackgroundGradientColor3 not exists") }
                if (res.fields.hasOwnProperty("brandFooterBackgroundGradientColor1")) { this.footgradient1 = res.fields.brandFooterBackgroundGradientColor1.value } else { console.log("brandFooterBackgroundGradientColor1 not exists") }
                if (res.fields.hasOwnProperty("brandFooterBackgroundGradientColor2")) { this.footgradient2 = res.fields.brandFooterBackgroundGradientColor2.value } else { console.log("brandFooterBackgroundGradientColor2 not exists") }
                if (res.fields.hasOwnProperty("fontColor")) { this.resourcefontColor = res.fields.fontColor.value } else { console.log("resourcefontColor not exists") }
                if (res.fields.hasOwnProperty("fontLinkColor")) { this.resourcefontLinkColor = res.fields.fontLinkColor.value } else { console.log("fontLinkColor not exists") }
                if (res.fields.hasOwnProperty("fontLinkRolloverColor")) { this.resourcefontLinkRolloverColor = res.fields.fontLinkRolloverColor.value } else { console.log("fontLinkRolloverColor not exists") }
                if (res.fields.hasOwnProperty("h1Color")) { this.resourceh1color = res.fields.h1Color.value } else { console.log("h1Color not exists") }
                if (res.fields.hasOwnProperty("horizontalRule")) { this.resourcehorizontalRule = res.fields.horizontalRule.value } else { console.log("horizontalRule not exists") }
                if (res.fields.hasOwnProperty("buttonBackgroundRollOverColor")) { this.resourcebuttonBackgroundRollOverColor = res.fields.buttonBackgroundRollOverColor.value } else { console.log("buttonBackgroundRollOverColor not exists") }
                if (res.fields.hasOwnProperty("buttonBackgroundColor")) { this.resourcebuttonBackgroundColor = res.fields.buttonBackgroundColor.value } else { console.log("buttonBackgroundColor not exists") }
                if (res.fields.hasOwnProperty("buttonFontColor")) { this.resourcebuttonFontColor = res.fields.buttonFontColor.value } else { console.log("buttonFontColor not exists") }
                if (res.fields.hasOwnProperty("buttonBackgroundRollOverColor")) { this.resourcebuttonBackgroundRollOverColor = res.fields.buttonBackgroundRollOverColor.value } else { console.log("buttonBackgroundRollOverColor not exists") }
                if (res.fields.hasOwnProperty("buttonRolloverFontColor")) { this.resourcebuttonRolloverFontColor = res.fields.buttonRolloverFontColor.value } else { console.log("buttonRolloverFontColor not exists") }
                if (res.fields.hasOwnProperty("brandFooterBackgroundColor")) { this.resourcebrandFooterBackgroundColor = res.fields.brandFooterBackgroundColor.value } else { console.log("brandFooterBackgroundColor not exists") }
                if (res.fields.hasOwnProperty("resourceHeaderColor")) { this.resourceHeaderColor = res.fields.resourceHeaderColor.value } else { console.log("resourceHeaderColor not exists") }


                if (res.fields.hasOwnProperty("headerIndicationHeaderFontSize")) { this.headerIndicationHeaderFontSize = res.fields.headerIndicationHeaderFontSize } else { console.log("headerIndicationHeaderFontSize not exists") }
                if (res.fields.hasOwnProperty("headerIndicationCopyFontSize")) { this.headerIndicationCopyFontSize = res.fields.headerIndicationCopyFontSize } else { console.log("headerIndicationCopyFontSize not exists") }
                if (res.fields.hasOwnProperty("headerIndicationFontColor")) { this.headerIndicationFontColor = res.fields.headerIndicationFontColor.value } else { console.log("headerIndicationFontColor not exists") }
                if (res.fields.hasOwnProperty("isiHeadersFontSize")) { this.isiHeadersFontSize = res.fields.isiHeadersFontSize } else { console.log("isiHeadersFontSize not exists") }
                if (res.fields.hasOwnProperty("isiHeadersColors")) { this.isiHeadersColors = res.fields.isiHeadersColors.value } else { console.log("isiHeadersColors not exists") }
                if (res.fields.hasOwnProperty("isiTextFontSize")) { this.isiTextFontSize = res.fields.isiTextFontSize } else { console.log("isiTextFontSize not exists") }
                if (res.fields.hasOwnProperty("bodyTextFontSize")) { this.bodyTextFontSize = res.fields.bodyTextFontSize } else { console.log("bodyTextFontSize not exists") }
                if (res.fields.hasOwnProperty("bodyTextFontWeight")) { this.bodyTextFontWeight = res.fields.bodyTextFontWeight } else { console.log("bodyTextFontWeight not exists") }
                if (res.fields.hasOwnProperty("isiTextFontWeight")) { this.isiTextFontWeight = res.fields.isiTextFontWeight } else { console.log("isiTextFontWeight not exists") }

                if (res.fields.hasOwnProperty("bodyTextLineHeight")) { this.bodyTextLineHeight = res.fields.bodyTextLineHeight } else { console.log("bodyTextLineHeight not exists") }
                if (res.fields.hasOwnProperty("isiTextLineHeight")) { this.isiTextLineHeight = res.fields.isiTextLineHeight } else { console.log("isiTextLineHeight not exists") }

                document.documentElement.style.setProperty('--resourceprimarycolor', this.resourceprimarycolor ? this.resourceprimarycolor : "#3254a2");
                document.documentElement.style.setProperty('--resourcesecondarycolor', this.resourcesecondarycolor ? this.resourcesecondarycolor : "#691c32");
                document.documentElement.style.setProperty('--gradient1', this.gradient1);
                document.documentElement.style.setProperty('--gradient2', this.gradient2);
                document.documentElement.style.setProperty('--gradient3', this.gradient3);
                document.documentElement.style.setProperty('--footgradient1', this.footgradient1);
                document.documentElement.style.setProperty('--footgradient2', this.footgradient2);
                document.documentElement.style.setProperty('--resourceLinkColor', this.resourcefontLinkColor ? this.resourcefontLinkColor : "#3254a2");
                document.documentElement.style.setProperty('--resourceLinkRolloverColor', this.resourcefontLinkRolloverColor ? this.resourcefontLinkRolloverColor : "#691c32");
                document.documentElement.style.setProperty('--resourceh1Color', this.resourceh1color ? this.resourceh1color : "#3254a2");
                document.documentElement.style.setProperty('--resourcehorizontalRuleColor', this.resourcehorizontalRule ? this.resourcehorizontalRule : "#3254a2");
                document.documentElement.style.setProperty('--resourcebuttonBackgroundColor', this.resourcebuttonBackgroundColor ? this.resourcebuttonBackgroundColor : "#3254a2");
                document.documentElement.style.setProperty('--resourcebuttonBackgroundRollOverColor', this.resourcebuttonBackgroundRollOverColor ? this.resourcebuttonBackgroundRollOverColor : "#691c32");
                document.documentElement.style.setProperty('--resourcebuttonfontcolor', this.resourcebuttonFontColor ? this.resourcebuttonFontColor : "#ffffff");
                document.documentElement.style.setProperty('--resourcebuttonhoverfontcolor', this.resourcebuttonRolloverFontColor ? this.resourcebuttonRolloverFontColor : "#ffffff");
                document.documentElement.style.setProperty('--footergradientColor', this.resourcebrandFooterBackgroundColor ? this.resourcebrandFooterBackgroundColor : "#3254a2");
                document.documentElement.style.setProperty('--resourceHeaderColor', this.resourceHeaderColor ? this.resourceHeaderColor : "#3254a2");

                document.documentElement.style.setProperty('--headerIndicationHeaderFontSize', this.headerIndicationHeaderFontSize ? this.headerIndicationHeaderFontSize : "19px");
                document.documentElement.style.setProperty('--headerIndicationCopyFontSize', this.headerIndicationCopyFontSize ? this.headerIndicationCopyFontSize : "18px");
                document.documentElement.style.setProperty('--headerIndicationFontColor', this.headerIndicationFontColor ? this.headerIndicationFontColor : "#ffffff");
                document.documentElement.style.setProperty('--isiHeadersFontSize', this.isiHeadersFontSize ? this.isiHeadersFontSize : "1 REM");
                document.documentElement.style.setProperty('--isiHeadersColors', this.isiHeadersColors ? this.isiHeadersColors : "#0072ce");


                document.documentElement.style.setProperty('--isiTextFontSize', this.isiTextFontSize ? this.isiTextFontSize : "1 REM");
                document.documentElement.style.setProperty('--bodyTextFontSize', this.bodyTextFontSize ? this.bodyTextFontSize : "1 REM");

                document.documentElement.style.setProperty('--bodyTextFontWeight', this.bodyTextFontWeight ? this.bodyTextFontWeight : "400");
                document.documentElement.style.setProperty('--isiTextFontWeight', this.isiTextFontWeight ? this.isiTextFontWeight : "400");

                document.documentElement.style.setProperty('--bodyTextLineHeight', this.bodyTextLineHeight ? this.bodyTextLineHeight : "1.25 REM");
                document.documentElement.style.setProperty('--isiTextLineHeight', this.isiTextLineHeight ? this.isiTextLineHeight : "1.25 REM");


                const gradientColors = [this.gradient1, this.gradient2, this.gradient3];
                const gradientString = `linear-gradient(to right, ${gradientColors.join(", ")})`;
                document.documentElement.style.setProperty('--headergradientColor', "#b5b3b4");
                document.getElementById("headgradient").style.background = gradientString;

                document.getElementById("footgradient").style.background = this.resourcebrandFooterBackgroundColor;

            })
        } else { console.log("branding not exists") }

    }



    getdrugrelatedbutton() {
        if (this.brandresourceres.fields.hasOwnProperty("branddiseaseHeader")) {
            this.brandheader_id = this.brandresourceres.fields.branddiseaseHeader.sys.id;
            this.contentfulservice.getdatapreview(this.brandheader_id).subscribe(res => {
                if (res.fields.hasOwnProperty("multiDiseaseQsaLinkstabsWrapper")) {
                    let a = res.fields.multiDiseaseQsaLinkstabsWrapper.sys.id;
                    this.contentfulservice.getdatapreview(a).subscribe(res => {
                        this.realtedbuttonids = res.fields.linkstabsbuttons
                        let buttonids = this.realtedbuttonids.map(id => id.sys.id)
                        let responseDataArray = [];
                        const requests$ = [];

                        let i;

                        for (let i = 0; i < buttonids.length; i++) {
                            const value = buttonids[i];
                            const urlWithQuery = `${value}`;
                            if (urlWithQuery !== undefined) {
                                requests$.push(from(this.contentfulservice.getdatapreview(urlWithQuery)));
                            }
                        }

                        zip(...requests$).pipe(
                            concatMap(responses => {
                                responseDataArray = [...responseDataArray, ...responses];
                                if (responseDataArray.length === buttonids.length) {
                                    this.relatedbuttonarray = responseDataArray;
                                }
                                return [];
                            })
                        ).subscribe();

                    })
                }
            })
        }
    }


    getValidPdfUrls(urls: any[]): any[] {
        const validPdfUrls: any[] = [];

        urls.forEach((url) => {
            if (url.toLowerCase().endsWith('.pdf')) {
                validPdfUrls.push(url);
            } else {
                htmlToPdfmake(url, (pdfDoc) => {
                    const blob = new Blob([pdfDoc], { type: 'application/pdf' });
                    const convertedUrl = URL.createObjectURL(blob);
                    validPdfUrls.push(convertedUrl);
                });
            }
        });

        return validPdfUrls;
    }

    isDropdownOpen = false;
    isDropdownOpen1: boolean;
    isDropdownOpen2: boolean;

    toggleDropdown() {
        this.isDropdownOpen = !this.isDropdownOpen;
    }
    toggleDropdown1() {
        this.isDropdownOpen1 = !this.isDropdownOpen1;
    }
    toggleDropdown2() {
        this.isDropdownOpen2 = !this.isDropdownOpen2;
    }

    // Function to extract "Prescribing Information" text
    private extractPrescribingInformation(textNode: any): string {
        let prescribingInfo = '';

        // Check if the textNode has content
        if (textNode && textNode.content) {
            textNode.content.forEach((block: any) => {
                if (block.nodeType === 'paragraph') {
                    block.content.forEach((inline: any) => {
                        if (inline.nodeType === 'hyperlink' && inline.data.uri) {
                            // Check if the hyperlink text is "Prescribing Information"
                            if (inline.content[0].value === 'Prescribing Information') {
                                prescribingInfo = inline.data.uri; // Get the URL or any other value you need
                            }
                        }
                    });
                }
            });
        }

        return prescribingInfo; // Return the extracted value
    }

    private extractPrescribingInformationtext(textNode: any): string {
        let prescribingInfotext = '';

        // Check if the textNode has content
        if (textNode && textNode.content) {
            textNode.content.forEach((block: any) => {
                if (block.nodeType === 'paragraph') {
                    block.content.forEach((inline: any) => {
                        if (inline.nodeType === 'hyperlink' && inline.data.uri) {
                            // Check if the hyperlink text is "Prescribing Information"
                            if (inline.content[0].value === 'Prescribing Information') {
                                prescribingInfotext = inline.content[0].value; // Get the URL or any other value you need
                            }
                        }
                    });
                }
            });
        }

        return prescribingInfotext; // Return the extracted value
    }
    private extractPrescribingInformationtext1(textNode: any): string {
        let prescribingInfotext = '';

        // Check if the textNode has content
        if (textNode && textNode.content) {
            textNode.content.forEach((block: any) => {
                if (block.nodeType === 'paragraph') {
                    block.content.forEach((inline: any) => {
                        if (inline.nodeType === 'hyperlink' && inline.data.uri) {
                            // Check if the hyperlink text is "Prescribing Information"
                            if (inline.content[0].value === 'Important Safety Information') {
                                prescribingInfotext = inline.content[0].value; // Get the URL or any other value you need
                            }
                        }
                    });
                }
            });
        }

        return prescribingInfotext; // Return the extracted value
    }
    hasValidContent(content: string): boolean {
        if (!content) return false;
        // Remove all HTML tags and check if there's any text content
        const textContent = content.replace(/<[^>]*>/g, '').trim();
        return textContent.length > 0;
    }
    handlePFileClick(event: MouseEvent) {
        let el = event.target as HTMLElement;
        const boundary = event.currentTarget as HTMLElement;
        while (el && el !== boundary) {
          if (el.tagName && el.tagName.toLowerCase() === 'a') {
            const anchor = el as HTMLAnchorElement;
            const text = (anchor.textContent || '').trim();
            if (text === 'Important Safety Information') {
              event.preventDefault();
              this.scrollToElementById('contentbox');
            }
            break;
          }
          el = el.parentElement as HTMLElement;
        }
      }
}

