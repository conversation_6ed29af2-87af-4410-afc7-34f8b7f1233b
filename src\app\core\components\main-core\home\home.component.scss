.container{
    //padding: 10px 0px 10px 0px;
    min-height: calc(100vh - 125px);

    
}
@media (max-width:1200px){
    .container{
        max-width: calc(100vw - 6vw) !important;
        width: 100% !important;
    }
}

@media (min-width:1200px) {
    .container{
      max-width: 1170px;
    }
  }


@media (max-width:768px){
    .container{
        // padding-top: 7%;
        //padding: 10px 25px 10px 25px;
    }
}

body{
    overflow-x: hidden;
}