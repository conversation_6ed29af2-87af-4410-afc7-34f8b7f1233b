import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AngularMaterialModule } from './angular-material.module';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { CoreModule } from './core/core.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FeatureModule } from './features/feature.module';
import { HttpClientModule } from '@angular/common/http';
import { ContentfulService } from './services/contentful.service';
import { PopUpComponent } from './features/pop-up/pop-up.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxHideOnScrollModule } from 'ngx-hide-on-scroll';
import { PdfViewerModule } from 'ng2-pdf-viewer';

@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [  
    BrowserModule,
    AppRoutingModule,
    CoreModule,
    BrowserAnimationsModule,
    AngularMaterialModule,
    FeatureModule,
    HttpClientModule,
    FormsModule,
    ReactiveFormsModule,
    NgxHideOnScrollModule,
    PdfViewerModule,
    
  ],
  providers: [ContentfulService],
  bootstrap: [AppComponent],
  entryComponents:[PopUpComponent]
})
export class AppModule { }
