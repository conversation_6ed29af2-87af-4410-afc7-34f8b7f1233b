// @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;900&display=swap');

.container-fluid{
    //position: relative;
    // left: 1.5rem;
    // top: 3rem;
    width: 100vw; /* make it 100% of the viewport width (vw) */
    margin-left: calc((100% - 100vw) / 2);  /* then remove the gap to the left of the container with this equation */
    padding: 0 !important
}

.disabled-input{
    background: #eaecef;
    padding: 10px;
}

::ng-deep.col-1{
    width: 0.33333%;
}

::ng-deep.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:hover:not([aria-disabled=true]){
   background-color: transparent;
   
}
.text-banner-image.Left {
    float: left;
    margin-right: 10px;
  }
  
  .text-banner-image.Right {
    float: right;
    margin-left: 10px;
  }
  
  .text-banner-image.Above {
    display: block;
    text-align: center;
    margin-bottom: 10px;
  }
  
  .text-banner-image.Below {
    display: block;
    text-align: center;
    margin-top: 10px;
  }
  
  .text-mobile-banner-image.Above {
    display: block; 
    text-align: center;
    margin-bottom: 10px;
  }
  
  .text-mobile-banner-image.Below {
    display: block;
    text-align: center;
    margin-top: 10px;
  }
  
  /* Clear floats after the text blocks */
  .text-banner-content::after {
    content: "";
    display: table;
    clear: both;
  }
  
  .text-mobile-banner-content::after {
    content: "";
    display: table;
    clear: both;
  }
  .mockupimg ,.mockupimgmobile ,.text-banner{
    justify-content: center;
  }
  .mockupimg img{
    width: var(--ImageBannerwidth);
    height:var(--ImageBannerheight)
  }
  .text-banner-image img{
    width: var(--imagewidth);
    height:var(--imageheight);
  }
  .text-mobile-banner-image img{
    width: var(--imageMobilewidth);
    height:var(--imageMobileheight);
  }
@media (max-width:500px) {
    .mockup , .mockupimg ,.text-banner{
        display: none;
    } 
}

@media (min-width:500px) {
    .mockupmobile , .mockupimgmobile, .text-mobile-banner{
        display: none;
    }
}
.mat-expansion-panel-margin{
   // margin-bottom: 10px !important;
}

h4{
    font-weight: 600;
}

#foot_links a{
    color: var(--resourceLinkColor);   
}
 #foot_links :host::ng-deep a{
        color: var(--resourceLinkRolloverColor);   
    
} 

a{
    color: var(--resourceLinkColor);   
    cursor: pointer; 
    font-weight: 500;
}

a:hover{
    color: var(--resourceLinkRolloverColor);
    font-weight: 500;
    cursor: pointer; 
}

#foot_links {
    /* styles for the host element go here */
    :host {
        a{
            color: white 
        
        }
    }
    
    /* styles for child elements go here */
    ::ng-deep {
        a{
            color: white 
        
        }
    }
  }

:host::ng-deep a{
    color: var(--resourceLinkColor);   
    cursor: pointer; 
    text-decoration: none;

}

:host::ng-deep a:hover{
    color: var(--resourceLinkRolloverColor);
    // font-weight: 500;
    text-decoration: none;
    cursor: pointer; 
}

a, u {
    text-decoration: none;
  }

  .para{
    position: relative;
    left: 1rem;
    top: 0.5rem;
    font-size: var(--bodyTextFontSize);
    font-weight: var(--bodyTextFontWeight);
    line-height: var(--bodyTextLineHeight);
    color: #282828;
    font-family: var(--fontStyle);
    width: 100%;
}

.paraA{
    color: var(--resourceLinkColor);   
    cursor: pointer; 
}

.paraA:hover{
    color: var(--resourceLinkRolloverColor);
    cursor: pointer; 
}


.fillable{
    color:var(--resourcesecondarycolor) ;
    position:relative;
    left:1.5rem;
    margin: 20px 0px 20px 0;
}

.copyicon{
    position: relative;
    top: -1rem;
}
.mat-expansion-panel{
    box-shadow: none;
    //width: calc(100vw - 35vw);
    padding-right: 10px;
}
.mat-expansion-panel:hover{
    background-color: #fff;
}
.main-content-resource{
    background-color: #f1f1f1;
    margin-bottom: 80px;
    border-top: 3px solid var(--resourcehorizontalRuleColor);
    //width: 100vw; /* make it 100% of the viewport width (vw) */
    //margin-left: calc((100% - 100vw) / 2);  /* then remove the gap to the left of the container with this equation */
}


// ::ng-deep.mat-content{
//     height: 67%;
// }

.top-content{ 
    width: 100%;
    position: relative;
    //left: 3rem;
}

.pdf-info{
    color: var(--resourceHeaderColor) !important;
    display: flex;
    justify-content: end;
    align-items: center;
    //font-size: 20px;
    font-size: 1.25rem;
    position: relative;
    //left: -2rem;
}
.HizentraLogo{
    position: relative;
    //left: 1.5rem;
    margin-top: 20px;
    margin-bottom: 20px;
}
.brand-msg{
    width: 100%;
    height: 140px;
    border: none;
    background-color: var(--resourcesecondarycolor);
    color: #fff;
    font-weight: 600;
    //font-size: 30px;
    font-size: 1.875rem;
    border-radius: 10px;
    position: relative;
    //left: 3.6rem;
    justify-content: center;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

#demo{
    //margin-left: 60px;
}


.icon-label{
    margin-left: 10px;
    vertical-align: text-top;
}

.info{
    color: var(--resourceprimarycolor);    
    cursor: pointer;
}

.info:hover{
    color: var(--brandYellow);
}

.tool-row{
    justify-content: center;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    
}

.contentbox{
    background-color: rgb(254, 254, 254);
    border-radius: 15px;
    width: 100%;
    position: relative;
    margin-bottom: 20px;
    //left: 3.6rem;
    height: 50%;
    padding: 40px;
    color: var(--resourceprimarycolor);
    //font-size: 22px;
    font-size: 1.375rem;
    font-weight: 500;
}

mat-panel-title{
    color: var(--resourceprimarycolor);    
    //font-size: 22px;
    font-size: 1.375rem;
    font-weight: 500;
}

::ng-deep .mat-expansion-panel-header{
    height: 64px !important;
    // margin-top: 6px;
   // height:auto !important;
}

:host::ng-deep .mat-checkbox-inner-container{
    //display: inline-block;
    //width: 24px;
    //height: 24px;
    //margin-top: 17px;
    margin: 23px 8px 0 auto 
}



#arrow{
    //font-size: 50px;
    font-size: 3.125rem;
    width: 40px;
    color: #686868;
}

 ::ng-deep.mat-checkbox-layout{
    // width: 100%;
 }

.alignRight{
    display: flex;
    margin-right: 40px; 
    position: absolute; 
    right: 0;
    justify-content: center;
}

.viewIconImg{
    width: 35px;
}

.para-text{
    //font-size: 19px;
    font-size: 1.188rem;
    font-weight: 400;
    word-spacing: 2px;
    line-height: 30px;
    font-family: 'Roboto';
}

.button-group{
   position: relative;
//    left: 8rem;
   margin: 25px 0 25px 0;
}

// .example-section{
//     margin-bottom: 10px;
// }

.contentbox2{
    background-color: rgb(254, 254, 254);
    border-radius: 15px;
    width: 100%;
    position: relative;
    //left: 3.6rem;
    padding: 20px 40px;
    padding-bottom: 8px !important;
}

#paraContent{
    font-size: 1.125rem;
    font-weight: 400;
    color: #282828;
    font-family: var(--fontStyle);
    margin: 20px 0 20px 0;
    line-height: 1.875rem;
}

.paraContentText{
    padding: 5px 150px 0px 150px
}

.button-contentbox{
    border-radius: 15px;
    width: 100%;
    position: relative;
    //left: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.content-button{
    width: 140px;
    height: 50px;
    border-radius: 10px;
    font-size: 1.125rem;
    font-weight: 500;
    font-family: var(--fontStyle);
    background-color: var(--resourcebuttonBackgroundColor);
    color: var(--resourcebuttonfontcolor);
}

.content-button p{
    margin: 0;
}

.btn-close{
    justify-content: flex-end;
    display: flex;
    padding-right: 33px;
    padding-bottom: 33px;
}

.content-button:hover{
    background-color: var(--resourcebuttonBackgroundRollOverColor);
}

.box{
    margin-top: 20px;
}

.modal-dialog{
    display: flex;
    justify-content: center;
}

.modal-content{
 width: 70%;
}

.modal-contents{
 width: 100%;
}

.modal-footer{
    justify-content: center;
    padding: 10px;
    border: none;
}

.msg_button{
    padding: 10px 20px;
    //font-size: 22px;
    font-size: 1.375rem;
    border-radius: 7px;
}

#dynamicMail{
    resize: both;
    // overflow: auto;
    word-wrap: break-word;
}

.contentbox3{
    background-color: rgb(254, 254, 254);
    // width: 90%;
    position: relative;
    // left: 3.6rem;
    top: 0.5rem;
    padding: 10px 0 0 0;
}

:host::ng-deep.mat-checkbox-layout{
    white-space: normal !important;
}
    

.scrolltotop{
    margin: 20px 0 20px 0;
    border: none;
    background: #fff;
}


.fixed-footer {
    width: 100%;
    color: white;
    background-color: var(--backgroundColor);
    padding: 0 2rem;
    transition: bottom 0.3s ease-in-out;
}

.botton-content{
    position: relative;
    top: 2rem;
    margin-bottom: 7rem;
}

.license{
    font-size: 0.9rem;
    position: relative;
    top: 1rem;
}

a:not([href]):not([class]){
    color: var(--resourceLinkColor);

}


 a:not([href]):not([class]):hover{
    color: var(--resourceLinkRolloverColor);
 }

 .header-padding{
    padding: 0 !important;
 }


@media (min-width:1200px) {
    .container{
      //max-width: calc(100vw - 10vw);
      max-width: 1170px;
    }
    .header-margin{
        margin-left:-25px
    }

    #checkbox-mat-panel{
        max-width: 1000px !important;
    }

    .mat-expansion-panel{
        width: calc(100vw - 20vw);
        min-width: 800px;
    }
    .mat-checkbox-inner-container{
        margin: 17px 8px auto 0;
    }
    
  }
 
@media (max-width: 1200px){

    .container{
        position: relative;
        // top: -2rem;
        // left: 0;
        width: 100%;
       // padding-left: 0;
       // padding-right: 0;
       max-width: calc(100vw - 6vw) !important;
    }

    .mat-checkbox-inner-container{
        margin: 23px 8px auto 0;
    }
    
    .mat-expansion-panel{
        width: calc(100vw - 15vw);
    }

    #paraContent{
    font-size: 1rem;
    line-height: 1.75rem;
    }
    .main-content{
        position: relative;
    }

    .top-content{
        display: flex;
        justify-content: center;
        align-self:center;
        position: relative;
        //left: 1.2rem;
    }

    .HizentraLogo{
        position: relative;
        left: 0;
        display: flex;
        justify-content: center;
    }

    .fillable{
       //font-size: 15px;
       font-size: 0.938rem;
       left: 0;
    }  

    :host::ng-deep.mat-expansion-panel-body{
        padding: 0;
     }

    .pdf-info{
        //font-size: 18px;
        color: var(--resourceHeaderColor) !important;
        font-size: 1.125rem;
        position: relative;
        left: 0rem;
        width: 100%;
        text-align: center;
    }

    .info{
        position: relative;
        left: 1rem;
    }

    .paraContentText{
        padding: 0
    }

    .contentbox2{
        padding: 20px;
    }

    .brand-msg{
        position: relative;
        //left: 1rem;
        margin-top: 30px;
        border: none;
        //font-size: 15px;
        font-size: 0.938rem;
        font-weight: 400;
        height: 50px;
    }

    .tool-row{
        display: block;
        position: relative;
        //: 1rem;
    }

    ::ng-deep .mat-expansion-panel-header{
        //height: auto !important;
    }

    .alignRight{
        margin-right: 6px; 
    }

    .viewIconImg{
        width: 19px;
        margin-top:8px;
    }

    .second-tr{
        position: relative;
        left: 7px;
    }

    // .button-group{
    //     position: relative;
    //     left: 5.5rem;
    // }

    .contentbox{
        position: relative;
        //left: 1rem;
        padding: 20px;
    }

    .contentbox2{
        position: relative;
        //left: 1rem;
    }

    .para-text{
        //font-size: 11px;
       // font-size: 0.688rem;
       // line-height: 1;
       font-size: 1rem;
       line-height: 1.75rem;
    }

    // .contentbox3{
    //     position: relative;
    //     left: 1rem;
    //     top: 1rem;
    // }

    .button-contentbox{
        display: block;
        position: relative;
        left: 0;
    }

    .modal-content{
        width: 100%;
       }
    

    .license{
        //font-size: 10px;
        font-size: .625rem;
    }

    .example-margin{
        //font-size: 14px;
        font-size: 0.875rem;
    }

    :host::ng-deep .mat-checkbox-inner-container{
        //display: inline-block;
       // width: 16px;
        //height: 16px;
        //margin-top: 17px;
       
    }

    #viewIcon{
        width: 12.5%;
        text-align: right;
        float: right;
    }

    #viewIcon img{
        width: 25px;
    }

    mat-panel-title{
        //font-size: 11px;
        //font-size: 0.688rem;
        font-size: 1rem;
        line-height: 1.75rem;
        //margin-top: 8px;
        //margin-right: 20% !important;
        
    }

    .mat-expansion-panel-header{
        padding: 0;
    }

    #arrow{
        //font-size: 31px;
        font-size: 1.938rem;
        //width: 25px;
        color: #686868;
        margin-top: 3px;
    }

    #demo{
        margin-left: 10px;
        //display: block;
    }

    #demo .col-2{
        width: 50%;
    }

}

@media (max-width:280px) {
    mat-panel-title{
        //margin-top: 28px;
    }
    mat-panel-title h3{
        font-size: 1.5rem !important;
       // line-height: 1rem;
       // margin-top: 8px;
    }
   
    .header-margin{
        margin-top: 20px;
    }
    .pdf-info{
        font-size: 0.6rem;
    }
    .HizentraLogo img{
        width: 100%;
        height: auto;
    }
    .brand-msg{
        font-size: 0.738rem !important;
        width: 100%;
    }
    
}

@media(max-width:480px) {
    .para{
        font-size: 1rem;
        width: 90%!important;
    }
    mat-panel-title h3{
        font-size: 1.5rem !important;
       // line-height: 1rem;
       // margin-top: 8px;
    }
    .brand-msg{
        font-size: 0.838rem;
    }
    .imgcsl{
        width: 100%;
    }
    .mat-checkbox-inner-container{
        margin: 23px 8px auto 0;
    }
}

@media (max-width:576px) {
    .pdf-info{
        display: block;
    }
    .pdf-info h5{
         color: var(--resourceHeaderColor) !important;
        content-visibility: hidden !important;
        visibility: hidden;
        padding-top: 10px;
        height: 10px;
    }
    .mat-checkbox-inner-container{
        margin: 23px 8px auto 0;
    }
}

@media (max-width:576px) {
    .container{
      max-width: calc(100vw - 10vw);
    }
    .para .brand-msg .contentbox .contentbox2{
        width: 100% !important;
    }
  }
  @media (max-width:480px) {
    .mat-expansion-panel{
        width: calc(100vw - 20vw);
    }
  }
  @media (max-width:576px) {
    .mat-expansion-panel{
        width: calc(100vw - 120px) !important;
    }
    .mat-expansion-panel-header{
        height: auto !important ;
        // margin-top: 6px;
       // height:auto !important;
    }
  }
  @media (max-width:768px) {
    .mat-expansion-panel{
        width: calc(100vw - 20vw);
    }
    .mat-checkbox-inner-container{
        margin: 23px 8px auto 0;
    }
  }
  @media (max-width:992px) {
    .mat-expansion-panel{
        width: calc(100vw - 20vw);
    }
    .mat-checkbox-inner-container{
        margin: 23px 8px auto 0;
    }
  }
  @media (max-width:1200px) {
    .mat-expansion-panel{
        width: calc(100vw - 20vw);
    }
  }

  .mat-expansion-indicator::after {
    height: 20px !important; 
    width: 20px !important; 
    font-size: larger !important;
}
.mat-expansion-indicator::after {
    transform: rotate(225deg) !important;  
}
.searhlink{
    margin-left:auto
}

@media (max-width:480px) {
    .mat-panel-title{
        font-size: 12px !important;
        line-height: normal !important;
    }
  }


  .headerpanel{
    width: 100% !important;
  }

  .top-con-background{
    background:  var(--gradientone);
    background: linear-gradient(60deg, var(--gradientone) 11%, var(--gradienttwo) 50%, var(--gradienthree) 100%); 
  }

 #headgradient{
    background: var(--headergradientColor);
}
#footgradient{
    background: var(--resourceprimarycolor);
}

.paraA{
    color: var(--resourceLinkColor)
}

.indications{
    color: var(--headerIndicationFontColor)
}

.indications h4{
    padding-top: 65px;
    margin-bottom: 10px;
    font-size: var(--headerIndicationHeaderFontSize);
}

.indications .indication-text{
    font-size: var(--headerIndicationCopyFontSize);
}

.indication-text p{
    margin-bottom: 0 !important;
}

.bottom-isi h4{
    color: var(--isiHeadersColors);
    font-size: var(--isiHeadersFontSize);
    padding-top: 40px;
    padding-bottom: 20px;
    z-index: 1;
}

.header-link{
    color: var(--headerIndicationFontColor)
}

.isi-text{
    font-size: var(--isiTextFontSize);
    font-weight: var(--isiTextFontWeight);
    line-height: var(--isiTextLineHeight);
}