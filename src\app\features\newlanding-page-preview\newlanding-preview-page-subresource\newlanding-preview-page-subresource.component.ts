import { Component, HostListener, OnInit } from '@angular/core';
import { documentToHtmlString } from '@contentful/rich-text-html-renderer';
import { BLOCKS, INLINES } from '@contentful/rich-text-types';
import { Observable } from 'rxjs';
import { ContentfulService } from 'src/app/services/contentful.service';

@Component({
  selector: 'app-newlanding-preview-page-subresource',
  templateUrl: './newlanding-preview-page-subresource.component.html',
  styleUrls: ['./newlanding-preview-page-subresource.component.scss']
})
export class NewlandingPreviewPageSubresourceComponent implements OnInit {


  responseData: any[] | undefined = [];
  subResourceContent = [];
  resourcespaceID: number;
  isi_id: any;
  resid: any;
  resid2: any;
  resourceprimarycolor: any;
  resourcetertiaryColor: any;
  resourcesecondarycolor: any;
  resourcefontColor: any;
  drugdata: any;
  versionone_med: any;
  qsaUrl: string;
  versionone_medname: any;
  result2: any;
  drug: any;
  landingpageid: any;
  resourcehorizontalRule: any;
  indication_header: any;
  isi_header: any;
  indication_text: any;
  isi_text: any;
  shortStickyHeader: any;
  shortStickyContentheading: string;
  shortStickyContenttext: string;
  shortStickyMargin: any;
  shortStickyPadding: any;
  shortStickyHeaderFontSize: any;
  shortStickyHeaderFontSizeMobile: any;
  shortStickyHeaderFontWeight: any;
  shortStickyHeaderFontColor: any;
  shortStickyContenttextAlignment: any;
  shortStickyContentfontSize: any;
  shortStickyContentlineHeight: any;
  shortStickyContentfontSizeMobile: any;
  shortStickyContentlineHeightMobile: any;
  shortStickyContentfontColor: any;
  shortStickyContenttextBlockPadding: any;
  shortStickyContenttextBlockMargin: any;
  shortStickyHeaderBackgroundColor: any;
  shortStickyContenttextAlignment1: any;
  shortStickyContentfontSize1: any;
  shortStickyContentlineHeight1: any;
  shortStickyContentfontSizeMobile1: any;
  shortStickyContentlineHeightMobile1: any;
  shortStickyContentfontColor1: any;
  shortStickyContenttextBlockPadding1: any;
  shortStickyContenttextBlockMargin1: any;
  shortStickyContentwidth1: any;
  shortStickyHeaderBackgroundPadding: any;



  constructor(private contentfulservice: ContentfulService) {

    let str = window.location.href;
    this.qsaUrl = str;
    let wordinurl = (this.qsaUrl.substring(this.qsaUrl.lastIndexOf('/') + 1));
    console.log(wordinurl)
    this.landingpageid = wordinurl;
    this.getshortstickIsiwithoutid(wordinurl);
    this.getColor();

    // this.contentfulservice.getVersiononeMedicines().subscribe(res => {
    //   this.drugdata = res;
    //   this.versionone_med = res.results.map(item => {
    //     return item.contentfulLandingPageId;
    //   });

    //   let str = window.location.href;
    //   this.qsaUrl = str;
    //   let urlmatch = (str.substring(str.lastIndexOf('/') + 1));
    //   this.versionone_medname = (filterItems(this.versionone_med, urlmatch))
    //   this.result2 = this.drugdata.results.find(item => item.contentfulLandingPageId === this.versionone_medname[0]);
    //   //this.drug = this.result2.name;
    //   this.resid = this.result2.contentfulResourceId;
    //   this.contentfulservice.resid_Visibility(this.resid)
    //   this.landingpageid = this.result2.contentfulLandingPageId;
    //   console.log(this.resid, this.landingpageid)


    //   this.getshortstickIsiwithoutid(this.landingpageid);
    //   this.getColor();
    // })

    // function filterItems(arr, query) {
    //   //return arr.filter((el) => el.toLowerCase().includes(query.toLowerCase()));
    //   return arr.filter(element => element.toLowerCase() === query.toLowerCase());
    // }

  }

  offsetFlag = true;
  mainText: Observable<[]>;
  heading: Observable<[]>;
  paragraph: Observable<[]>;
  unorderedList: Observable<[]>;
  listItem: Observable<[]>;
  listItem1: Observable<[]>;
  subhead: Observable<[]>;
  mainTexts
  shortStickyIsi: any;

  ngOnInit() { }



  scrollToElement(element: HTMLElement) {
    element.scrollIntoView({
      behavior: "smooth"
    });
  }

  private getElementById(id: string): HTMLElement {
    //console.log("element id : ", id);
    const element = document.getElementById(id);
    return element;
  }

  scrollToElementById(id: string) {
    const element = this.getElementById(id);
    this.scrollToElement(element);
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll($event) {
    const numb = window.scrollY;
    if (numb >= 1200) {
      this.offsetFlag = false;
    } else {
      this.offsetFlag = true;
    }
  }


  // getshortstickIsiwithoutid(value) {
  //   this.contentfulservice.getdatapreview(value).subscribe(res => {
  //     let a = res.fields.sponsoredHero.sys.id;
  //     this.contentfulservice.getdatapreview(a).subscribe(res => {
  //       let b = res.fields.stickyIsi.sys.id
  //       this.contentfulservice.getdatapreview(b).subscribe(res => {
  //         this.shortStickyIsi = res.fields.shortStickyIsi
  //       });
  //     });
  //   })
  // }
  getshortstickIsiwithoutid(value) {
    this.contentfulservice.getdatawithoutId(value).subscribe(res => {
      let a = res.fields.sponsoredHero.sys.id;
      this.contentfulservice.getdata(a).subscribe(res => {
        let b = res.fields.isiFooterAndSticky[0].sys.id
        this.contentfulservice.getdata(b).subscribe(res => {
          this.contentfulservice.getdatapreview(b).subscribe(res => {
            if (res.fields.hasOwnProperty("shortStickyHeader")) {
              this.shortStickyHeader = res.fields.shortStickyHeader;
            } else { console.log("shortStickyHeader not exists") }
            if (res.fields.hasOwnProperty("shortStickyContent")) {
              res.fields.shortStickyContent.forEach((block, index) => {

                if (index === 0) {
                  let shortStickyContent = res.fields.shortStickyContent[0].sys.id
                  this.contentfulservice.getdatapreview(shortStickyContent).subscribe(res => {
                    this.shortStickyContentheading = this._returnHtmlFromRichText(res.fields.text);

                    this.shortStickyContenttextAlignment = res.fields.textAlignment
                    document.documentElement.style.setProperty('--shortStickyContenttextAlignment', this.shortStickyContenttextAlignment);
                    this.shortStickyContentfontSize = res.fields.fontSize
                    document.documentElement.style.setProperty('--shortStickyContentfontSize', this.shortStickyContentfontSize);
                    this.shortStickyContentlineHeight = res.fields.lineHeight
                    document.documentElement.style.setProperty('--shortStickyContentlineHeight', this.shortStickyContentlineHeight);
                    this.shortStickyContentfontSizeMobile = res.fields.fontSizeMobile
                    document.documentElement.style.setProperty('--shortStickyContentfontSizeMobile', this.shortStickyContentfontSizeMobile);
                    this.shortStickyContentlineHeightMobile = res.fields.lineHeightMobile
                    document.documentElement.style.setProperty('--shortStickyContentlineHeightMobile', this.shortStickyContentlineHeightMobile);
                    this.shortStickyContentfontColor = res.fields.fontColor.value
                    document.documentElement.style.setProperty('--shortStickyContentfontColor', this.shortStickyContentfontColor);
                    this.shortStickyContenttextBlockPadding = res.fields.textBlockPadding
                    document.documentElement.style.setProperty('--shortStickyContenttextBlockPadding', this.shortStickyContenttextBlockPadding);  
                    this.shortStickyContenttextBlockMargin = res.fields.textBlockMargin
                    document.documentElement.style.setProperty('--shortStickyContenttextBlockMargin', this.shortStickyContenttextBlockMargin);
                  });
                } else if (index === 1) {

                  let shortStickyContent1 = res.fields.shortStickyContent[1].sys.id
                  this.contentfulservice.getdatapreview(shortStickyContent1).subscribe(res => {
                    this.shortStickyContenttext = this._returnHtmlFromRichText(res.fields.text);
                    

                    this.shortStickyContenttextAlignment1 = res.fields.textAlignment
                    document.documentElement.style.setProperty('--shortStickyContenttextAlignment1', this.shortStickyContenttextAlignment1);
                    this.shortStickyContentfontSize1 = res.fields.fontSize
                    document.documentElement.style.setProperty('--shortStickyContentfontSize1', this.shortStickyContentfontSize1);
                    this.shortStickyContentlineHeight1 = res.fields.lineHeight
                    document.documentElement.style.setProperty('--shortStickyContentlineHeight1', this.shortStickyContentlineHeight1);
                    this.shortStickyContentfontSizeMobile1 = res.fields.fontSizeMobile
                    document.documentElement.style.setProperty('--shortStickyContentfontSizeMobile1', this.shortStickyContentfontSizeMobile1);
                    this.shortStickyContentlineHeightMobile1 = res.fields.lineHeightMobile
                    document.documentElement.style.setProperty('--shortStickyContentlineHeightMobile1', this.shortStickyContentlineHeightMobile1);
                    this.shortStickyContentfontColor1 = res.fields.fontColor.value
                    document.documentElement.style.setProperty('--shortStickyContentfontColor1', this.shortStickyContentfontColor1);
                    this.shortStickyContenttextBlockPadding1 = res.fields.textBlockPadding
                    document.documentElement.style.setProperty('--shortStickyContenttextBlockPadding1', this.shortStickyContenttextBlockPadding1);  
                    this.shortStickyContenttextBlockMargin1 = res.fields.textBlockMargin
                    document.documentElement.style.setProperty('--shortStickyContenttextBlockMargin1', this.shortStickyContenttextBlockMargin1);  
                    this.shortStickyContentwidth1 = res.fields.width
                    document.documentElement.style.setProperty('--shortStickyContentwidth1', this.shortStickyContentwidth1);
                  });
                }
              });
            } else { console.log("shortStickyIsiImportantSafetyInformationText not exists") }


            this.shortStickyPadding = res.fields.shortStickyPadding
            document.documentElement.style.setProperty('--shortStickyPadding', this.shortStickyPadding);
            this.shortStickyHeaderFontSize = res.fields.shortStickyHeaderFontSize
            document.documentElement.style.setProperty('--shortStickyHeaderFontSize', this.shortStickyHeaderFontSize);
            this.shortStickyHeaderFontSizeMobile = res.fields.shortStickyHeaderFontSizeMobile
            document.documentElement.style.setProperty('--shortStickyHeaderFontSizeMobile', this.shortStickyHeaderFontSizeMobile);
            this.shortStickyHeaderFontWeight = res.fields.shortStickyHeaderFontWeight
            document.documentElement.style.setProperty('--shortStickyHeaderFontWeight', this.shortStickyHeaderFontWeight);
            this.shortStickyHeaderFontColor = res.fields.shortStickyHeaderFontColor.value
            document.documentElement.style.setProperty('--shortStickyHeaderFontColor', this.shortStickyHeaderFontColor);
            this.shortStickyHeaderBackgroundColor = res.fields.shortStickyHeaderBackgroundColor.value
            document.documentElement.style.setProperty('--shortStickyHeaderBackgroundColor', this.shortStickyHeaderBackgroundColor);
            this.shortStickyHeaderBackgroundPadding = res.fields.shortStickyHeaderBackgroundPadding
            document.documentElement.style.setProperty('--shortStickyHeaderBackgroundPadding', this.shortStickyHeaderBackgroundPadding);
          });
        });
      });
    })
  }
  textBlockMargin(arg0: string, textBlockMargin: any) {
    throw new Error('Method not implemented.');
  }

  public options: any = {
    renderNode: {
      [INLINES.HYPERLINK]: (node, next) => `<a href="${node.data.uri}" target="_blank" rel="noopener noreferrer">${next(node.content)}</a>`,
      [BLOCKS.PARAGRAPH]: (node, next) => `<p>${next(node.content).replace(/\n/g, '<br/>')}</p>`,
    }
  }


  _returnHtmlFromRichText(richText) {
    if (richText === undefined || richText === null || richText.nodeType !== 'document') {
      return '<p>Loading...</p>';
    }
    return documentToHtmlString(richText, this.options);
  }



  getColor() {

    //console.log("works!")
    this.contentfulservice.getdatapreview(this.landingpageid).subscribe(res => {
      let brandresources = res.fields.sponsoredHero.sys.id;
      this.contentfulservice.getdatapreview(brandresources).subscribe(res => {
        if (res.fields.hasOwnProperty("branding")) {
          let branding = res.fields.branding.sys.id;
          this.contentfulservice.getdatapreview(branding).subscribe(res => {
            if (res.fields.hasOwnProperty("primaryColor")) { this.resourceprimarycolor = res.fields.primaryColor.value; } else { console.log("resourceprimarycolor not exists") }
            if (res.fields.hasOwnProperty("tertiaryColor")) { this.resourcetertiaryColor = res.fields.tertiaryColor.value; } else { console.log("tertiaryColor not exists") }
            if (res.fields.hasOwnProperty("secondaryColor")) { this.resourcesecondarycolor = res.fields.secondaryColor.value; } else { console.log("resourcesecondarycolor not exists") }
            if (res.fields.hasOwnProperty("fontColor")) { this.resourcefontColor = res.fields.fontColor.value } else { console.log("resourcefontColor not exists") }
            if (res.fields.hasOwnProperty("horizontalRule")) { this.resourcehorizontalRule = res.fields.horizontalRule.value } else { console.log("horizontalRule not exists") }

            document.documentElement.style.setProperty('--resourceprimarycolor', this.resourceprimarycolor ? this.resourceprimarycolor : "#3254a2");
            document.documentElement.style.setProperty('--resourcesecondarycolor', this.resourcesecondarycolor ? this.resourcesecondarycolor : "#691c32");
            document.documentElement.style.setProperty('--resourcehorizontalRuleColor', this.resourcehorizontalRule ? this.resourcehorizontalRule : "#3254a2");
          })
        } else { console.log("branding not exists") }
      })
    })

  }
}
