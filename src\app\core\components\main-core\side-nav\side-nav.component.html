<mat-sidenav-container class="sidenav-container" [hasBackdrop]="false">
  <mat-sidenav-container class="sidenav-container" [hasBackdrop]="false">
    <mat-sidenav #drawer class="sidenav" fixedInViewport [@slideInOut]="menuState"
      [attr.role]="(isHandset$ | async) ? 'dialog' : 'navigation'" [mode]="(isHandset$ | async) ? 'over' : 'over'"
      [position]="nav_position" [(opened)]="opened" [opened]="!(isHandset$ | async)" id="sidenav">
      <div>
        <svg xmlns="http://www.w3.org/2000/svg" class="crossSvg" id="bc047f9a-3b44-4bc2-b832-aa662bb7263f" fill="#fff"
          data-name="Layer 1" width="24.7488" height="24.7487" viewBox="0 0 24.7488 24.7487"
          (click)="toggleMenuAndDrawer()">
          <polygon
            points="24.749 2.828 21.92 0 12.374 9.545 2.828 0 0 2.828 9.545 12.374 0 21.92 2.828 24.749 12.374 15.202 21.92 24.749 24.749 21.92 15.202 12.374 24.749 2.828">
          </polygon>
        </svg>
        <mat-toolbar class="sidenavMenuText">Menu</mat-toolbar>
        <mat-nav-list>
          <a mat-list-item href={{newLink}}>{{new}}</a>
          <a mat-list-item href={{aboutLink}}>{{about}}</a>
          <!-- <a mat-list-item [routerLink]="newLink">{{new}}</a>
          <a mat-list-item [routerLink]="aboutLink">{{about}}</a> -->
        </mat-nav-list>
      </div>
    </mat-sidenav>

    <mat-toolbar color="primary" class="margin">
      <section class="section">
        <div class=" container imgContent">
          <div class="imgContent1">
            <div class="imgCon">
              <div class="imgCon1">
                <div class="imgCont">
                  <div class="header-img col">
                    <a id="logimg">
                      <img src={{logo}} width="253px" id="logoimgalt">
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class=" container svgContent" id="element-to-hide">
            <div class="imgSvg">
              <div class="svgCon">
                <div class="svgCont">
                  <div class="col" id="sidenavButton">
                    <button type="button" class="sidenav-button" aria-label="Toggle sidenav" mat-icon-button
                      (click)="toggleMenuAndDrawer()">
                      <mat-icon class="toggleIcon" width="45%" aria-label="Side nav toggle icon">menu</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </mat-toolbar>
  </mat-sidenav-container>