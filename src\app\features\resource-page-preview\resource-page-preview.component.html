<div id="head-content">

</div>
<div class="container-fluid" id="top" *ngIf="allcontents">
  <div class="main-content container">
    <mat-accordion>
      <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false" expanded="true" hideToggle
        class="mat-expansion-panel-margin" style="width: 100%;" class="headerpanel">
        <mat-expansion-panel-header class="header-padding">
          <img src="../../../assets/images/arrow-up.svg" width="29px" *ngIf="!panelOpenState">
          <img src="../../../assets/images/arrow-down.svg" width="29px" *ngIf="panelOpenState">
          <mat-panel-title style="color: var(--fontColor);">
            <h3
              style="font-size: 1.875rem;font-weight: 500; font-family: var(--fontStyle);color: var(--resourceprimarycolor);">
              {{heading}}</h3>
            <i class="bi bi-arrow-down-circle-fill"></i>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <!-- <div style="font-size: 1.2em;" id="getStartedContentId" [innerHtml]="_returnHtmlFromRichText(getStartedContent)">
        </div> -->
        <table style="font-size: 1.2em;" class="header-margin">
          <tbody>
            <tr>
              <td class="copyicon">
                <img style="margin-top: 30px;" src={{linkIcon}} width="25px" alt="">
              </td>
              <td>
                <p class="para">
                  <a class="paraA" (click)="getTabUrl()">{{secondList}}</a>{{secondListValue}}<a
                    href={{secondListValue2Url}} data-bs-toggle="modal" data-bs-target="#exampleModal1"
                    data-bs-backdrop="true">{{secondListValue2}}.</a>
                </p>
              </td>
            </tr>
            <tr>
              <td>
                <img src={{copyIcon}} width="28px" alt="">
              </td>
              <td>
                <p class="para">
                  {{firstList}}<a target="_blank" href={{firstListUrl}}>{{firstListUrlValue}}</a>{{firstListValue}}
                  {{secondListValue2Sl}}<a href={{secondListValue2TlUrl}}
                    target="_blank">{{secondListValue2Tl}}</a>{{secondListValue2Fl}}
                </p>
              </td>
            </tr>
            <tr>
              <td>
                <img src={{searchIcon}} width="25px" alt="">
              </td>
              <td>
                <p class="para">
                  {{thirdList}}
                </p>
              </td>
            </tr>
            <tr>
              <td colspan="2">
                <p class="d-flex justify-content-end" style="font-size: 14px;">
                  {{ginacontent}}
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </mat-expansion-panel>
    </mat-accordion>

    <app-chat-bot #chatBot></app-chat-bot>
  </div>
  <div class="main-content-resource">
    <div class="main_content_padding container">
      <!--<div class="row top-content mt-3 mb-3 " id="headgradient">
          <div class="row HizentraLogo d-flex justify-content-center" style="text-align:center">
            <img src={{logo}} width="300px" height="150px" alt="Resource Logo" style="max-width: 350px;">
          </div>
          <div class="row">
            <h4 style="padding-top: 20px;">Indications</h4>
            <div class=""  style="font-size:14px"  [innerHtml]="_returnHtmlFromRichText(indication)"></div>
          </div>
          <div class="row">
            <div class="col-auto" *ngIf="button1name != null"><button mat-raised-button class="content-button" type="button" style="background-color:var(--btn1bgcolor);color:var(--btn1txtcolor)"><a href="{{button1link}}" target="_blank" style="color:var(--btn1txtcolor);
              text-decoration: none;">{{button1name}}</a></button></div>
            <div class="col-auto" *ngIf="button2name != null"><button  mat-raised-button class="content-button" type="button" style="background-color:var(--btn2bgcolor);color:var(--btn2txtcolor)"><a href="{{button2link}}" target="_blank" style="color:var(--btn2txtcolor);text-decoration: none;">{{button2name}}</a></button></div>
            
          </div>
        </div>-->
      <div class="row h-100  mt-3 mb-3" id="headgradient">
        <div class="row h-100">
          <div class="col-xl-4 col-lg-4 col-md-5 col-sm-auto col-auto">
            <div class="image-cont" style="padding-top: 35px;" *ngIf="logo">
              <img src={{logo}} width="300px" height="150px" alt="Resource Logo" style="max-width: 350px;">
            </div>
          </div>
          <div class="col-xl-8 col-lg-8 col-md-7 col-sm-auto col-auto my-auto">

            <div class="row float-right">
              <div class="col" *ngFor="let btn of relatedbuttonarray;let i = index"
                [style.backgroundColor]="btn?.fields?.customButtonBackgroundColor?.value">
                <button style="padding:2%;min-width:150px;border:0;background-color: transparent;">
                  <a href="{{btn.fields.buttonLink}}{{userId}}"
                    style="text-decoration: none;font-size:18px;font-weight:700"
                    [style.color]="btn?.fields?.customButtonTextColor?.value">
                    {{btn.fields.actionbuttonText}}
                  </a>
                </button>
              </div>
            </div>
            <div class="" *ngIf="indication">
            <div class="indications p-3" >
              <h4>{{indicationTitle}}</h4>
              <div class="indication-text" [innerHtml]="_returnHtmlFromRichText(indication)"></div>
            </div>
          </div>
          </div>
        </div>
        <div class="row mb-4">
          <!--<div class="pdf-info">
            <!--<div class=""  *ngFor="let groupResource of resourceData; let i = index">
              <a class="paraA" (click)="scrollToElementById(groupResource.name)">{{groupResource.name}}</a><span style="display:inline-block;margin-left:10px;margin-right:10px"><h5>|</h5></span>
            </div>-->
            <!--<div class="">
              <a class="paraA text-white" href={{prescribe?.content[8]?.content[1].data.uri}} target="_blank"
                rel="noopener">{{prescribe?.content[8]?.content[1]?.content[0].value}}</a><span
                style="display:inline-block;margin-left:10px;margin-right:10px"
                *ngIf="prescribe?.content[8]?.content[1]?.content[0].value">
                <h5>|</h5>
              </span>
            </div>
            <div class="text-white">
              <a class="paraA text-white" (click)="scrollToElementById('contentbox')" target="_blank"
                rel="noopener">{{shortstickyisi?.content[0].content[0].value}}</a><span
                style="display:inline-block;margin-left:10px;margin-right:10px">
                <h5 style="visibility:hidden">|</h5>
              </span>
            </div>
            <!--<a class="paraA"  (click)="scrollToElementById('contentbox')" >{{safetyInformation?.content[4]?.content[0].value}}</a>-->
          <!--</div>-->
          <div class="pdf-info">
            <!--<div class=""  *ngFor="let groupResource of resourceData; let i = index">
              <a class="" (click)="scrollToElementById(groupResource.name)">{{groupResource.name}}</a><span style="display:inline-block;margin-left:10px;margin-right:10px"><h5>|</h5></span>
            </div>-->
            <div class="" *ngIf="p_file">
              <a class=" header-link" href={{p_file}} target="_blank"
                rel="noopener">Prescribing Information</a><span
                style="display:inline-block;margin-left:10px;margin-right:10px"
                *ngIf="p_file">
                <h5 class="header-link">|</h5>
              </span>
            </div>
            <div>
              <a class=" header-link" (click)="scrollToElementById('contentbox')" target="_blank"
                rel="noopener">Important Safety Information</a><span
                style="display:inline-block;margin-left:10px;margin-right:10px">
                <h5 style="visibility:hidden">|</h5>
              </span>
            </div>
            <!--<a class="paraA"  (click)="scrollToElementById('contentbox')" >{{safetyInformation?.content[4]?.content[0].value}}</a>-->
          </div>
        </div>
      </div>
       
      <!-- image-banner -->
       <div class="row mockupimg"  *ngIf="dynamicMessageLayoutType === 'image banner'">  
        <img src={{imageBanner}} >  
      </div> 
      <div class=" row mockupimgmobile"  *ngIf="dynamicMessageLayoutType === 'image banner'"> 
        <img src={{imageBannerMobile}} > 
      </div>

      <!-- image+text banner -->
      <div class="row text-banner" *ngIf="dynamicMessageLayoutType === 'image + text'"  >
        <div class="text-banner-content" [ngStyle]="{ 'background-color': bannerbg , padding:'0'}" [style.width]="txtbannerwidth">
          <div class="text-banner-image"  [ngStyle]="{height:'100%'}" *ngIf="imageText" [ngClass]="imagePlacement">
            <img  [src]="imageText" alt="Banner Image">
          </div>
          <div class="text-banner-sec" [ngStyle]="{'padding':textimageTextPadding}">
           <div class="text-banner-heading" [innerHTML]="Textbanneronly" [ngStyle]="{ 'font-size': headingFontSize, 'color': headingFontColor, 'text-align': headingAlignment }"></div>
           <div class="text-banner-body" [innerHTML]="Textbanneronlyb" [ngStyle]="{ 'font-size': textFontSize ,'color': imgtextFontColor, 'text-align': textbanneronlyb }"></div>
           <div class="text-banner-references" [innerHTML]="Textbannerref" [ngStyle]="{ 'color': referencesFontColor, 'font-size': referencesFontSize, 'text-align': referencesAlignment }"></div>
        </div>
        </div>
      </div>
      
      <div class="text-mobile-banner" *ngIf="dynamicMessageLayoutType === 'image + text'">
        <div class="text-mobile-banner-content"  [ngStyle]="{ 'background-color': bannerbg }">
          <div class="text-mobile-banner-image" *ngIf="imageMobile" [ngStyle]="{height:'100%'}" [ngClass]="imageMobilePlacement">
            <img [src]="imageMobile"  alt="Mobile Banner Image">
          </div> 
          <div class="text-banner-Mobile-sec" [ngStyle]="{'padding':textimageMobileTextPadding}">
            <div class="text-mobile-banner-heading" [innerHTML]="Textmobilebanneronly" [ngStyle]="{ 'font-size': headingFontSizeMobile, 'color': headingFontColor, 'text-align': headingAlignmentMobile }"></div>
            <div class="text-mobile-banner-body" [innerHTML]="Textmobilebanneronlyb" [ngStyle]="{ 'font-size': textFontSizeMobile,'color': imgtextFontColor, 'text-align': textmobilebanneronlyb }"></div>
            <div class="text-mobile-banner-references" [innerHTML]="Textmobilebannerref" [ngStyle]="{ 'color': referencesFontColor, 'font-size': referencesFontSizeMobile, 'text-align': referencesAlignmentMobile }"></div>
        </div>
        </div>
      </div> 

      <!-- text banner -->
      <div class="row text-banner"  *ngIf="dynamicMessageLayoutType === 'text banner'">
        <div class="text-banner-content" [ngStyle]="{ 'background-color': bannerbg , 'padding':textBannerPadding} " [style.width]="txtbannerwidth"> 
          <div class="text-banner-heading" [innerHTML]="Textbanneronly" [ngStyle]="{ 'font-size': headingFontSize, 'color': headingFontColor, 'text-align': headingAlignment }"></div>
          <div class="text-banner-body" [innerHTML]="Textbanneronlyb" [ngStyle]="{ 'font-size': textFontSize ,'color': imgtextFontColor, 'text-align': textbanneronlyb }"></div>
          <div class="text-banner-references" [innerHTML]="Textbannerref" [ngStyle]="{ 'color': referencesFontColor, 'font-size': referencesFontSize, 'text-align': referencesAlignment }"></div>
        </div>
      </div>
      
      <div class="text-mobile-banner" *ngIf="dynamicMessageLayoutType === 'text banner'">
        <div class="text-mobile-banner-content"  [ngStyle]="{ 'background-color': bannerbg,'padding':textBannerPaddingMobile }"> 
          <div class="text-mobile-banner-heading" [innerHTML]="Textmobilebanneronly" [ngStyle]="{ 'font-size': headingFontSizeMobile, 'color': headingFontColor, 'text-align': headingAlignmentMobile }"></div>
          <div class="text-mobile-banner-body" [innerHTML]="Textmobilebanneronlyb" [ngStyle]="{ 'font-size': textFontSizeMobile,'color': imgtextFontColor, 'text-align': textmobilebanneronlyb }"></div>
          <div class="text-mobile-banner-references" [innerHTML]="Textmobilebannerref" [ngStyle]="{ 'color': referencesFontColor, 'font-size': referencesFontSizeMobile, 'text-align': referencesAlignmentMobile }"></div>
        </div>
      </div> 
<!--  --> 

      <!-- <div *ngIf="dynamicMsg != null">
        <button type="button" class="brand-msg">{{dynamicMsg}}</button>
      </div> -->
      <div class="row mt-3" id="demo">
        <div class="col-sm col-md-3 col-xl-2">
          <p><span class="myicon"><img src="../../../assets/images/checkbox outline.png" width="20px" height="20px"
                alt=""></span><span class="icon-label">Select Tool(s)</span></p>
        </div>
        <div class="col-sm col-md-3 col-xl-2">
          <p><span class="myicon"><img src="../../../assets/images/contents/ViewIcon.png" width="20px"
                alt=""></span><span class="icon-label">Preview Tool</span></p>
        </div>
        <div class="col-sm col-md-3 col-xl-2">
          <p><span class="myicon"><img src="../../../assets/images/downarrow.webp" style="rotate:90deg; width: 20px;"
                alt=""></span><span class="icon-label">Description</span></p>
        </div>
      </div>
      <div style="text-overflow: ellipsis;" *ngFor="let groupResource of resourceData; let i = index">

        <div class="contentbox">
          <mat-accordion id="{{groupResource.name}}">
            <p style="font-weight: bold;">{{groupResource.name | uppercase}}</p>
            <section>
              <mat-checkbox color="primary" #managing (change)='onResourceSelect(resource)'
                [(ngModel)]="resource.checked" class="example-margin example-section"
                *ngFor="let resource of groupResource.values;" style="display:block">
                <mat-expansion-panel id="checkbox-mat-panel">
                  <mat-expansion-panel-header style="height: auto !important;">
                    <mat-panel-title>
                      {{resource.fields.healthResourceTitle}} <span class="searhlink"> <a
                          href="{{resource.fields.healthResourceUrl}}" target="_blank" rel="noopener"><img
                            class="viewIconImg" src="../../../assets/images/contents/ViewIcon.png" alt=""></a></span>
                    </mat-panel-title>
                  </mat-expansion-panel-header>
                  <p class="para-text">{{resource.fields.teaser.content[0].content[0].value}}.</p>
                </mat-expansion-panel>
              </mat-checkbox>
            </section>
          </mat-accordion>
        </div>
        <div class="" *ngIf="paraContent != null">
          <div class="contentbox2" *ngIf="i == 0" id="paraContent">
            <div id="paraContentText" [innerHtml]="_returnHtmlFromRichText(paraContent)">
            </div>
          </div>
        </div>
      </div>




      <!--Loop End-->



      <div style="display: flex;justify-content: center;align-items: center;">
        <div class="row button-group">
          <div class="col-md mt-2" style="display: flex;justify-content: center;">
            <button type="button" (click)="generateEmailContent()" mat-raised-button class="content-button">
              <p>Email</p>
            </button>
            <button type="button" id='openbutton' mat-raised-button class="content-button" data-bs-toggle="modal"
              data-bs-target="#exampleModal" hidden>
              <p>Email</p>
            </button>
          </div>
          <div class="col-md mt-2" style="display: flex;justify-content: center;">
            <button class="content-button" (click)="viewPrintSelectedResourcs()" mat-raised-button color="primary">
              <p>View/Print</p>
            </button>
          </div>
          <div class="col-md mt-2" style="display: flex;justify-content: center;">
            <button class="content-button" (click)="copySelectedResourceLinks()" mat-raised-button color="primary">
              <p>Copy Link</p>
            </button>
          </div>
        </div>
      </div>



      <!-- Modal -->

      <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header d-flex row">
              <div class="col-10" style=" display:flex;justify-content:center ;">
                <h5 class="modal-title" id="exampleModalLabel">Fill out the information below to send an email <br>
                  containing this tool.</h5>
              </div>
              <div class="col-2">
                <button type="button" #closebutton class="btn-close" data-bs-dismiss="modal"
                  aria-label="Close"></button>
              </div>
            </div>
            <!-- <form #usersForm="ngForm" (ngSubmit)="getUserFormData(usersForm.value)"> -->
            <form [formGroup]="myemailForm" (ngSubmit)="onSubmit()">
              <div class="modal-body">
                <div class="col box">
                  <label for="validationCustom01" class="form-label">From</label>
                  <input type="email" formControlName="fromEmail" class="form-control" readonly id="fromEmail" required>
                </div>
                <div class="col box">
                  <label for="validationCustom01" class="form-label">To</label>
                  <input type="email" formControlName="toEmail" class="form-control" id="toEmail" required>
                </div>
                <div class="col box">
                  <label for="validationCustom01" class="form-label">Subject (not-editable)</label>
                  <input type="text" readonly formControlName="subject" class="form-control" id="subject" required>
                </div>

                <div id="dynamicMail" class="box" required>
                  <div class="mb-3 box">
                    <label for="validationTextarea" class="form-label">Body (not-editable)</label>
                    <div [innerHTML]="emailContent" class="disabled-input"></div>
                  </div>
                </div>
              </div>


              <div class="modal-footer">
                <div class="mail_button">
                  <button class="msg_button" type="submit" mat-raised-button color="primary">Send Message</button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Modal for email Form -->


      <div class="modal fade" id="exampleModal1" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="exampleModalLabel"> Provide Your Email Address</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form [formGroup]="provideForm" (ngSubmit)="provideEmail()">
              <div class="modal-body">
                <div class="mb-3">
                  <label for="recipient-toEmail" class="col-form-label">Email:</label>
                  <input type="email" class="form-control" formControlName="email" name="email" id="recipient-toEmail" />
                  <!-- <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
                        *Please Submit Your Email
                      </div> -->
                </div>
                <!--<div class="mb-3" hidden>
                      <label for="recipient-id" class="col-form-label">id</label>
                      <input type="text" ngModel={{this.userId}} class="form-control" formControlName="id" name="id"  id="recipient-id">
                    </div>-->
                <!--<div class="mb-3" hidden>
                  <label for="recipient-qsaUrl" class="col-form-label">qsaUrl</label>
                  <input type="text" ngModel={{qsaUrl}} class="form-control" formControlName="qsaUrl" name="qsaUrl"
                    id="recipient-qsaUrl">
                </div>
                <div class="mb-3" hidden>
                  <label for="recipient-qsaUrl" class="col-form-label">drug</label>
                  <input type="text" ngModel={{drug}} class="form-control" formControlName="drug" name="drug"
                    id="recipient-drug">
                </div>-->
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" [disabled]="!provideForm.valid" class="btn btn-primary"
                  data-bs-dismiss="modal">Submit</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="contentbox3 container-fluid" id="contentbox3">
        <div class="container bottom-isi" >
          <!--<div id="contentbox" style="line-height: 1.3rem;" 
                  [innerHtml]="_returnHtmlFromRichText(safetyInformation)">
            </div>-->
          <h4 style="text-transform:uppercase;font-weight:700" *ngIf="indication_text" id="contentbox">Indications</h4>
            <div class="isi-text" id="contentbox"   [innerHtml]="_returnHtmlFromRichText(indication_text)" *ngIf="indication"></div>
          <h4 style="text-transform:uppercase;font-weight:700" *ngIf="isi">Important Safety Information</h4>
          <div class="isi-text" id="contentbox"  [innerHtml]="_returnHtmlFromRichText(isi)" *ngIf="isi"></div>

          <div class="row">
            <div class="text-end">
              <span>
                <span><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                    class="bi bi-chevron-double-up" viewBox="0 0 16 16">
                    <path fill-rule="evenodd"
                      d="M7.646 2.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 3.707 2.354 9.354a.5.5 0 1 1-.708-.708l6-6z" />
                    <path fill-rule="evenodd"
                      d="M7.646 6.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 7.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z" />
                  </svg></span>
                <button (click)="scrollToElementById('top')" class="elementor-button-text scrolltotop">BACK TO
                  TOP</button>
              </span>
            </div>
          </div>
          <div class="row" id="footgradient" style="padding:2%">
            <div class="" style="padding-top: 35px;">
              <img src={{landFootImg}} title="LandingImage" alt="LandingImage" class="imgcsl" >
            </div>
            <div class="license text-white" *ngIf="copyRights">
              <div [innerHtml]="_returnHtmlFromRichText(copyRights)" style="color: white;" id="foot_links">
              </div>
            </div>
          </div>
          <div>
          </div>
        </div>
      </div>
      <app-subresource-page-preview #subResource ></app-subresource-page-preview>
    </div>
  </div>
</div>
  <div id="alertmssg" class="text-center" *ngIf="alertmssg">
    <h4 style="color:var(--primaryColor);padding-top:10%">No Matching Drug Found</h4>
  </div>