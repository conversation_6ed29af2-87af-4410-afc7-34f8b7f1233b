
.contain {
    border-top: 3px solid var(--resourcehorizontalRuleColor);
    //border-top-color:#691c32 ;
    background-color: #fff; 
    //font-weight: 600;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    flex-grow: 1;
    // display: flex;
    align-items: center;
    justify-content: center;
    margin-top:20px;
    margin-left: auto;
    margin-right: auto;
    //max-width: calc(100vw - 6vw);
    //z-index: 99999;
    width: 100%;
    transition: transform 0.3s ease-in-out; // Transition on hide/show.
    font-family: Roboto, "Helvetica Neue", sans-serif !important;
}

.containers{
    padding: 10px 0px 0px 0px;
    margin: 0;
}

.offset-1{
    margin-left: 3% !important;
    line-height: 1.3rem !important;
}

@media (min-width:1200px) {
    .contain{
        //width:1190px;
    }
    
}

div a:hover{
    color: var(--resourceLinkRolloverColor);
    font-weight: 600;
}

.info{
    text-decoration: none;
    color: #000;
    font-weight: 400;
    // display: flex;
    // justify-content: center;
    // height: 1.7rem;
}

@media (max-width:768px){
    .contain{
     //display: none;
     //transform: translateY(100%) !important;
    }
}

.bottom-isi h4{
    color: var(--isiHeadersColors);
    font-size: var(--isiHeadersFontSize);
    // padding-top: 10px;
    // padding-bottom: 10px;
    font-weight: 700;
}


.bottom-isi{
    padding: var(--shortStickyPadding); 
}

@media only screen and (max-width: 600px) {
    .shortStickyHeader{ 
        font-size: var(--shortStickyHeaderFontSizeMobile) !important;
    }

    .shortStickyContentheading{ 
        font-size: var(--shortStickyContentfontSizeMobile) !important;
        line-height: var(--shortStickyContentlineHeightMobile) !important;
    }
    .shortStickyContenttext{
        font-size: var(--shortStickyContentfontSizeMobile1) !important;
        line-height: var(--shortStickyContentlineHeightMobile1) !important;
    }
}
.shortStickyHeader{ 
    font-size: var(--shortStickyHeaderFontSize);
    font-weight: var(--shortStickyHeaderFontWeight);
    color: var(--shortStickyHeaderFontColor);
} 
.shortStickyContenttext{
    text-align: var(--shortStickyContenttextAlignment1);
    font-size: var(--shortStickyContentfontSize1);
    line-height: var(--shortStickyContentlineHeight1);
    color: var(--shortStickyContentfontColor1);
    padding: var(--shortStickyContenttextBlockPadding1);
    margin: var(--shortStickyContenttextBlockMargin1);
    width: var(--shortStickyContentwidth1);
}

::ng-deep {
    
    .shortStickyContentheading p{
        text-align: var(--shortStickyContenttextAlignment);
        font-size: var(--shortStickyContentfontSize);
        line-height: var(--shortStickyContentlineHeight);
        color: var(--shortStickyContentfontColor);
        padding: var(--shortStickyContenttextBlockPadding);
        margin: var(--shortStickyContenttextBlockMargin);
    }
}

.stickyheader{
    background-color: var(--shortStickyHeaderBackgroundColor);
    padding: var(--shortStickyHeaderBackgroundPadding);
}