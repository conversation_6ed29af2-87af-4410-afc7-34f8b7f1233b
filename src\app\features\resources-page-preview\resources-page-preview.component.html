<div id="head-content">

</div>
<div class="container-fluid" id="top" *ngIf="allcontents">
    <div class="top_heading_container-bg" *ngIf="heading || headingMobile || headinglist || textMobile ">
        <div class="main-content container top_heading_container">
            <div style="display: flex;justify-content: space-between;">
                <div class="heading" [innerHTML]="heading"></div>
                <div class="headingMobile" [innerHTML]="headingMobile" [ngStyle]="{ 'text-align':headingAlignment }">
                </div>
                <div *ngIf="arrowdown && hasValidContent(headinglist)" class="headinglist" (click)="toggleDropdown2()">
                    <div *ngIf="isDropdownOpen2" class="headinglist-icon">
                        <img class="dynamicicon" src={{arrowdown}} *ngIf="isDropdownOpen2">
                    </div>
                </div>
                <div *ngIf="arrowdown && hasValidContent(textMobile)" class="headingMobilelist"
                    (click)="toggleDropdown2()">
                    <div *ngIf="isDropdownOpen2" class="headinglist-icon">
                        <img class="dynamicicon" src={{arrowdown}} *ngIf="isDropdownOpen2">
                    </div>
                </div>
            </div>
            <div *ngIf="headinglist || textMobile" class="dropdown-content2" [class.show]="!isDropdownOpen2">
                <div style="display: flex;justify-content: space-between;">
                    <div *ngIf="hasValidContent(headinglist)" class="headinglist headinglist-content"
                        [innerHTML]="headinglist"></div>
                    <div *ngIf="hasValidContent(textMobile)" class="headingMobilelist headingMobilelist-content"
                        [innerHTML]="textMobile"></div>
                    <div *ngIf="arrowup" class="dropdown-header" (click)="toggleDropdown2()">
                        <div class="headinglist-icon" *ngIf="!isDropdownOpen2">
                            <img class="dynamicicon" src={{arrowup}} *ngIf="!isDropdownOpen2">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <app-chat-bot #chatBot></app-chat-bot>
    <div style="background-color: var(--brandbgcolor); padding: var(--columnBlockPadding);"
        *ngIf="brandheaderimageurl || brandheadertextContent || brandheadertextContent2 || branddropdownLinks || branddropdowntext">
        <div class="main-content container">
            <div class="columnBlockcontainer">
                <!-- <div style="width: var(--LeftcolumnWidth);font-size: var(--brandheaderfontsize); text-align: left;font-size: 19px;"> -->
                <div class="brandheaderleftcol">
                    <img class="brandheaderimageurl" src={{brandheaderimageurl}}>
                    <div class="brandheadertextContent" [innerHTML]="brandheadertextContent"></div>
                </div>
                <!-- <div style="width: var(--rightcolumnWidth); text-align: right;font-size: var(--brandheaderfontsize);"> -->
                <div class="brandheaderleftcol2">
                    <div class="d-flex" style="display: flex;justify-content: flex-end; align-items: center;">
                        <div class="brandheadertextContent2" [innerHTML]="brandheadertextContent2"></div>
                        <div class="dropdown-container">
                            <div (click)="toggleDropdown()">
                                <img class="brandheaderimageurl2" src={{brandheaderimageurl2}} *ngIf="!isDropdownOpen">
                                <img class="brandheaderimageurl2" src={{brandheaderimageurl2}} *ngIf="isDropdownOpen">
                            </div>
                            <div class="dropdown-content dropdownBackground" [class.show]="isDropdownOpen">
                                <a href="{{branddropdownLinks}}" target="_blank"
                                    style="text-decoration: none;">{{branddropdowntext}}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="logo">
        <div style="text-align: var(--logoimg);">
            <img class="ResourceLogo" src={{logo}} alt="Resource Logo">
        </div>
    </div>
    <div class="headerIsi"
        style="background-color: var(--headerIsibackgroundColor);">
        <!-- <div class="headerIsi" [innerHTML]="_returnHtmlFromRichText(indicationtextContent)"></div>  -->
  
            <div class="headerIsitext" [innerHTML]="p_file1" (click)="handlePFileClick($event)"></div>
            <div class="headerIsitextmobile" [innerHTML]="p_file1Mobile" (click)="handlePFileClick($event)"></div>
           
        <!-- <a class="headerIsitext" href={{indicationtextContent}} target="_blank" rel="noopener">{{indicationtextContenttext}}</a>
        <a class="headerIsitextmobile" href={{indicationtextContentMobile}} target="_blank" rel="noopener">{{indicationtextContenttexttextMobile}}</a>
        <span style="display:inline-block;margin-left:10px;margin-right:10px">
            <h5>|</h5>
        </span>
        <a class="headerIsitext" (click)="scrollToElementById('contentbox')" target="_blank" rel="noopener">{{indicationtextContenttext1}}</a>
        <a class="headerIsitextmobile" (click)="scrollToElementById('contentbox')" target="_blank" rel="noopener">{{indicationtextContenttext1textMobile}}</a> -->
    </div>

    <div class="main-content-resource">
        <div class="main_content_padding container">
            <div *ngIf="textBoxBelowHeaderIndication" class="textBoxBelowHeaderIndication">
                <div [innerHTML]="textBoxBelowHeaderIndication"></div>
            </div>
            <div *ngIf="textBoxBelowHeaderIndicationMobile" class="textBoxBelowHeaderIndicationMobile"> 
                <div [innerHTML]="textBoxBelowHeaderIndicationMobile"></div>
            </div>

            <div class="h-100 dynamicMsgsec"
                *ngIf="DynamicMessagecontent || DynamicMessagecontent1 || DynamicMessagecontent2 || DynamicMessagecontent3 || DynamicMessagecontent4">
                <div class="DynamicMescol">
                    <div style="width: 90%;">
                        <div class="DynamicMessagecontent" [innerHTML]="DynamicMessagecontent"></div>
                        <div class="DynamicMessagecontent1" [innerHTML]="DynamicMessagecontent1"></div>
                    </div>
                    <div *ngIf="openIcon" class="dropdown-header" style="width: 10%;">
                        <div class="iconBackgroundColor" *ngIf="!isDropdownOpen1">
                            <img class="DynamicMessageicon" src={{openIcon}} (click)="toggleDropdown1()">
                        </div>
                    </div>
                </div>
                <div class="dropdown-content1" style="border-radius: var(--borderRadiusRoundedCorners);"
                    [class.show]="isDropdownOpen1">
                    <div class="DynamicMescol">
                        <div style="width: 90%;">
                            <div class="DynamicMessagecontent2" [innerHTML]="DynamicMessagecontent2"></div>
                            <div class="DynamicMessagecontent3" [innerHTML]="DynamicMessagecontent3"></div>
                            <div style="display: flex;justify-content: center;">
                                <div class="DynamicMessagecontent4" [innerHTML]="DynamicMessagecontent4"></div>
                            </div>
                        </div>
                        <div *ngIf="closeIcon" class="dropdown-header" style="width: 10%;">
                            <div class="iconBackgroundColor" *ngIf="isDropdownOpen1">
                                <img class="DynamicMessageicon" src={{closeIcon}} (click)="toggleDropdown1()">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="textBoxAboveResourcecontent" class="textBoxAboveResourcecontent">
                <div [innerHTML]="textBoxAboveResourcecontent" (click)="handleBookmarkClick($event)"></div>
            </div>
            <div *ngIf="textBoxAboveResourcecontentMobile" class="textBoxAboveResourcecontentMobile">
                <div [innerHTML]="textBoxAboveResourcecontentMobile" (click)="handleBookmarkClick($event)"></div>
            </div>

            <div style="text-overflow: ellipsis;" *ngFor="let groupResource of resourceData; let i = index">
                <div class="contentbox">
                    <div id="{{groupResource.name}}">
                        <p class="healthToolsres">{{groupResource.name | uppercase}}</p>
                        <div *ngFor="let resource of groupResource.values;" class="healthToolsres-content">
                            <div style="cursor: pointer; display: flex;justify-content: space-between;">
                                <div class="healthToolsres healthResourceTitle">
                                    <div class="mat_checkbox" (mouseenter)="showPopUp = true"
                                        (mouseleave)="showPopUp = false">
                                        <div *ngIf="showPopUp" class="checkboxSelectPopUpContent"
                                            (click)="resource.checked = true; onResourceSelect(resource)">
                                            <div [innerHTML]="checkboxSelectPopUpContent"></div>
                                        </div>
                                        <mat-checkbox color="primary" [(ngModel)]="resource.checked"
                                            (change)="onResourceSelect(resource)">
                                        </mat-checkbox>
                                    </div>
                                    {{resource.fields.healthResourceTitle}}
                                </div>
                                <span class="searhlink">
                                    <div (click)="resource.open = !resource.open" class="expandIconsec"
                                        (mouseenter)="showPopUp = true" (mouseleave)="showPopUp = false">
                                        <div *ngIf="showPopUp" class="expandIconPopUpContent">
                                            <div [innerHTML]="expandIconPopUpContent"></div>
                                        </div>
                                        <img class="expandIcon" src={{expandIcon}} alt="">
                                    </div>
                                    <a class="viewIconImgsec" (mouseenter)="showPopUp = true"
                                        (mouseleave)="showPopUp = false" href="{{resource.fields.healthResourceUrl}}"
                                        target="_blank" rel="noopener">
                                        <div *ngIf="showPopUp" class="previewIconPopUpContent">
                                            <div [innerHTML]="previewIconPopUpContent"></div>
                                        </div>
                                        <img class="viewIconImg" src={{previewIcon}} alt="">
                                    </a>
                                </span>
                            </div>
                            <div *ngIf="resource.open">
                                <div class="resteaser" [innerHTML]="_returnHtmlFromRichText(resource.fields.teaser)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--Loop End-->

            <div class="textAndLinkResources" *ngIf="textAndLinkResources">
                <div [innerHTML]="textAndLinkResources"></div>
            </div>
            

            <div *ngIf="buttonsSectionBlockcecontent">
                <div class="buttonsSectionBlockcecontent" [innerHTML]="buttonsSectionBlockcecontent"></div>
            </div>

            <div class="buttonsSectionBlockcecontentMobile" *ngIf="buttonsSectionBlockcecontentMobile">
                <div [innerHTML]="buttonsSectionBlockcecontentMobile"></div>
            </div>


            <div style="display: flex;justify-content: center;align-items: center;">
                <div class="button-group">
                    <div class="col-md mt-2 Emailbutton "
                        style="display: flex;justify-content: center; flex-direction: column;text-align: center;align-items: center;">
                        <button type="button" (click)="generateEmailContent()" mat-raised-button class="Emailimage">
                            <img class="customButtonIcon" src={{Emailimage}}>
                        </button>
                        <div class="generateEmailContent" [innerHTML]="buttonemailtext"></div>
                        <button type="button" id='openbutton' class="Emailimage" data-bs-toggle="modal"
                            data-bs-target="#exampleModal" hidden>
                            <p>Email</p>
                        </button>
                    </div>
                    <div class="col-md mt-2 Printbutton"
                        style="display: flex;justify-content: center; flex-direction: column;text-align: center;align-items: center;">
                        <button class="Printimage" (click)="viewPrintSelectedResourcs()" mat-raised-button
                            color="primary">
                            <img class="customButtonIcon1" src={{Printimage}}>
                        </button>
                        <div class="viewPrintSelectedResourcs" [innerHTML]="buttonPrinttext"></div>
                    </div>
                    <div class="col-md mt-2 CopyLinkbutton"
                        style="display: flex;justify-content: center; flex-direction: column;text-align: center;align-items: center;">
                        <button class="CopyLinkimage" (click)="copySelectedResourceLinks()" mat-raised-button
                            color="primary">
                            <img class="customButtonIcon2" src={{CopyLinkimage}}>
                        </button>
                        <div class="copySelectedResourceLinks" [innerHTML]="buttonCopyLinktext"></div>
                    </div>
                </div>
            </div>



            <!-- Modal -->

            <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel"
                aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header d-flex row">
                            <div class="col-10" style=" display:flex;justify-content:center ;">
                                <h5 class="modal-title" id="exampleModalLabel">Fill out the information below to send an
                                    email <br>
                                    containing this tool.</h5>
                            </div>
                            <div class="col-2">
                                <button type="button" #closebutton class="btn-close" data-bs-dismiss="modal"
                                    aria-label="Close"></button>
                            </div>
                        </div>
                        <!-- <form #usersForm="ngForm" (ngSubmit)="getUserFormData(usersForm.value)"> -->
                        <form [formGroup]="myemailForm" (ngSubmit)="onSubmit()">
                            <div class="modal-body">
                                <div class="col box">
                                    <label for="validationCustom01" class="form-label">From</label>
                                    <input type="email" formControlName="fromEmail" class="form-control" readonly
                                        id="fromEmail" required>
                                </div>
                                <div class="col box">
                                    <label for="validationCustom01" class="form-label">To</label>
                                    <input type="email" formControlName="toEmail" class="form-control" id="toEmail"
                                        required>
                                </div>
                                <div class="col box">
                                    <label for="validationCustom01" class="form-label">Subject (not-editable)</label>
                                    <input type="text" readonly formControlName="subject" class="form-control"
                                        id="subject" required>
                                </div>

                                <div id="dynamicMail" class="box" required>
                                    <div class="mb-3 box">
                                        <label for="validationTextarea" class="form-label">Body (not-editable)</label>
                                        <div [innerHTML]="emailContent" class="disabled-input"></div>
                                    </div>
                                </div>
                            </div>


                            <div class="modal-footer">
                                <div class="mail_button">
                                    <button class="msg_button" type="submit" mat-raised-button color="primary">Send
                                        Message</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Modal for email Form -->


            <div class="modal fade" id="exampleModal1" tabindex="-1" aria-labelledby="exampleModalLabel"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel"> Provide Your Email Address</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form [formGroup]="provideForm" (ngSubmit)="provideEmail()">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="recipient-toEmail" class="col-form-label">Email:</label>
                                    <input type="email" class="form-control" formControlName="email" name="email"
                                        id="recipient-toEmail" />
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="submit" [disabled]="!provideForm.valid" class="btn btn-primary"
                                    data-bs-dismiss="modal">Submit</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="container" style="border-top:4px solid var(--resourcehorizontalRuleColor);"></div>
            <div class="contentbox3 container-fluid" id="contentbox3">
                <div class="container bottom-isi">
                    <div class="mainIsi">
                        <div class="mainIsiHeader">{{mainIsiHeader}}</div>
                        <div id="contentbox" class="mainIsiContentheading" [innerHtml]="mainIsiContentheading"></div>
                        <div id="contentbox" class="mainIsiContentheadingMobile" [innerHtml]="mainIsiContentheadingMobile"></div>
                        <div class="mainIsiContenttext" [innerHtml]="mainIsiContenttext"></div>
                        <div class="mainIsiContentheading1" [innerHtml]="mainIsiContentheading1"></div>
                        <div class="mainIsiContenttext1" [innerHtml]="mainIsiContenttext1"></div>
                        <div class="mainIsiContentheading2" [innerHtml]="mainIsiContentheading2"></div>
                        <div class="mainIsiContenttext2" [innerHtml]="mainIsiContenttext2"></div>
                    </div>

                    <div class="row">
                        <div class="text-end">
                            <span>
                                <span><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                        class="bi bi-chevron-double-up" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd"
                                            d="M7.646 2.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 3.707 2.354 9.354a.5.5 0 1 1-.708-.708l6-6z" />
                                        <path fill-rule="evenodd"
                                            d="M7.646 6.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 7.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z" />
                                    </svg></span>
                                <button (click)="scrollToElementById('top')"
                                    class="elementor-button-text scrolltotop">BACK TO
                                    TOP</button>
                            </span>
                        </div>
                    </div>
                    <div class="footerres-container">
                        <div class="footerres">
                            <div class="" *ngIf="logoimage">
                                <img src={{logoimage}} title="LandingImage" alt="LandingImage" class="imgcsl">
                            </div>
                            <div class="license" *ngIf="copyRights">
                                <div [innerHtml]="_returnHtmlFromRichText(copyRights)" id="foot_links"></div>
                            </div>
                        </div>
                    </div>
                    <div>
                    </div>
                </div>
            </div>
            <app-subresources-page-preview #subResource></app-subresources-page-preview>
        </div>
    </div>
</div>
<div id="alertmssg" class="text-center" *ngIf="alertmssg">
    <h4 style="color:var(--primaryColor);padding-top:10%">No Matching Drug Found</h4>
</div>